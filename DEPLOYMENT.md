# Deployment Guide for Map Augment

## Server Requirements

- **PHP 7.4+** with the following extensions:
  - PDO
  - SQLite3
  - JSON support (usually enabled by default)
- **Web Server**: nginx, Apache, or similar
- **File Permissions**: Write access for SQLite database creation

## Nginx Configuration

Since you mentioned nginx without URL rewrite rules, the current implementation works without requiring any special rewrite configuration. However, for cleaner URLs, you could optionally add:

```nginx
server {
    listen 80;
    server_name map.lvl3marketing.com;
    root /path/to/map-augment;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

## Deployment Steps

1. **Upload Files**: Copy all files to your web server directory
2. **Set Permissions**: Ensure the web server can write to the directory for SQLite database
   ```bash
   chmod 755 /path/to/map-augment
   chmod 644 /path/to/map-augment/*.php
   chmod 644 /path/to/map-augment/api/*.php
   ```
3. **Test PHP**: Verify PHP is working by accessing `index.php`
4. **Database Creation**: The SQLite database will be created automatically on first use

## File Permissions

The application needs write permissions to create and modify:
- `map_data.sqlite` (SQLite database file)
- Directory permissions for SQLite journal files

## Security Considerations

1. **Database Location**: Consider moving the SQLite database outside the web root
2. **Error Reporting**: Disable PHP error display in production
3. **HTTPS**: Use SSL/TLS for production deployment
4. **Backup**: Regular database backups recommended

## Testing the Deployment

1. **Basic Access**: Visit `https://map.lvl3marketing.com/index.php`
2. **User Creation**: Verify a 4-character user ID is generated and displayed
3. **Map Creation**: Create some map elements and verify they save
4. **Data Persistence**: Refresh the page and verify data loads
5. **URL Access**: Test `user.php?code=abcd` with your generated user ID
6. **API Endpoints**: Test the API endpoints directly if needed

## Troubleshooting

### Common Issues

1. **Database Errors**: Check file permissions and PHP SQLite extension
2. **API Not Working**: Verify the `/api/` directory and `index.php` file
3. **User ID Not Showing**: Check JavaScript console for errors
4. **Data Not Saving**: Check network tab for API call failures

### Debug Mode

To enable debugging, add this to the top of `index.php`:
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
```

## Backup Strategy

Regular backups of the SQLite database:
```bash
# Create backup
cp map_data.sqlite map_data_backup_$(date +%Y%m%d).sqlite

# Or use SQLite dump
sqlite3 map_data.sqlite .dump > backup.sql
```

## Performance Considerations

- SQLite is suitable for moderate traffic
- For high traffic, consider migrating to MySQL/PostgreSQL
- Enable gzip compression in nginx/Apache
- Consider CDN for static assets (CSS/JS)

## Migration from Original

The transformation is complete and maintains all original functionality:
- ✅ Grid editing works exactly as before
- ✅ Save/load functionality preserved (now server-side)
- ✅ All UI elements and interactions maintained
- ✅ Added user identification system
- ✅ Added URL-based access
- ✅ Improved code organization and readability
