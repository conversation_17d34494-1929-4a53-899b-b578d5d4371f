# Map Augment - PHP Web Application

A web-based map creation and editing application with server-side persistence using PHP and SQLite.

## Overview

This application has been converted from a client-side React application to a full-stack PHP web application with the following features:

- **User Identification**: Each user gets a unique 4-character lowercase ID (e.g., "abcd")
- **Server-side Persistence**: Map data is stored in SQLite database instead of localStorage
- **URL-based Access**: Users can access their maps via URLs with their user codes
- **Cookie Management**: Returning users are automatically recognized

## File Structure

```
map-augment/
├── index.php          # Main application (converted from index.html)
├── user.php           # User access page for URL-based access
├── database.php       # SQLite database class and schema
├── script.js          # Beautified JavaScript (modified for server API)
├── style.css          # Beautified CSS
├── api/
│   └── index.php      # API endpoints for save/load operations
├── assets/            # Empty (files moved to root)
└── README.md          # This documentation
```

## Features

### Phase 1: Code Cleanup ✅
- Renamed `assets/index-BS0NAyua.css` to `style.css`
- Renamed `assets/index-DqrlNgvr.js` to `script.js`
- Updated file references in HTML
- Beautified minified CSS and JavaScript files

### Phase 2: PHP Backend ✅
- Converted `index.html` to `index.php`
- Implemented SQLite database with `user_maps` table
- Added user identification system with 4-character IDs
- Created PHP API for save/load operations
- Modified JavaScript to use server endpoints instead of localStorage
- Added user ID display in the interface

### Phase 3: API and URL Access ✅
- Created `/api/` directory structure
- Implemented URL-based access system
- Created `user.php` for accessing maps via user codes
- Added proper error handling and validation

## Database Schema

```sql
CREATE TABLE user_maps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    map_data TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

### GET `/api/?action=load&user_id={user_id}`
Load map data for a specific user.

### POST `/api/?action=save`
Save map data for a user.
```json
{
    "mapData": {...},
    "user_id": "abcd"
}
```

### GET `/api/?action=check&user_id={user_id}`
Check if a user exists in the database.

## Usage

### For New Users
1. Visit `index.php`
2. A new 4-character user ID will be generated automatically
3. Create and edit your map
4. Data is automatically saved to the server

### For Returning Users
1. Visit `index.php` (if you have the cookie set)
2. Or visit `user.php?code=abcd` (replace 'abcd' with your user code)
3. Or use the user access form at `user.php`

### URL Access Examples
- `https://map.lvl3marketing.com/user.php?code=abcd`
- `https://map.lvl3marketing.com/index.php?user=abcd`

## Technical Requirements

- **PHP 7.4+** with PDO SQLite extension
- **Web server** (nginx, Apache, etc.)
- **SQLite** support

## Installation

1. Upload all files to your web server
2. Ensure PHP has write permissions for SQLite database creation
3. Access `index.php` in your browser
4. The SQLite database will be created automatically on first use

## Security Features

- User ID validation (4 lowercase letters only)
- SQL injection protection via prepared statements
- XSS protection via `htmlspecialchars()`
- CORS headers for API access

## Browser Compatibility

- Modern browsers with JavaScript enabled
- Supports all features of the original React application
- Progressive enhancement with server-side fallbacks
