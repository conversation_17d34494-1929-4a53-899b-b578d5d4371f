<?php
class MapDatabase {
    private $db;
    
    public function __construct($dbPath = 'map_data.sqlite') {
        try {
            $this->db = new PDO('sqlite:' . $dbPath);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->initializeDatabase();
        } catch (PDOException $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    private function initializeDatabase() {
        $sql = "
        CREATE TABLE IF NOT EXISTS user_maps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            map_data TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_user_id ON user_maps(user_id);
        ";
        
        $this->db->exec($sql);
    }
    
    public function saveMapData($userId, $mapData) {
        try {
            // Check if user already has data
            $stmt = $this->db->prepare("SELECT id FROM user_maps WHERE user_id = ?");
            $stmt->execute([$userId]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // Update existing data
                $stmt = $this->db->prepare("
                    UPDATE user_maps 
                    SET map_data = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE user_id = ?
                ");
                $stmt->execute([$mapData, $userId]);
            } else {
                // Insert new data
                $stmt = $this->db->prepare("
                    INSERT INTO user_maps (user_id, map_data) 
                    VALUES (?, ?)
                ");
                $stmt->execute([$userId, $mapData]);
            }
            
            return true;
        } catch (PDOException $e) {
            error_log('Database save error: ' . $e->getMessage());
            return false;
        }
    }
    
    public function loadMapData($userId) {
        try {
            $stmt = $this->db->prepare("SELECT map_data FROM user_maps WHERE user_id = ?");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result['map_data'] : null;
        } catch (PDOException $e) {
            error_log('Database load error: ' . $e->getMessage());
            return null;
        }
    }
    
    public function userExists($userId) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM user_maps WHERE user_id = ?");
            $stmt->execute([$userId]);
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log('Database check error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
