<?php
// User identification system
function generateUserId() {
    $chars = 'abcdefghijklmnopqrstuvwxyz';
    $userId = '';
    for ($i = 0; $i < 4; $i++) {
        $userId .= $chars[rand(0, 25)];
    }
    return $userId;
}

// Get or create user ID
$userId = '';
if (isset($_COOKIE['map_user_id'])) {
    $userId = $_COOKIE['map_user_id'];
} elseif (isset($_GET['user']) && preg_match('/^[a-z]{4}$/', $_GET['user'])) {
    $userId = $_GET['user'];
    setcookie('map_user_id', $userId, time() + (86400 * 365), '/'); // 1 year
} else {
    $userId = generateUserId();
    setcookie('map_user_id', $userId, time() + (86400 * 365), '/'); // 1 year
}
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <style data-vite-theme="" data-inject-first="">:root {
      --background: 240 10% 3.9%;
--foreground: 0 0% 98%;
--muted: 240 3.7% 15.9%;
--muted-foreground: 240 5% 64.9%;
--popover: 240 10% 3.9%;
--popover-foreground: 0 0% 98%;
--card: 240 10% 3.9%;
--card-foreground: 0 0% 98%;
--border: 240 3.7% 15.9%;
--input: 240 3.7% 15.9%;
--primary: 222 84% 5%;
--primary-foreground: 216 7% 97%;
--secondary: 240 3.7% 15.9%;
--secondary-foreground: 0 0% 98%;
--accent: 240 3.7% 15.9%;
--accent-foreground: 0 0% 98%;
--destructive: 0 62.8% 30.6%;
--destructive-foreground: 0 0% 98%;
--ring: 240 4.9% 83.9%;
--radius: 0.5rem;
  }
  .light {
      --background: 0 0% 100%;
--foreground: 20 14.3% 4.1%;
--muted: 60 4.8% 95.9%;
--muted-foreground: 25 5.3% 44.7%;
--popover: 0 0% 100%;
--popover-foreground: 20 14.3% 4.1%;
--card: 0 0% 100%;
--card-foreground: 20 14.3% 4.1%;
--border: 20 5.9% 90%;
--input: 20 5.9% 90%;
--primary: 222 84% 5%;
--primary-foreground: 216 7% 97%;
--secondary: 60 4.8% 95.9%;
--secondary-foreground: 24 9.8% 10%;
--accent: 60 4.8% 95.9%;
--accent-foreground: 24 9.8% 10%;
--destructive: 0 84.2% 60.2%;
--destructive-foreground: 60 9.1% 97.8%;
--ring: 20 14.3% 4.1%;
--radius: 0.5rem;
  }</style>

    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="script.js"></script>
    <link rel="stylesheet" crossorigin href="style.css">
  </head>
  <body>
    <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 8px 12px; border-radius: 4px; font-family: monospace; z-index: 9999;">
      User ID: <?php echo htmlspecialchars($userId); ?>
    </div>
    <div id="root">
      <div style="position: fixed; top: 50px; left: 20px; color: red; background: yellow; padding: 20px; font-family: monospace; z-index: 99999; border: 3px solid red;">
        <h2>🚨 DEBUG: React App Not Loading 🚨</h2>
        <p>If you see this, the React app failed to render.</p>
        <p>User ID: <?php echo htmlspecialchars($userId); ?></p>
        <p>Check browser console (F12) for JavaScript errors.</p>
        <p id="js-test">JavaScript test will appear here...</p>
      </div>
    </div>
    <script>
      // Make user ID available to JavaScript
      window.MAP_USER_ID = '<?php echo htmlspecialchars($userId); ?>';

      // Debug script to check if JavaScript is working
      console.log('User ID set:', window.MAP_USER_ID);
      console.log('DOM ready state:', document.readyState);

      // Check if root element exists
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded');
        const root = document.getElementById('root');
        console.log('Root element found:', !!root);
        if (root) {
          console.log('Root element HTML:', root.innerHTML);
        }
      });

      // Check for JavaScript errors
      window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        // Show error visually on page
        document.body.innerHTML += '<div style="position:fixed;top:100px;left:20px;background:red;color:white;padding:20px;z-index:99999;font-family:monospace;">JS ERROR: ' + e.error + '</div>';
      });

      // Test if basic JS is working
      setTimeout(function() {
        document.body.innerHTML += '<div style="position:fixed;top:150px;left:20px;background:green;color:white;padding:10px;z-index:99999;font-family:monospace;">✅ Basic JS Working</div>';
      }, 1000);
    </script>
  </body>
</html>