var au = (e) => {
  throw TypeError(e);
};
var ss = (e, t, n) => t.has(e) || au("Cannot " + n);
var P = (e, t, n) => (
    ss(e, t, "read from private field"),
    n ? n.call(e) : t.get(e)
  ),
  V = (e, t, n) =>
    t.has(e)
      ? au("Cannot add the same private member more than once")
      : t instanceof WeakSet
        ? t.add(e)
        : t.set(e, n),
  $ = (e, t, n, r) => (
    ss(e, t, "write to private field"),
    r ? r.call(e, n) : t.set(e, n),
    n
  ),
  ye = (e, t, n) => (ss(e, t, "access private method"), n);
var Co = (e, t, n, r) => ({
  set _(o) {
    $(e, t, o, n);
  },
  get _() {
    return P(e, t, r);
  },
});
function im(e, t) {
  for (var n = 0; n < t.length; n++) {
    const r = t[n];
    if (typeof r != "string" && !Array.isArray(r)) {
      for (const o in r)
        if (o !== "default" && !(o in e)) {
          const i = Object.getOwnPropertyDescriptor(r, o);
          i &&
            Object.defineProperty(
              e,
              o,
              i.get ? i : { enumerable: !0, get: () => r[o] },
            );
        }
    }
  }
  return Object.freeze(
    Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }),
  );
}
(function () {
  const t = document.createElement("link").relList;
  if (t && t.supports && t.supports("modulepreload")) return;
  for (const o of document.querySelectorAll('link[rel="modulepreload"]')) r(o);
  new MutationObserver((o) => {
    for (const i of o)
      if (i.type === "childList")
        for (const s of i.addedNodes)
          s.tagName === "LINK" && s.rel === "modulepreload" && r(s);
  }).observe(document, { childList: !0, subtree: !0 });
  function n(o) {
    const i = {};
    return (
      o.integrity && (i.integrity = o.integrity),
      o.referrerPolicy && (i.referrerPolicy = o.referrerPolicy),
      o.crossOrigin === "use-credentials"
        ? (i.credentials = "include")
        : o.crossOrigin === "anonymous"
          ? (i.credentials = "omit")
          : (i.credentials = "same-origin"),
      i
    );
  }
  function r(o) {
    if (o.ep) return;
    o.ep = !0;
    const i = n(o);
    fetch(o.href, i);
  }
})();
function nd(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default")
    ? e.default
    : e;
}
var rd = { exports: {} },
  Ai = {},
  od = { exports: {} },
  U = {};
/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var go = Symbol.for("react.element"),
  sm = Symbol.for("react.portal"),
  lm = Symbol.for("react.fragment"),
  am = Symbol.for("react.strict_mode"),
  um = Symbol.for("react.profiler"),
  cm = Symbol.for("react.provider"),
  dm = Symbol.for("react.context"),
  fm = Symbol.for("react.forward_ref"),
  pm = Symbol.for("react.suspense"),
  hm = Symbol.for("react.memo"),
  mm = Symbol.for("react.lazy"),
  uu = Symbol.iterator;
function vm(e) {
  return e === null || typeof e != "object"
    ? null
    : ((e = (uu && e[uu]) || e["@@iterator"]),
      typeof e == "function" ? e : null);
}
var id = {
    isMounted: function () {
      return !1;
    },
    enqueueForceUpdate: function () {},
    enqueueReplaceState: function () {},
    enqueueSetState: function () {},
  },
  sd = Object.assign,
  ld = {};
function xr(e, t, n) {
  ((this.props = e),
    (this.context = t),
    (this.refs = ld),
    (this.updater = n || id));
}
xr.prototype.isReactComponent = {};
xr.prototype.setState = function (e, t) {
  if (typeof e != "object" && typeof e != "function" && e != null)
    throw Error(
      "setState(...): takes an object of state variables to update or a function which returns an object of state variables.",
    );
  this.updater.enqueueSetState(this, e, t, "setState");
};
xr.prototype.forceUpdate = function (e) {
  this.updater.enqueueForceUpdate(this, e, "forceUpdate");
};
function ad() {}
ad.prototype = xr.prototype;
function Kl(e, t, n) {
  ((this.props = e),
    (this.context = t),
    (this.refs = ld),
    (this.updater = n || id));
}
var Gl = (Kl.prototype = new ad());
Gl.constructor = Kl;
sd(Gl, xr.prototype);
Gl.isPureReactComponent = !0;
var cu = Array.isArray,
  ud = Object.prototype.hasOwnProperty,
  Xl = { current: null },
  cd = { key: !0, ref: !0, __self: !0, __source: !0 };
function dd(e, t, n) {
  var r,
    o = {},
    i = null,
    s = null;
  if (t != null)
    for (r in (t.ref !== void 0 && (s = t.ref),
    t.key !== void 0 && (i = "" + t.key),
    t))
      ud.call(t, r) && !cd.hasOwnProperty(r) && (o[r] = t[r]);
  var l = arguments.length - 2;
  if (l === 1) o.children = n;
  else if (1 < l) {
    for (var a = Array(l), u = 0; u < l; u++) a[u] = arguments[u + 2];
    o.children = a;
  }
  if (e && e.defaultProps)
    for (r in ((l = e.defaultProps), l)) o[r] === void 0 && (o[r] = l[r]);
  return {
    $$typeof: go,
    type: e,
    key: i,
    ref: s,
    props: o,
    _owner: Xl.current,
  };
}
function gm(e, t) {
  return {
    $$typeof: go,
    type: e.type,
    key: t,
    ref: e.ref,
    props: e.props,
    _owner: e._owner,
  };
}
function Yl(e) {
  return typeof e == "object" && e !== null && e.$$typeof === go;
}
function ym(e) {
  var t = { "=": "=0", ":": "=2" };
  return (
    "$" +
    e.replace(/[=:]/g, function (n) {
      return t[n];
    })
  );
}
var du = /\/+/g;
function ls(e, t) {
  return typeof e == "object" && e !== null && e.key != null
    ? ym("" + e.key)
    : t.toString(36);
}
function Go(e, t, n, r, o) {
  var i = typeof e;
  (i === "undefined" || i === "boolean") && (e = null);
  var s = !1;
  if (e === null) s = !0;
  else
    switch (i) {
      case "string":
      case "number":
        s = !0;
        break;
      case "object":
        switch (e.$$typeof) {
          case go:
          case sm:
            s = !0;
        }
    }
  if (s)
    return (
      (s = e),
      (o = o(s)),
      (e = r === "" ? "." + ls(s, 0) : r),
      cu(o)
        ? ((n = ""),
          e != null && (n = e.replace(du, "$&/") + "/"),
          Go(o, t, n, "", function (u) {
            return u;
          }))
        : o != null &&
          (Yl(o) &&
            (o = gm(
              o,
              n +
                (!o.key || (s && s.key === o.key)
                  ? ""
                  : ("" + o.key).replace(du, "$&/") + "/") +
                e,
            )),
          t.push(o)),
      1
    );
  if (((s = 0), (r = r === "" ? "." : r + ":"), cu(e)))
    for (var l = 0; l < e.length; l++) {
      i = e[l];
      var a = r + ls(i, l);
      s += Go(i, t, n, a, o);
    }
  else if (((a = vm(e)), typeof a == "function"))
    for (e = a.call(e), l = 0; !(i = e.next()).done; )
      ((i = i.value), (a = r + ls(i, l++)), (s += Go(i, t, n, a, o)));
  else if (i === "object")
    throw (
      (t = String(e)),
      Error(
        "Objects are not valid as a React child (found: " +
          (t === "[object Object]"
            ? "object with keys {" + Object.keys(e).join(", ") + "}"
            : t) +
          "). If you meant to render a collection of children, use an array instead.",
      )
    );
  return s;
}
function ko(e, t, n) {
  if (e == null) return e;
  var r = [],
    o = 0;
  return (
    Go(e, r, "", "", function (i) {
      return t.call(n, i, o++);
    }),
    r
  );
}
function wm(e) {
  if (e._status === -1) {
    var t = e._result;
    ((t = t()),
      t.then(
        function (n) {
          (e._status === 0 || e._status === -1) &&
            ((e._status = 1), (e._result = n));
        },
        function (n) {
          (e._status === 0 || e._status === -1) &&
            ((e._status = 2), (e._result = n));
        },
      ),
      e._status === -1 && ((e._status = 0), (e._result = t)));
  }
  if (e._status === 1) return e._result.default;
  throw e._result;
}
var Me = { current: null },
  Xo = { transition: null },
  xm = {
    ReactCurrentDispatcher: Me,
    ReactCurrentBatchConfig: Xo,
    ReactCurrentOwner: Xl,
  };
function fd() {
  throw Error("act(...) is not supported in production builds of React.");
}
U.Children = {
  map: ko,
  forEach: function (e, t, n) {
    ko(
      e,
      function () {
        t.apply(this, arguments);
      },
      n,
    );
  },
  count: function (e) {
    var t = 0;
    return (
      ko(e, function () {
        t++;
      }),
      t
    );
  },
  toArray: function (e) {
    return (
      ko(e, function (t) {
        return t;
      }) || []
    );
  },
  only: function (e) {
    if (!Yl(e))
      throw Error(
        "React.Children.only expected to receive a single React element child.",
      );
    return e;
  },
};
U.Component = xr;
U.Fragment = lm;
U.Profiler = um;
U.PureComponent = Kl;
U.StrictMode = am;
U.Suspense = pm;
U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = xm;
U.act = fd;
U.cloneElement = function (e, t, n) {
  if (e == null)
    throw Error(
      "React.cloneElement(...): The argument must be a React element, but you passed " +
        e +
        ".",
    );
  var r = sd({}, e.props),
    o = e.key,
    i = e.ref,
    s = e._owner;
  if (t != null) {
    if (
      (t.ref !== void 0 && ((i = t.ref), (s = Xl.current)),
      t.key !== void 0 && (o = "" + t.key),
      e.type && e.type.defaultProps)
    )
      var l = e.type.defaultProps;
    for (a in t)
      ud.call(t, a) &&
        !cd.hasOwnProperty(a) &&
        (r[a] = t[a] === void 0 && l !== void 0 ? l[a] : t[a]);
  }
  var a = arguments.length - 2;
  if (a === 1) r.children = n;
  else if (1 < a) {
    l = Array(a);
    for (var u = 0; u < a; u++) l[u] = arguments[u + 2];
    r.children = l;
  }
  return { $$typeof: go, type: e.type, key: o, ref: i, props: r, _owner: s };
};
U.createContext = function (e) {
  return (
    (e = {
      $$typeof: dm,
      _currentValue: e,
      _currentValue2: e,
      _threadCount: 0,
      Provider: null,
      Consumer: null,
      _defaultValue: null,
      _globalName: null,
    }),
    (e.Provider = { $$typeof: cm, _context: e }),
    (e.Consumer = e)
  );
};
U.createElement = dd;
U.createFactory = function (e) {
  var t = dd.bind(null, e);
  return ((t.type = e), t);
};
U.createRef = function () {
  return { current: null };
};
U.forwardRef = function (e) {
  return { $$typeof: fm, render: e };
};
U.isValidElement = Yl;
U.lazy = function (e) {
  return { $$typeof: mm, _payload: { _status: -1, _result: e }, _init: wm };
};
U.memo = function (e, t) {
  return { $$typeof: hm, type: e, compare: t === void 0 ? null : t };
};
U.startTransition = function (e) {
  var t = Xo.transition;
  Xo.transition = {};
  try {
    e();
  } finally {
    Xo.transition = t;
  }
};
U.unstable_act = fd;
U.useCallback = function (e, t) {
  return Me.current.useCallback(e, t);
};
U.useContext = function (e) {
  return Me.current.useContext(e);
};
U.useDebugValue = function () {};
U.useDeferredValue = function (e) {
  return Me.current.useDeferredValue(e);
};
U.useEffect = function (e, t) {
  return Me.current.useEffect(e, t);
};
U.useId = function () {
  return Me.current.useId();
};
U.useImperativeHandle = function (e, t, n) {
  return Me.current.useImperativeHandle(e, t, n);
};
U.useInsertionEffect = function (e, t) {
  return Me.current.useInsertionEffect(e, t);
};
U.useLayoutEffect = function (e, t) {
  return Me.current.useLayoutEffect(e, t);
};
U.useMemo = function (e, t) {
  return Me.current.useMemo(e, t);
};
U.useReducer = function (e, t, n) {
  return Me.current.useReducer(e, t, n);
};
U.useRef = function (e) {
  return Me.current.useRef(e);
};
U.useState = function (e) {
  return Me.current.useState(e);
};
U.useSyncExternalStore = function (e, t, n) {
  return Me.current.useSyncExternalStore(e, t, n);
};
U.useTransition = function () {
  return Me.current.useTransition();
};
U.version = "18.3.1";
od.exports = U;
var h = od.exports;
const At = nd(h),
  pd = im({ __proto__: null, default: At }, [h]);
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var Sm = h,
  Em = Symbol.for("react.element"),
  Cm = Symbol.for("react.fragment"),
  km = Object.prototype.hasOwnProperty,
  Pm = Sm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,
  Nm = { key: !0, ref: !0, __self: !0, __source: !0 };
function hd(e, t, n) {
  var r,
    o = {},
    i = null,
    s = null;
  (n !== void 0 && (i = "" + n),
    t.key !== void 0 && (i = "" + t.key),
    t.ref !== void 0 && (s = t.ref));
  for (r in t) km.call(t, r) && !Nm.hasOwnProperty(r) && (o[r] = t[r]);
  if (e && e.defaultProps)
    for (r in ((t = e.defaultProps), t)) o[r] === void 0 && (o[r] = t[r]);
  return {
    $$typeof: Em,
    type: e,
    key: i,
    ref: s,
    props: o,
    _owner: Pm.current,
  };
}
Ai.Fragment = Cm;
Ai.jsx = hd;
Ai.jsxs = hd;
rd.exports = Ai;
var y = rd.exports,
  md = { exports: {} },
  Ve = {},
  vd = { exports: {} },
  gd = {};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ (function (e) {
  function t(M, j) {
    var A = M.length;
    M.push(j);
    e: for (; 0 < A; ) {
      var _ = (A - 1) >>> 1,
        I = M[_];
      if (0 < o(I, j)) ((M[_] = j), (M[A] = I), (A = _));
      else break e;
    }
  }
  function n(M) {
    return M.length === 0 ? null : M[0];
  }
  function r(M) {
    if (M.length === 0) return null;
    var j = M[0],
      A = M.pop();
    if (A !== j) {
      M[0] = A;
      e: for (var _ = 0, I = M.length, ee = I >>> 1; _ < ee; ) {
        var re = 2 * (_ + 1) - 1,
          St = M[re],
          de = re + 1,
          He = M[de];
        if (0 > o(St, A))
          de < I && 0 > o(He, St)
            ? ((M[_] = He), (M[de] = A), (_ = de))
            : ((M[_] = St), (M[re] = A), (_ = re));
        else if (de < I && 0 > o(He, A)) ((M[_] = He), (M[de] = A), (_ = de));
        else break e;
      }
    }
    return j;
  }
  function o(M, j) {
    var A = M.sortIndex - j.sortIndex;
    return A !== 0 ? A : M.id - j.id;
  }
  if (typeof performance == "object" && typeof performance.now == "function") {
    var i = performance;
    e.unstable_now = function () {
      return i.now();
    };
  } else {
    var s = Date,
      l = s.now();
    e.unstable_now = function () {
      return s.now() - l;
    };
  }
  var a = [],
    u = [],
    v = 1,
    f = null,
    m = 3,
    x = !1,
    E = !1,
    g = !1,
    w = typeof setTimeout == "function" ? setTimeout : null,
    d = typeof clearTimeout == "function" ? clearTimeout : null,
    c = typeof setImmediate < "u" ? setImmediate : null;
  typeof navigator < "u" &&
    navigator.scheduling !== void 0 &&
    navigator.scheduling.isInputPending !== void 0 &&
    navigator.scheduling.isInputPending.bind(navigator.scheduling);
  function p(M) {
    for (var j = n(u); j !== null; ) {
      if (j.callback === null) r(u);
      else if (j.startTime <= M)
        (r(u), (j.sortIndex = j.expirationTime), t(a, j));
      else break;
      j = n(u);
    }
  }
  function S(M) {
    if (((g = !1), p(M), !E))
      if (n(a) !== null) ((E = !0), le(C));
      else {
        var j = n(u);
        j !== null && je(S, j.startTime - M);
      }
  }
  function C(M, j) {
    ((E = !1), g && ((g = !1), d(T), (T = -1)), (x = !0));
    var A = m;
    try {
      for (
        p(j), f = n(a);
        f !== null && (!(f.expirationTime > j) || (M && !H()));

      ) {
        var _ = f.callback;
        if (typeof _ == "function") {
          ((f.callback = null), (m = f.priorityLevel));
          var I = _(f.expirationTime <= j);
          ((j = e.unstable_now()),
            typeof I == "function" ? (f.callback = I) : f === n(a) && r(a),
            p(j));
        } else r(a);
        f = n(a);
      }
      if (f !== null) var ee = !0;
      else {
        var re = n(u);
        (re !== null && je(S, re.startTime - j), (ee = !1));
      }
      return ee;
    } finally {
      ((f = null), (m = A), (x = !1));
    }
  }
  var N = !1,
    k = null,
    T = -1,
    b = 5,
    D = -1;
  function H() {
    return !(e.unstable_now() - D < b);
  }
  function z() {
    if (k !== null) {
      var M = e.unstable_now();
      D = M;
      var j = !0;
      try {
        j = k(!0, M);
      } finally {
        j ? Pe() : ((N = !1), (k = null));
      }
    } else N = !1;
  }
  var Pe;
  if (typeof c == "function")
    Pe = function () {
      c(z);
    };
  else if (typeof MessageChannel < "u") {
    var L = new MessageChannel(),
      ie = L.port2;
    ((L.port1.onmessage = z),
      (Pe = function () {
        ie.postMessage(null);
      }));
  } else
    Pe = function () {
      w(z, 0);
    };
  function le(M) {
    ((k = M), N || ((N = !0), Pe()));
  }
  function je(M, j) {
    T = w(function () {
      M(e.unstable_now());
    }, j);
  }
  ((e.unstable_IdlePriority = 5),
    (e.unstable_ImmediatePriority = 1),
    (e.unstable_LowPriority = 4),
    (e.unstable_NormalPriority = 3),
    (e.unstable_Profiling = null),
    (e.unstable_UserBlockingPriority = 2),
    (e.unstable_cancelCallback = function (M) {
      M.callback = null;
    }),
    (e.unstable_continueExecution = function () {
      E || x || ((E = !0), le(C));
    }),
    (e.unstable_forceFrameRate = function (M) {
      0 > M || 125 < M
        ? console.error(
            "forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported",
          )
        : (b = 0 < M ? Math.floor(1e3 / M) : 5);
    }),
    (e.unstable_getCurrentPriorityLevel = function () {
      return m;
    }),
    (e.unstable_getFirstCallbackNode = function () {
      return n(a);
    }),
    (e.unstable_next = function (M) {
      switch (m) {
        case 1:
        case 2:
        case 3:
          var j = 3;
          break;
        default:
          j = m;
      }
      var A = m;
      m = j;
      try {
        return M();
      } finally {
        m = A;
      }
    }),
    (e.unstable_pauseExecution = function () {}),
    (e.unstable_requestPaint = function () {}),
    (e.unstable_runWithPriority = function (M, j) {
      switch (M) {
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
          break;
        default:
          M = 3;
      }
      var A = m;
      m = M;
      try {
        return j();
      } finally {
        m = A;
      }
    }),
    (e.unstable_scheduleCallback = function (M, j, A) {
      var _ = e.unstable_now();
      switch (
        (typeof A == "object" && A !== null
          ? ((A = A.delay), (A = typeof A == "number" && 0 < A ? _ + A : _))
          : (A = _),
        M)
      ) {
        case 1:
          var I = -1;
          break;
        case 2:
          I = 250;
          break;
        case 5:
          I = 1073741823;
          break;
        case 4:
          I = 1e4;
          break;
        default:
          I = 5e3;
      }
      return (
        (I = A + I),
        (M = {
          id: v++,
          callback: j,
          priorityLevel: M,
          startTime: A,
          expirationTime: I,
          sortIndex: -1,
        }),
        A > _
          ? ((M.sortIndex = A),
            t(u, M),
            n(a) === null &&
              M === n(u) &&
              (g ? (d(T), (T = -1)) : (g = !0), je(S, A - _)))
          : ((M.sortIndex = I), t(a, M), E || x || ((E = !0), le(C))),
        M
      );
    }),
    (e.unstable_shouldYield = H),
    (e.unstable_wrapCallback = function (M) {
      var j = m;
      return function () {
        var A = m;
        m = j;
        try {
          return M.apply(this, arguments);
        } finally {
          m = A;
        }
      };
    }));
})(gd);
vd.exports = gd;
var Tm = vd.exports;
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var Rm = h,
  We = Tm;
function R(e) {
  for (
    var t = "https://reactjs.org/docs/error-decoder.html?invariant=" + e, n = 1;
    n < arguments.length;
    n++
  )
    t += "&args[]=" + encodeURIComponent(arguments[n]);
  return (
    "Minified React error #" +
    e +
    "; visit " +
    t +
    " for the full message or use the non-minified dev environment for full errors and additional helpful warnings."
  );
}
var yd = new Set(),
  Gr = {};
function _n(e, t) {
  (dr(e, t), dr(e + "Capture", t));
}
function dr(e, t) {
  for (Gr[e] = t, e = 0; e < t.length; e++) yd.add(t[e]);
}
var _t = !(
    typeof window > "u" ||
    typeof window.document > "u" ||
    typeof window.document.createElement > "u"
  ),
  Ks = Object.prototype.hasOwnProperty,
  _m =
    /^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,
  fu = {},
  pu = {};
function Mm(e) {
  return Ks.call(pu, e)
    ? !0
    : Ks.call(fu, e)
      ? !1
      : _m.test(e)
        ? (pu[e] = !0)
        : ((fu[e] = !0), !1);
}
function Om(e, t, n, r) {
  if (n !== null && n.type === 0) return !1;
  switch (typeof t) {
    case "function":
    case "symbol":
      return !0;
    case "boolean":
      return r
        ? !1
        : n !== null
          ? !n.acceptsBooleans
          : ((e = e.toLowerCase().slice(0, 5)), e !== "data-" && e !== "aria-");
    default:
      return !1;
  }
}
function jm(e, t, n, r) {
  if (t === null || typeof t > "u" || Om(e, t, n, r)) return !0;
  if (r) return !1;
  if (n !== null)
    switch (n.type) {
      case 3:
        return !t;
      case 4:
        return t === !1;
      case 5:
        return isNaN(t);
      case 6:
        return isNaN(t) || 1 > t;
    }
  return !1;
}
function Oe(e, t, n, r, o, i, s) {
  ((this.acceptsBooleans = t === 2 || t === 3 || t === 4),
    (this.attributeName = r),
    (this.attributeNamespace = o),
    (this.mustUseProperty = n),
    (this.propertyName = e),
    (this.type = t),
    (this.sanitizeURL = i),
    (this.removeEmptyString = s));
}
var ge = {};
"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style"
  .split(" ")
  .forEach(function (e) {
    ge[e] = new Oe(e, 0, !1, e, null, !1, !1);
  });
[
  ["acceptCharset", "accept-charset"],
  ["className", "class"],
  ["htmlFor", "for"],
  ["httpEquiv", "http-equiv"],
].forEach(function (e) {
  var t = e[0];
  ge[t] = new Oe(t, 1, !1, e[1], null, !1, !1);
});
["contentEditable", "draggable", "spellCheck", "value"].forEach(function (e) {
  ge[e] = new Oe(e, 2, !1, e.toLowerCase(), null, !1, !1);
});
[
  "autoReverse",
  "externalResourcesRequired",
  "focusable",
  "preserveAlpha",
].forEach(function (e) {
  ge[e] = new Oe(e, 2, !1, e, null, !1, !1);
});
"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope"
  .split(" ")
  .forEach(function (e) {
    ge[e] = new Oe(e, 3, !1, e.toLowerCase(), null, !1, !1);
  });
["checked", "multiple", "muted", "selected"].forEach(function (e) {
  ge[e] = new Oe(e, 3, !0, e, null, !1, !1);
});
["capture", "download"].forEach(function (e) {
  ge[e] = new Oe(e, 4, !1, e, null, !1, !1);
});
["cols", "rows", "size", "span"].forEach(function (e) {
  ge[e] = new Oe(e, 6, !1, e, null, !1, !1);
});
["rowSpan", "start"].forEach(function (e) {
  ge[e] = new Oe(e, 5, !1, e.toLowerCase(), null, !1, !1);
});
var ql = /[\-:]([a-z])/g;
function Zl(e) {
  return e[1].toUpperCase();
}
"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height"
  .split(" ")
  .forEach(function (e) {
    var t = e.replace(ql, Zl);
    ge[t] = new Oe(t, 1, !1, e, null, !1, !1);
  });
"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type"
  .split(" ")
  .forEach(function (e) {
    var t = e.replace(ql, Zl);
    ge[t] = new Oe(t, 1, !1, e, "http://www.w3.org/1999/xlink", !1, !1);
  });
["xml:base", "xml:lang", "xml:space"].forEach(function (e) {
  var t = e.replace(ql, Zl);
  ge[t] = new Oe(t, 1, !1, e, "http://www.w3.org/XML/1998/namespace", !1, !1);
});
["tabIndex", "crossOrigin"].forEach(function (e) {
  ge[e] = new Oe(e, 1, !1, e.toLowerCase(), null, !1, !1);
});
ge.xlinkHref = new Oe(
  "xlinkHref",
  1,
  !1,
  "xlink:href",
  "http://www.w3.org/1999/xlink",
  !0,
  !1,
);
["src", "href", "action", "formAction"].forEach(function (e) {
  ge[e] = new Oe(e, 1, !1, e.toLowerCase(), null, !0, !0);
});
function Jl(e, t, n, r) {
  var o = ge.hasOwnProperty(t) ? ge[t] : null;
  (o !== null
    ? o.type !== 0
    : r ||
      !(2 < t.length) ||
      (t[0] !== "o" && t[0] !== "O") ||
      (t[1] !== "n" && t[1] !== "N")) &&
    (jm(t, n, o, r) && (n = null),
    r || o === null
      ? Mm(t) && (n === null ? e.removeAttribute(t) : e.setAttribute(t, "" + n))
      : o.mustUseProperty
        ? (e[o.propertyName] = n === null ? (o.type === 3 ? !1 : "") : n)
        : ((t = o.attributeName),
          (r = o.attributeNamespace),
          n === null
            ? e.removeAttribute(t)
            : ((o = o.type),
              (n = o === 3 || (o === 4 && n === !0) ? "" : "" + n),
              r ? e.setAttributeNS(r, t, n) : e.setAttribute(t, n))));
}
var Dt = Rm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
  Po = Symbol.for("react.element"),
  Ln = Symbol.for("react.portal"),
  An = Symbol.for("react.fragment"),
  ea = Symbol.for("react.strict_mode"),
  Gs = Symbol.for("react.profiler"),
  wd = Symbol.for("react.provider"),
  xd = Symbol.for("react.context"),
  ta = Symbol.for("react.forward_ref"),
  Xs = Symbol.for("react.suspense"),
  Ys = Symbol.for("react.suspense_list"),
  na = Symbol.for("react.memo"),
  $t = Symbol.for("react.lazy"),
  Sd = Symbol.for("react.offscreen"),
  hu = Symbol.iterator;
function kr(e) {
  return e === null || typeof e != "object"
    ? null
    : ((e = (hu && e[hu]) || e["@@iterator"]),
      typeof e == "function" ? e : null);
}
var J = Object.assign,
  as;
function br(e) {
  if (as === void 0)
    try {
      throw Error();
    } catch (n) {
      var t = n.stack.trim().match(/\n( *(at )?)/);
      as = (t && t[1]) || "";
    }
  return (
    `
` +
    as +
    e
  );
}
var us = !1;
function cs(e, t) {
  if (!e || us) return "";
  us = !0;
  var n = Error.prepareStackTrace;
  Error.prepareStackTrace = void 0;
  try {
    if (t)
      if (
        ((t = function () {
          throw Error();
        }),
        Object.defineProperty(t.prototype, "props", {
          set: function () {
            throw Error();
          },
        }),
        typeof Reflect == "object" && Reflect.construct)
      ) {
        try {
          Reflect.construct(t, []);
        } catch (u) {
          var r = u;
        }
        Reflect.construct(e, [], t);
      } else {
        try {
          t.call();
        } catch (u) {
          r = u;
        }
        e.call(t.prototype);
      }
    else {
      try {
        throw Error();
      } catch (u) {
        r = u;
      }
      e();
    }
  } catch (u) {
    if (u && r && typeof u.stack == "string") {
      for (
        var o = u.stack.split(`
`),
          i = r.stack.split(`
`),
          s = o.length - 1,
          l = i.length - 1;
        1 <= s && 0 <= l && o[s] !== i[l];

      )
        l--;
      for (; 1 <= s && 0 <= l; s--, l--)
        if (o[s] !== i[l]) {
          if (s !== 1 || l !== 1)
            do
              if ((s--, l--, 0 > l || o[s] !== i[l])) {
                var a =
                  `
` + o[s].replace(" at new ", " at ");
                return (
                  e.displayName &&
                    a.includes("<anonymous>") &&
                    (a = a.replace("<anonymous>", e.displayName)),
                  a
                );
              }
            while (1 <= s && 0 <= l);
          break;
        }
    }
  } finally {
    ((us = !1), (Error.prepareStackTrace = n));
  }
  return (e = e ? e.displayName || e.name : "") ? br(e) : "";
}
function Dm(e) {
  switch (e.tag) {
    case 5:
      return br(e.type);
    case 16:
      return br("Lazy");
    case 13:
      return br("Suspense");
    case 19:
      return br("SuspenseList");
    case 0:
    case 2:
    case 15:
      return ((e = cs(e.type, !1)), e);
    case 11:
      return ((e = cs(e.type.render, !1)), e);
    case 1:
      return ((e = cs(e.type, !0)), e);
    default:
      return "";
  }
}
function qs(e) {
  if (e == null) return null;
  if (typeof e == "function") return e.displayName || e.name || null;
  if (typeof e == "string") return e;
  switch (e) {
    case An:
      return "Fragment";
    case Ln:
      return "Portal";
    case Gs:
      return "Profiler";
    case ea:
      return "StrictMode";
    case Xs:
      return "Suspense";
    case Ys:
      return "SuspenseList";
  }
  if (typeof e == "object")
    switch (e.$$typeof) {
      case xd:
        return (e.displayName || "Context") + ".Consumer";
      case wd:
        return (e._context.displayName || "Context") + ".Provider";
      case ta:
        var t = e.render;
        return (
          (e = e.displayName),
          e ||
            ((e = t.displayName || t.name || ""),
            (e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef")),
          e
        );
      case na:
        return (
          (t = e.displayName || null),
          t !== null ? t : qs(e.type) || "Memo"
        );
      case $t:
        ((t = e._payload), (e = e._init));
        try {
          return qs(e(t));
        } catch {}
    }
  return null;
}
function bm(e) {
  var t = e.type;
  switch (e.tag) {
    case 24:
      return "Cache";
    case 9:
      return (t.displayName || "Context") + ".Consumer";
    case 10:
      return (t._context.displayName || "Context") + ".Provider";
    case 18:
      return "DehydratedFragment";
    case 11:
      return (
        (e = t.render),
        (e = e.displayName || e.name || ""),
        t.displayName || (e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef")
      );
    case 7:
      return "Fragment";
    case 5:
      return t;
    case 4:
      return "Portal";
    case 3:
      return "Root";
    case 6:
      return "Text";
    case 16:
      return qs(t);
    case 8:
      return t === ea ? "StrictMode" : "Mode";
    case 22:
      return "Offscreen";
    case 12:
      return "Profiler";
    case 21:
      return "Scope";
    case 13:
      return "Suspense";
    case 19:
      return "SuspenseList";
    case 25:
      return "TracingMarker";
    case 1:
    case 0:
    case 17:
    case 2:
    case 14:
    case 15:
      if (typeof t == "function") return t.displayName || t.name || null;
      if (typeof t == "string") return t;
  }
  return null;
}
function sn(e) {
  switch (typeof e) {
    case "boolean":
    case "number":
    case "string":
    case "undefined":
      return e;
    case "object":
      return e;
    default:
      return "";
  }
}
function Ed(e) {
  var t = e.type;
  return (
    (e = e.nodeName) &&
    e.toLowerCase() === "input" &&
    (t === "checkbox" || t === "radio")
  );
}
function Im(e) {
  var t = Ed(e) ? "checked" : "value",
    n = Object.getOwnPropertyDescriptor(e.constructor.prototype, t),
    r = "" + e[t];
  if (
    !e.hasOwnProperty(t) &&
    typeof n < "u" &&
    typeof n.get == "function" &&
    typeof n.set == "function"
  ) {
    var o = n.get,
      i = n.set;
    return (
      Object.defineProperty(e, t, {
        configurable: !0,
        get: function () {
          return o.call(this);
        },
        set: function (s) {
          ((r = "" + s), i.call(this, s));
        },
      }),
      Object.defineProperty(e, t, { enumerable: n.enumerable }),
      {
        getValue: function () {
          return r;
        },
        setValue: function (s) {
          r = "" + s;
        },
        stopTracking: function () {
          ((e._valueTracker = null), delete e[t]);
        },
      }
    );
  }
}
function No(e) {
  e._valueTracker || (e._valueTracker = Im(e));
}
function Cd(e) {
  if (!e) return !1;
  var t = e._valueTracker;
  if (!t) return !0;
  var n = t.getValue(),
    r = "";
  return (
    e && (r = Ed(e) ? (e.checked ? "true" : "false") : e.value),
    (e = r),
    e !== n ? (t.setValue(e), !0) : !1
  );
}
function ci(e) {
  if (((e = e || (typeof document < "u" ? document : void 0)), typeof e > "u"))
    return null;
  try {
    return e.activeElement || e.body;
  } catch {
    return e.body;
  }
}
function Zs(e, t) {
  var n = t.checked;
  return J({}, t, {
    defaultChecked: void 0,
    defaultValue: void 0,
    value: void 0,
    checked: n ?? e._wrapperState.initialChecked,
  });
}
function mu(e, t) {
  var n = t.defaultValue == null ? "" : t.defaultValue,
    r = t.checked != null ? t.checked : t.defaultChecked;
  ((n = sn(t.value != null ? t.value : n)),
    (e._wrapperState = {
      initialChecked: r,
      initialValue: n,
      controlled:
        t.type === "checkbox" || t.type === "radio"
          ? t.checked != null
          : t.value != null,
    }));
}
function kd(e, t) {
  ((t = t.checked), t != null && Jl(e, "checked", t, !1));
}
function Js(e, t) {
  kd(e, t);
  var n = sn(t.value),
    r = t.type;
  if (n != null)
    r === "number"
      ? ((n === 0 && e.value === "") || e.value != n) && (e.value = "" + n)
      : e.value !== "" + n && (e.value = "" + n);
  else if (r === "submit" || r === "reset") {
    e.removeAttribute("value");
    return;
  }
  (t.hasOwnProperty("value")
    ? el(e, t.type, n)
    : t.hasOwnProperty("defaultValue") && el(e, t.type, sn(t.defaultValue)),
    t.checked == null &&
      t.defaultChecked != null &&
      (e.defaultChecked = !!t.defaultChecked));
}
function vu(e, t, n) {
  if (t.hasOwnProperty("value") || t.hasOwnProperty("defaultValue")) {
    var r = t.type;
    if (
      !(
        (r !== "submit" && r !== "reset") ||
        (t.value !== void 0 && t.value !== null)
      )
    )
      return;
    ((t = "" + e._wrapperState.initialValue),
      n || t === e.value || (e.value = t),
      (e.defaultValue = t));
  }
  ((n = e.name),
    n !== "" && (e.name = ""),
    (e.defaultChecked = !!e._wrapperState.initialChecked),
    n !== "" && (e.name = n));
}
function el(e, t, n) {
  (t !== "number" || ci(e.ownerDocument) !== e) &&
    (n == null
      ? (e.defaultValue = "" + e._wrapperState.initialValue)
      : e.defaultValue !== "" + n && (e.defaultValue = "" + n));
}
var Ir = Array.isArray;
function Gn(e, t, n, r) {
  if (((e = e.options), t)) {
    t = {};
    for (var o = 0; o < n.length; o++) t["$" + n[o]] = !0;
    for (n = 0; n < e.length; n++)
      ((o = t.hasOwnProperty("$" + e[n].value)),
        e[n].selected !== o && (e[n].selected = o),
        o && r && (e[n].defaultSelected = !0));
  } else {
    for (n = "" + sn(n), t = null, o = 0; o < e.length; o++) {
      if (e[o].value === n) {
        ((e[o].selected = !0), r && (e[o].defaultSelected = !0));
        return;
      }
      t !== null || e[o].disabled || (t = e[o]);
    }
    t !== null && (t.selected = !0);
  }
}
function tl(e, t) {
  if (t.dangerouslySetInnerHTML != null) throw Error(R(91));
  return J({}, t, {
    value: void 0,
    defaultValue: void 0,
    children: "" + e._wrapperState.initialValue,
  });
}
function gu(e, t) {
  var n = t.value;
  if (n == null) {
    if (((n = t.children), (t = t.defaultValue), n != null)) {
      if (t != null) throw Error(R(92));
      if (Ir(n)) {
        if (1 < n.length) throw Error(R(93));
        n = n[0];
      }
      t = n;
    }
    (t == null && (t = ""), (n = t));
  }
  e._wrapperState = { initialValue: sn(n) };
}
function Pd(e, t) {
  var n = sn(t.value),
    r = sn(t.defaultValue);
  (n != null &&
    ((n = "" + n),
    n !== e.value && (e.value = n),
    t.defaultValue == null && e.defaultValue !== n && (e.defaultValue = n)),
    r != null && (e.defaultValue = "" + r));
}
function yu(e) {
  var t = e.textContent;
  t === e._wrapperState.initialValue && t !== "" && t !== null && (e.value = t);
}
function Nd(e) {
  switch (e) {
    case "svg":
      return "http://www.w3.org/2000/svg";
    case "math":
      return "http://www.w3.org/1998/Math/MathML";
    default:
      return "http://www.w3.org/1999/xhtml";
  }
}
function nl(e, t) {
  return e == null || e === "http://www.w3.org/1999/xhtml"
    ? Nd(t)
    : e === "http://www.w3.org/2000/svg" && t === "foreignObject"
      ? "http://www.w3.org/1999/xhtml"
      : e;
}
var To,
  Td = (function (e) {
    return typeof MSApp < "u" && MSApp.execUnsafeLocalFunction
      ? function (t, n, r, o) {
          MSApp.execUnsafeLocalFunction(function () {
            return e(t, n, r, o);
          });
        }
      : e;
  })(function (e, t) {
    if (e.namespaceURI !== "http://www.w3.org/2000/svg" || "innerHTML" in e)
      e.innerHTML = t;
    else {
      for (
        To = To || document.createElement("div"),
          To.innerHTML = "<svg>" + t.valueOf().toString() + "</svg>",
          t = To.firstChild;
        e.firstChild;

      )
        e.removeChild(e.firstChild);
      for (; t.firstChild; ) e.appendChild(t.firstChild);
    }
  });
function Xr(e, t) {
  if (t) {
    var n = e.firstChild;
    if (n && n === e.lastChild && n.nodeType === 3) {
      n.nodeValue = t;
      return;
    }
  }
  e.textContent = t;
}
var Fr = {
    animationIterationCount: !0,
    aspectRatio: !0,
    borderImageOutset: !0,
    borderImageSlice: !0,
    borderImageWidth: !0,
    boxFlex: !0,
    boxFlexGroup: !0,
    boxOrdinalGroup: !0,
    columnCount: !0,
    columns: !0,
    flex: !0,
    flexGrow: !0,
    flexPositive: !0,
    flexShrink: !0,
    flexNegative: !0,
    flexOrder: !0,
    gridArea: !0,
    gridRow: !0,
    gridRowEnd: !0,
    gridRowSpan: !0,
    gridRowStart: !0,
    gridColumn: !0,
    gridColumnEnd: !0,
    gridColumnSpan: !0,
    gridColumnStart: !0,
    fontWeight: !0,
    lineClamp: !0,
    lineHeight: !0,
    opacity: !0,
    order: !0,
    orphans: !0,
    tabSize: !0,
    widows: !0,
    zIndex: !0,
    zoom: !0,
    fillOpacity: !0,
    floodOpacity: !0,
    stopOpacity: !0,
    strokeDasharray: !0,
    strokeDashoffset: !0,
    strokeMiterlimit: !0,
    strokeOpacity: !0,
    strokeWidth: !0,
  },
  Lm = ["Webkit", "ms", "Moz", "O"];
Object.keys(Fr).forEach(function (e) {
  Lm.forEach(function (t) {
    ((t = t + e.charAt(0).toUpperCase() + e.substring(1)), (Fr[t] = Fr[e]));
  });
});
function Rd(e, t, n) {
  return t == null || typeof t == "boolean" || t === ""
    ? ""
    : n || typeof t != "number" || t === 0 || (Fr.hasOwnProperty(e) && Fr[e])
      ? ("" + t).trim()
      : t + "px";
}
function _d(e, t) {
  e = e.style;
  for (var n in t)
    if (t.hasOwnProperty(n)) {
      var r = n.indexOf("--") === 0,
        o = Rd(n, t[n], r);
      (n === "float" && (n = "cssFloat"), r ? e.setProperty(n, o) : (e[n] = o));
    }
}
var Am = J(
  { menuitem: !0 },
  {
    area: !0,
    base: !0,
    br: !0,
    col: !0,
    embed: !0,
    hr: !0,
    img: !0,
    input: !0,
    keygen: !0,
    link: !0,
    meta: !0,
    param: !0,
    source: !0,
    track: !0,
    wbr: !0,
  },
);
function rl(e, t) {
  if (t) {
    if (Am[e] && (t.children != null || t.dangerouslySetInnerHTML != null))
      throw Error(R(137, e));
    if (t.dangerouslySetInnerHTML != null) {
      if (t.children != null) throw Error(R(60));
      if (
        typeof t.dangerouslySetInnerHTML != "object" ||
        !("__html" in t.dangerouslySetInnerHTML)
      )
        throw Error(R(61));
    }
    if (t.style != null && typeof t.style != "object") throw Error(R(62));
  }
}
function ol(e, t) {
  if (e.indexOf("-") === -1) return typeof t.is == "string";
  switch (e) {
    case "annotation-xml":
    case "color-profile":
    case "font-face":
    case "font-face-src":
    case "font-face-uri":
    case "font-face-format":
    case "font-face-name":
    case "missing-glyph":
      return !1;
    default:
      return !0;
  }
}
var il = null;
function ra(e) {
  return (
    (e = e.target || e.srcElement || window),
    e.correspondingUseElement && (e = e.correspondingUseElement),
    e.nodeType === 3 ? e.parentNode : e
  );
}
var sl = null,
  Xn = null,
  Yn = null;
function wu(e) {
  if ((e = xo(e))) {
    if (typeof sl != "function") throw Error(R(280));
    var t = e.stateNode;
    t && ((t = Wi(t)), sl(e.stateNode, e.type, t));
  }
}
function Md(e) {
  Xn ? (Yn ? Yn.push(e) : (Yn = [e])) : (Xn = e);
}
function Od() {
  if (Xn) {
    var e = Xn,
      t = Yn;
    if (((Yn = Xn = null), wu(e), t)) for (e = 0; e < t.length; e++) wu(t[e]);
  }
}
function jd(e, t) {
  return e(t);
}
function Dd() {}
var ds = !1;
function bd(e, t, n) {
  if (ds) return e(t, n);
  ds = !0;
  try {
    return jd(e, t, n);
  } finally {
    ((ds = !1), (Xn !== null || Yn !== null) && (Dd(), Od()));
  }
}
function Yr(e, t) {
  var n = e.stateNode;
  if (n === null) return null;
  var r = Wi(n);
  if (r === null) return null;
  n = r[t];
  e: switch (t) {
    case "onClick":
    case "onClickCapture":
    case "onDoubleClick":
    case "onDoubleClickCapture":
    case "onMouseDown":
    case "onMouseDownCapture":
    case "onMouseMove":
    case "onMouseMoveCapture":
    case "onMouseUp":
    case "onMouseUpCapture":
    case "onMouseEnter":
      ((r = !r.disabled) ||
        ((e = e.type),
        (r = !(
          e === "button" ||
          e === "input" ||
          e === "select" ||
          e === "textarea"
        ))),
        (e = !r));
      break e;
    default:
      e = !1;
  }
  if (e) return null;
  if (n && typeof n != "function") throw Error(R(231, t, typeof n));
  return n;
}
var ll = !1;
if (_t)
  try {
    var Pr = {};
    (Object.defineProperty(Pr, "passive", {
      get: function () {
        ll = !0;
      },
    }),
      window.addEventListener("test", Pr, Pr),
      window.removeEventListener("test", Pr, Pr));
  } catch {
    ll = !1;
  }
function Fm(e, t, n, r, o, i, s, l, a) {
  var u = Array.prototype.slice.call(arguments, 3);
  try {
    t.apply(n, u);
  } catch (v) {
    this.onError(v);
  }
}
var zr = !1,
  di = null,
  fi = !1,
  al = null,
  zm = {
    onError: function (e) {
      ((zr = !0), (di = e));
    },
  };
function $m(e, t, n, r, o, i, s, l, a) {
  ((zr = !1), (di = null), Fm.apply(zm, arguments));
}
function Um(e, t, n, r, o, i, s, l, a) {
  if (($m.apply(this, arguments), zr)) {
    if (zr) {
      var u = di;
      ((zr = !1), (di = null));
    } else throw Error(R(198));
    fi || ((fi = !0), (al = u));
  }
}
function Mn(e) {
  var t = e,
    n = e;
  if (e.alternate) for (; t.return; ) t = t.return;
  else {
    e = t;
    do ((t = e), t.flags & 4098 && (n = t.return), (e = t.return));
    while (e);
  }
  return t.tag === 3 ? n : null;
}
function Id(e) {
  if (e.tag === 13) {
    var t = e.memoizedState;
    if (
      (t === null && ((e = e.alternate), e !== null && (t = e.memoizedState)),
      t !== null)
    )
      return t.dehydrated;
  }
  return null;
}
function xu(e) {
  if (Mn(e) !== e) throw Error(R(188));
}
function Wm(e) {
  var t = e.alternate;
  if (!t) {
    if (((t = Mn(e)), t === null)) throw Error(R(188));
    return t !== e ? null : e;
  }
  for (var n = e, r = t; ; ) {
    var o = n.return;
    if (o === null) break;
    var i = o.alternate;
    if (i === null) {
      if (((r = o.return), r !== null)) {
        n = r;
        continue;
      }
      break;
    }
    if (o.child === i.child) {
      for (i = o.child; i; ) {
        if (i === n) return (xu(o), e);
        if (i === r) return (xu(o), t);
        i = i.sibling;
      }
      throw Error(R(188));
    }
    if (n.return !== r.return) ((n = o), (r = i));
    else {
      for (var s = !1, l = o.child; l; ) {
        if (l === n) {
          ((s = !0), (n = o), (r = i));
          break;
        }
        if (l === r) {
          ((s = !0), (r = o), (n = i));
          break;
        }
        l = l.sibling;
      }
      if (!s) {
        for (l = i.child; l; ) {
          if (l === n) {
            ((s = !0), (n = i), (r = o));
            break;
          }
          if (l === r) {
            ((s = !0), (r = i), (n = o));
            break;
          }
          l = l.sibling;
        }
        if (!s) throw Error(R(189));
      }
    }
    if (n.alternate !== r) throw Error(R(190));
  }
  if (n.tag !== 3) throw Error(R(188));
  return n.stateNode.current === n ? e : t;
}
function Ld(e) {
  return ((e = Wm(e)), e !== null ? Ad(e) : null);
}
function Ad(e) {
  if (e.tag === 5 || e.tag === 6) return e;
  for (e = e.child; e !== null; ) {
    var t = Ad(e);
    if (t !== null) return t;
    e = e.sibling;
  }
  return null;
}
var Fd = We.unstable_scheduleCallback,
  Su = We.unstable_cancelCallback,
  Vm = We.unstable_shouldYield,
  Bm = We.unstable_requestPaint,
  oe = We.unstable_now,
  Hm = We.unstable_getCurrentPriorityLevel,
  oa = We.unstable_ImmediatePriority,
  zd = We.unstable_UserBlockingPriority,
  pi = We.unstable_NormalPriority,
  Qm = We.unstable_LowPriority,
  $d = We.unstable_IdlePriority,
  Fi = null,
  wt = null;
function Km(e) {
  if (wt && typeof wt.onCommitFiberRoot == "function")
    try {
      wt.onCommitFiberRoot(Fi, e, void 0, (e.current.flags & 128) === 128);
    } catch {}
}
var st = Math.clz32 ? Math.clz32 : Ym,
  Gm = Math.log,
  Xm = Math.LN2;
function Ym(e) {
  return ((e >>>= 0), e === 0 ? 32 : (31 - ((Gm(e) / Xm) | 0)) | 0);
}
var Ro = 64,
  _o = 4194304;
function Lr(e) {
  switch (e & -e) {
    case 1:
      return 1;
    case 2:
      return 2;
    case 4:
      return 4;
    case 8:
      return 8;
    case 16:
      return 16;
    case 32:
      return 32;
    case 64:
    case 128:
    case 256:
    case 512:
    case 1024:
    case 2048:
    case 4096:
    case 8192:
    case 16384:
    case 32768:
    case 65536:
    case 131072:
    case 262144:
    case 524288:
    case 1048576:
    case 2097152:
      return e & 4194240;
    case 4194304:
    case 8388608:
    case 16777216:
    case 33554432:
    case 67108864:
      return e & 130023424;
    case 134217728:
      return 134217728;
    case 268435456:
      return 268435456;
    case 536870912:
      return 536870912;
    case 1073741824:
      return 1073741824;
    default:
      return e;
  }
}
function hi(e, t) {
  var n = e.pendingLanes;
  if (n === 0) return 0;
  var r = 0,
    o = e.suspendedLanes,
    i = e.pingedLanes,
    s = n & 268435455;
  if (s !== 0) {
    var l = s & ~o;
    l !== 0 ? (r = Lr(l)) : ((i &= s), i !== 0 && (r = Lr(i)));
  } else ((s = n & ~o), s !== 0 ? (r = Lr(s)) : i !== 0 && (r = Lr(i)));
  if (r === 0) return 0;
  if (
    t !== 0 &&
    t !== r &&
    !(t & o) &&
    ((o = r & -r), (i = t & -t), o >= i || (o === 16 && (i & 4194240) !== 0))
  )
    return t;
  if ((r & 4 && (r |= n & 16), (t = e.entangledLanes), t !== 0))
    for (e = e.entanglements, t &= r; 0 < t; )
      ((n = 31 - st(t)), (o = 1 << n), (r |= e[n]), (t &= ~o));
  return r;
}
function qm(e, t) {
  switch (e) {
    case 1:
    case 2:
    case 4:
      return t + 250;
    case 8:
    case 16:
    case 32:
    case 64:
    case 128:
    case 256:
    case 512:
    case 1024:
    case 2048:
    case 4096:
    case 8192:
    case 16384:
    case 32768:
    case 65536:
    case 131072:
    case 262144:
    case 524288:
    case 1048576:
    case 2097152:
      return t + 5e3;
    case 4194304:
    case 8388608:
    case 16777216:
    case 33554432:
    case 67108864:
      return -1;
    case 134217728:
    case 268435456:
    case 536870912:
    case 1073741824:
      return -1;
    default:
      return -1;
  }
}
function Zm(e, t) {
  for (
    var n = e.suspendedLanes,
      r = e.pingedLanes,
      o = e.expirationTimes,
      i = e.pendingLanes;
    0 < i;

  ) {
    var s = 31 - st(i),
      l = 1 << s,
      a = o[s];
    (a === -1
      ? (!(l & n) || l & r) && (o[s] = qm(l, t))
      : a <= t && (e.expiredLanes |= l),
      (i &= ~l));
  }
}
function ul(e) {
  return (
    (e = e.pendingLanes & -1073741825),
    e !== 0 ? e : e & 1073741824 ? 1073741824 : 0
  );
}
function Ud() {
  var e = Ro;
  return ((Ro <<= 1), !(Ro & 4194240) && (Ro = 64), e);
}
function fs(e) {
  for (var t = [], n = 0; 31 > n; n++) t.push(e);
  return t;
}
function yo(e, t, n) {
  ((e.pendingLanes |= t),
    t !== 536870912 && ((e.suspendedLanes = 0), (e.pingedLanes = 0)),
    (e = e.eventTimes),
    (t = 31 - st(t)),
    (e[t] = n));
}
function Jm(e, t) {
  var n = e.pendingLanes & ~t;
  ((e.pendingLanes = t),
    (e.suspendedLanes = 0),
    (e.pingedLanes = 0),
    (e.expiredLanes &= t),
    (e.mutableReadLanes &= t),
    (e.entangledLanes &= t),
    (t = e.entanglements));
  var r = e.eventTimes;
  for (e = e.expirationTimes; 0 < n; ) {
    var o = 31 - st(n),
      i = 1 << o;
    ((t[o] = 0), (r[o] = -1), (e[o] = -1), (n &= ~i));
  }
}
function ia(e, t) {
  var n = (e.entangledLanes |= t);
  for (e = e.entanglements; n; ) {
    var r = 31 - st(n),
      o = 1 << r;
    ((o & t) | (e[r] & t) && (e[r] |= t), (n &= ~o));
  }
}
var B = 0;
function Wd(e) {
  return (
    (e &= -e),
    1 < e ? (4 < e ? (e & 268435455 ? 16 : 536870912) : 4) : 1
  );
}
var Vd,
  sa,
  Bd,
  Hd,
  Qd,
  cl = !1,
  Mo = [],
  qt = null,
  Zt = null,
  Jt = null,
  qr = new Map(),
  Zr = new Map(),
  Wt = [],
  ev =
    "mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(
      " ",
    );
function Eu(e, t) {
  switch (e) {
    case "focusin":
    case "focusout":
      qt = null;
      break;
    case "dragenter":
    case "dragleave":
      Zt = null;
      break;
    case "mouseover":
    case "mouseout":
      Jt = null;
      break;
    case "pointerover":
    case "pointerout":
      qr.delete(t.pointerId);
      break;
    case "gotpointercapture":
    case "lostpointercapture":
      Zr.delete(t.pointerId);
  }
}
function Nr(e, t, n, r, o, i) {
  return e === null || e.nativeEvent !== i
    ? ((e = {
        blockedOn: t,
        domEventName: n,
        eventSystemFlags: r,
        nativeEvent: i,
        targetContainers: [o],
      }),
      t !== null && ((t = xo(t)), t !== null && sa(t)),
      e)
    : ((e.eventSystemFlags |= r),
      (t = e.targetContainers),
      o !== null && t.indexOf(o) === -1 && t.push(o),
      e);
}
function tv(e, t, n, r, o) {
  switch (t) {
    case "focusin":
      return ((qt = Nr(qt, e, t, n, r, o)), !0);
    case "dragenter":
      return ((Zt = Nr(Zt, e, t, n, r, o)), !0);
    case "mouseover":
      return ((Jt = Nr(Jt, e, t, n, r, o)), !0);
    case "pointerover":
      var i = o.pointerId;
      return (qr.set(i, Nr(qr.get(i) || null, e, t, n, r, o)), !0);
    case "gotpointercapture":
      return (
        (i = o.pointerId),
        Zr.set(i, Nr(Zr.get(i) || null, e, t, n, r, o)),
        !0
      );
  }
  return !1;
}
function Kd(e) {
  var t = hn(e.target);
  if (t !== null) {
    var n = Mn(t);
    if (n !== null) {
      if (((t = n.tag), t === 13)) {
        if (((t = Id(n)), t !== null)) {
          ((e.blockedOn = t),
            Qd(e.priority, function () {
              Bd(n);
            }));
          return;
        }
      } else if (t === 3 && n.stateNode.current.memoizedState.isDehydrated) {
        e.blockedOn = n.tag === 3 ? n.stateNode.containerInfo : null;
        return;
      }
    }
  }
  e.blockedOn = null;
}
function Yo(e) {
  if (e.blockedOn !== null) return !1;
  for (var t = e.targetContainers; 0 < t.length; ) {
    var n = dl(e.domEventName, e.eventSystemFlags, t[0], e.nativeEvent);
    if (n === null) {
      n = e.nativeEvent;
      var r = new n.constructor(n.type, n);
      ((il = r), n.target.dispatchEvent(r), (il = null));
    } else return ((t = xo(n)), t !== null && sa(t), (e.blockedOn = n), !1);
    t.shift();
  }
  return !0;
}
function Cu(e, t, n) {
  Yo(e) && n.delete(t);
}
function nv() {
  ((cl = !1),
    qt !== null && Yo(qt) && (qt = null),
    Zt !== null && Yo(Zt) && (Zt = null),
    Jt !== null && Yo(Jt) && (Jt = null),
    qr.forEach(Cu),
    Zr.forEach(Cu));
}
function Tr(e, t) {
  e.blockedOn === t &&
    ((e.blockedOn = null),
    cl ||
      ((cl = !0),
      We.unstable_scheduleCallback(We.unstable_NormalPriority, nv)));
}
function Jr(e) {
  function t(o) {
    return Tr(o, e);
  }
  if (0 < Mo.length) {
    Tr(Mo[0], e);
    for (var n = 1; n < Mo.length; n++) {
      var r = Mo[n];
      r.blockedOn === e && (r.blockedOn = null);
    }
  }
  for (
    qt !== null && Tr(qt, e),
      Zt !== null && Tr(Zt, e),
      Jt !== null && Tr(Jt, e),
      qr.forEach(t),
      Zr.forEach(t),
      n = 0;
    n < Wt.length;
    n++
  )
    ((r = Wt[n]), r.blockedOn === e && (r.blockedOn = null));
  for (; 0 < Wt.length && ((n = Wt[0]), n.blockedOn === null); )
    (Kd(n), n.blockedOn === null && Wt.shift());
}
var qn = Dt.ReactCurrentBatchConfig,
  mi = !0;
function rv(e, t, n, r) {
  var o = B,
    i = qn.transition;
  qn.transition = null;
  try {
    ((B = 1), la(e, t, n, r));
  } finally {
    ((B = o), (qn.transition = i));
  }
}
function ov(e, t, n, r) {
  var o = B,
    i = qn.transition;
  qn.transition = null;
  try {
    ((B = 4), la(e, t, n, r));
  } finally {
    ((B = o), (qn.transition = i));
  }
}
function la(e, t, n, r) {
  if (mi) {
    var o = dl(e, t, n, r);
    if (o === null) (Es(e, t, r, vi, n), Eu(e, r));
    else if (tv(o, e, t, n, r)) r.stopPropagation();
    else if ((Eu(e, r), t & 4 && -1 < ev.indexOf(e))) {
      for (; o !== null; ) {
        var i = xo(o);
        if (
          (i !== null && Vd(i),
          (i = dl(e, t, n, r)),
          i === null && Es(e, t, r, vi, n),
          i === o)
        )
          break;
        o = i;
      }
      o !== null && r.stopPropagation();
    } else Es(e, t, r, null, n);
  }
}
var vi = null;
function dl(e, t, n, r) {
  if (((vi = null), (e = ra(r)), (e = hn(e)), e !== null))
    if (((t = Mn(e)), t === null)) e = null;
    else if (((n = t.tag), n === 13)) {
      if (((e = Id(t)), e !== null)) return e;
      e = null;
    } else if (n === 3) {
      if (t.stateNode.current.memoizedState.isDehydrated)
        return t.tag === 3 ? t.stateNode.containerInfo : null;
      e = null;
    } else t !== e && (e = null);
  return ((vi = e), null);
}
function Gd(e) {
  switch (e) {
    case "cancel":
    case "click":
    case "close":
    case "contextmenu":
    case "copy":
    case "cut":
    case "auxclick":
    case "dblclick":
    case "dragend":
    case "dragstart":
    case "drop":
    case "focusin":
    case "focusout":
    case "input":
    case "invalid":
    case "keydown":
    case "keypress":
    case "keyup":
    case "mousedown":
    case "mouseup":
    case "paste":
    case "pause":
    case "play":
    case "pointercancel":
    case "pointerdown":
    case "pointerup":
    case "ratechange":
    case "reset":
    case "resize":
    case "seeked":
    case "submit":
    case "touchcancel":
    case "touchend":
    case "touchstart":
    case "volumechange":
    case "change":
    case "selectionchange":
    case "textInput":
    case "compositionstart":
    case "compositionend":
    case "compositionupdate":
    case "beforeblur":
    case "afterblur":
    case "beforeinput":
    case "blur":
    case "fullscreenchange":
    case "focus":
    case "hashchange":
    case "popstate":
    case "select":
    case "selectstart":
      return 1;
    case "drag":
    case "dragenter":
    case "dragexit":
    case "dragleave":
    case "dragover":
    case "mousemove":
    case "mouseout":
    case "mouseover":
    case "pointermove":
    case "pointerout":
    case "pointerover":
    case "scroll":
    case "toggle":
    case "touchmove":
    case "wheel":
    case "mouseenter":
    case "mouseleave":
    case "pointerenter":
    case "pointerleave":
      return 4;
    case "message":
      switch (Hm()) {
        case oa:
          return 1;
        case zd:
          return 4;
        case pi:
        case Qm:
          return 16;
        case $d:
          return 536870912;
        default:
          return 16;
      }
    default:
      return 16;
  }
}
var Xt = null,
  aa = null,
  qo = null;
function Xd() {
  if (qo) return qo;
  var e,
    t = aa,
    n = t.length,
    r,
    o = "value" in Xt ? Xt.value : Xt.textContent,
    i = o.length;
  for (e = 0; e < n && t[e] === o[e]; e++);
  var s = n - e;
  for (r = 1; r <= s && t[n - r] === o[i - r]; r++);
  return (qo = o.slice(e, 1 < r ? 1 - r : void 0));
}
function Zo(e) {
  var t = e.keyCode;
  return (
    "charCode" in e
      ? ((e = e.charCode), e === 0 && t === 13 && (e = 13))
      : (e = t),
    e === 10 && (e = 13),
    32 <= e || e === 13 ? e : 0
  );
}
function Oo() {
  return !0;
}
function ku() {
  return !1;
}
function Be(e) {
  function t(n, r, o, i, s) {
    ((this._reactName = n),
      (this._targetInst = o),
      (this.type = r),
      (this.nativeEvent = i),
      (this.target = s),
      (this.currentTarget = null));
    for (var l in e)
      e.hasOwnProperty(l) && ((n = e[l]), (this[l] = n ? n(i) : i[l]));
    return (
      (this.isDefaultPrevented = (
        i.defaultPrevented != null ? i.defaultPrevented : i.returnValue === !1
      )
        ? Oo
        : ku),
      (this.isPropagationStopped = ku),
      this
    );
  }
  return (
    J(t.prototype, {
      preventDefault: function () {
        this.defaultPrevented = !0;
        var n = this.nativeEvent;
        n &&
          (n.preventDefault
            ? n.preventDefault()
            : typeof n.returnValue != "unknown" && (n.returnValue = !1),
          (this.isDefaultPrevented = Oo));
      },
      stopPropagation: function () {
        var n = this.nativeEvent;
        n &&
          (n.stopPropagation
            ? n.stopPropagation()
            : typeof n.cancelBubble != "unknown" && (n.cancelBubble = !0),
          (this.isPropagationStopped = Oo));
      },
      persist: function () {},
      isPersistent: Oo,
    }),
    t
  );
}
var Sr = {
    eventPhase: 0,
    bubbles: 0,
    cancelable: 0,
    timeStamp: function (e) {
      return e.timeStamp || Date.now();
    },
    defaultPrevented: 0,
    isTrusted: 0,
  },
  ua = Be(Sr),
  wo = J({}, Sr, { view: 0, detail: 0 }),
  iv = Be(wo),
  ps,
  hs,
  Rr,
  zi = J({}, wo, {
    screenX: 0,
    screenY: 0,
    clientX: 0,
    clientY: 0,
    pageX: 0,
    pageY: 0,
    ctrlKey: 0,
    shiftKey: 0,
    altKey: 0,
    metaKey: 0,
    getModifierState: ca,
    button: 0,
    buttons: 0,
    relatedTarget: function (e) {
      return e.relatedTarget === void 0
        ? e.fromElement === e.srcElement
          ? e.toElement
          : e.fromElement
        : e.relatedTarget;
    },
    movementX: function (e) {
      return "movementX" in e
        ? e.movementX
        : (e !== Rr &&
            (Rr && e.type === "mousemove"
              ? ((ps = e.screenX - Rr.screenX), (hs = e.screenY - Rr.screenY))
              : (hs = ps = 0),
            (Rr = e)),
          ps);
    },
    movementY: function (e) {
      return "movementY" in e ? e.movementY : hs;
    },
  }),
  Pu = Be(zi),
  sv = J({}, zi, { dataTransfer: 0 }),
  lv = Be(sv),
  av = J({}, wo, { relatedTarget: 0 }),
  ms = Be(av),
  uv = J({}, Sr, { animationName: 0, elapsedTime: 0, pseudoElement: 0 }),
  cv = Be(uv),
  dv = J({}, Sr, {
    clipboardData: function (e) {
      return "clipboardData" in e ? e.clipboardData : window.clipboardData;
    },
  }),
  fv = Be(dv),
  pv = J({}, Sr, { data: 0 }),
  Nu = Be(pv),
  hv = {
    Esc: "Escape",
    Spacebar: " ",
    Left: "ArrowLeft",
    Up: "ArrowUp",
    Right: "ArrowRight",
    Down: "ArrowDown",
    Del: "Delete",
    Win: "OS",
    Menu: "ContextMenu",
    Apps: "ContextMenu",
    Scroll: "ScrollLock",
    MozPrintableKey: "Unidentified",
  },
  mv = {
    8: "Backspace",
    9: "Tab",
    12: "Clear",
    13: "Enter",
    16: "Shift",
    17: "Control",
    18: "Alt",
    19: "Pause",
    20: "CapsLock",
    27: "Escape",
    32: " ",
    33: "PageUp",
    34: "PageDown",
    35: "End",
    36: "Home",
    37: "ArrowLeft",
    38: "ArrowUp",
    39: "ArrowRight",
    40: "ArrowDown",
    45: "Insert",
    46: "Delete",
    112: "F1",
    113: "F2",
    114: "F3",
    115: "F4",
    116: "F5",
    117: "F6",
    118: "F7",
    119: "F8",
    120: "F9",
    121: "F10",
    122: "F11",
    123: "F12",
    144: "NumLock",
    145: "ScrollLock",
    224: "Meta",
  },
  vv = {
    Alt: "altKey",
    Control: "ctrlKey",
    Meta: "metaKey",
    Shift: "shiftKey",
  };
function gv(e) {
  var t = this.nativeEvent;
  return t.getModifierState ? t.getModifierState(e) : (e = vv[e]) ? !!t[e] : !1;
}
function ca() {
  return gv;
}
var yv = J({}, wo, {
    key: function (e) {
      if (e.key) {
        var t = hv[e.key] || e.key;
        if (t !== "Unidentified") return t;
      }
      return e.type === "keypress"
        ? ((e = Zo(e)), e === 13 ? "Enter" : String.fromCharCode(e))
        : e.type === "keydown" || e.type === "keyup"
          ? mv[e.keyCode] || "Unidentified"
          : "";
    },
    code: 0,
    location: 0,
    ctrlKey: 0,
    shiftKey: 0,
    altKey: 0,
    metaKey: 0,
    repeat: 0,
    locale: 0,
    getModifierState: ca,
    charCode: function (e) {
      return e.type === "keypress" ? Zo(e) : 0;
    },
    keyCode: function (e) {
      return e.type === "keydown" || e.type === "keyup" ? e.keyCode : 0;
    },
    which: function (e) {
      return e.type === "keypress"
        ? Zo(e)
        : e.type === "keydown" || e.type === "keyup"
          ? e.keyCode
          : 0;
    },
  }),
  wv = Be(yv),
  xv = J({}, zi, {
    pointerId: 0,
    width: 0,
    height: 0,
    pressure: 0,
    tangentialPressure: 0,
    tiltX: 0,
    tiltY: 0,
    twist: 0,
    pointerType: 0,
    isPrimary: 0,
  }),
  Tu = Be(xv),
  Sv = J({}, wo, {
    touches: 0,
    targetTouches: 0,
    changedTouches: 0,
    altKey: 0,
    metaKey: 0,
    ctrlKey: 0,
    shiftKey: 0,
    getModifierState: ca,
  }),
  Ev = Be(Sv),
  Cv = J({}, Sr, { propertyName: 0, elapsedTime: 0, pseudoElement: 0 }),
  kv = Be(Cv),
  Pv = J({}, zi, {
    deltaX: function (e) {
      return "deltaX" in e ? e.deltaX : "wheelDeltaX" in e ? -e.wheelDeltaX : 0;
    },
    deltaY: function (e) {
      return "deltaY" in e
        ? e.deltaY
        : "wheelDeltaY" in e
          ? -e.wheelDeltaY
          : "wheelDelta" in e
            ? -e.wheelDelta
            : 0;
    },
    deltaZ: 0,
    deltaMode: 0,
  }),
  Nv = Be(Pv),
  Tv = [9, 13, 27, 32],
  da = _t && "CompositionEvent" in window,
  $r = null;
_t && "documentMode" in document && ($r = document.documentMode);
var Rv = _t && "TextEvent" in window && !$r,
  Yd = _t && (!da || ($r && 8 < $r && 11 >= $r)),
  Ru = " ",
  _u = !1;
function qd(e, t) {
  switch (e) {
    case "keyup":
      return Tv.indexOf(t.keyCode) !== -1;
    case "keydown":
      return t.keyCode !== 229;
    case "keypress":
    case "mousedown":
    case "focusout":
      return !0;
    default:
      return !1;
  }
}
function Zd(e) {
  return ((e = e.detail), typeof e == "object" && "data" in e ? e.data : null);
}
var Fn = !1;
function _v(e, t) {
  switch (e) {
    case "compositionend":
      return Zd(t);
    case "keypress":
      return t.which !== 32 ? null : ((_u = !0), Ru);
    case "textInput":
      return ((e = t.data), e === Ru && _u ? null : e);
    default:
      return null;
  }
}
function Mv(e, t) {
  if (Fn)
    return e === "compositionend" || (!da && qd(e, t))
      ? ((e = Xd()), (qo = aa = Xt = null), (Fn = !1), e)
      : null;
  switch (e) {
    case "paste":
      return null;
    case "keypress":
      if (!(t.ctrlKey || t.altKey || t.metaKey) || (t.ctrlKey && t.altKey)) {
        if (t.char && 1 < t.char.length) return t.char;
        if (t.which) return String.fromCharCode(t.which);
      }
      return null;
    case "compositionend":
      return Yd && t.locale !== "ko" ? null : t.data;
    default:
      return null;
  }
}
var Ov = {
  color: !0,
  date: !0,
  datetime: !0,
  "datetime-local": !0,
  email: !0,
  month: !0,
  number: !0,
  password: !0,
  range: !0,
  search: !0,
  tel: !0,
  text: !0,
  time: !0,
  url: !0,
  week: !0,
};
function Mu(e) {
  var t = e && e.nodeName && e.nodeName.toLowerCase();
  return t === "input" ? !!Ov[e.type] : t === "textarea";
}
function Jd(e, t, n, r) {
  (Md(r),
    (t = gi(t, "onChange")),
    0 < t.length &&
      ((n = new ua("onChange", "change", null, n, r)),
      e.push({ event: n, listeners: t })));
}
var Ur = null,
  eo = null;
function jv(e) {
  df(e, 0);
}
function $i(e) {
  var t = Un(e);
  if (Cd(t)) return e;
}
function Dv(e, t) {
  if (e === "change") return t;
}
var ef = !1;
if (_t) {
  var vs;
  if (_t) {
    var gs = "oninput" in document;
    if (!gs) {
      var Ou = document.createElement("div");
      (Ou.setAttribute("oninput", "return;"),
        (gs = typeof Ou.oninput == "function"));
    }
    vs = gs;
  } else vs = !1;
  ef = vs && (!document.documentMode || 9 < document.documentMode);
}
function ju() {
  Ur && (Ur.detachEvent("onpropertychange", tf), (eo = Ur = null));
}
function tf(e) {
  if (e.propertyName === "value" && $i(eo)) {
    var t = [];
    (Jd(t, eo, e, ra(e)), bd(jv, t));
  }
}
function bv(e, t, n) {
  e === "focusin"
    ? (ju(), (Ur = t), (eo = n), Ur.attachEvent("onpropertychange", tf))
    : e === "focusout" && ju();
}
function Iv(e) {
  if (e === "selectionchange" || e === "keyup" || e === "keydown")
    return $i(eo);
}
function Lv(e, t) {
  if (e === "click") return $i(t);
}
function Av(e, t) {
  if (e === "input" || e === "change") return $i(t);
}
function Fv(e, t) {
  return (e === t && (e !== 0 || 1 / e === 1 / t)) || (e !== e && t !== t);
}
var at = typeof Object.is == "function" ? Object.is : Fv;
function to(e, t) {
  if (at(e, t)) return !0;
  if (typeof e != "object" || e === null || typeof t != "object" || t === null)
    return !1;
  var n = Object.keys(e),
    r = Object.keys(t);
  if (n.length !== r.length) return !1;
  for (r = 0; r < n.length; r++) {
    var o = n[r];
    if (!Ks.call(t, o) || !at(e[o], t[o])) return !1;
  }
  return !0;
}
function Du(e) {
  for (; e && e.firstChild; ) e = e.firstChild;
  return e;
}
function bu(e, t) {
  var n = Du(e);
  e = 0;
  for (var r; n; ) {
    if (n.nodeType === 3) {
      if (((r = e + n.textContent.length), e <= t && r >= t))
        return { node: n, offset: t - e };
      e = r;
    }
    e: {
      for (; n; ) {
        if (n.nextSibling) {
          n = n.nextSibling;
          break e;
        }
        n = n.parentNode;
      }
      n = void 0;
    }
    n = Du(n);
  }
}
function nf(e, t) {
  return e && t
    ? e === t
      ? !0
      : e && e.nodeType === 3
        ? !1
        : t && t.nodeType === 3
          ? nf(e, t.parentNode)
          : "contains" in e
            ? e.contains(t)
            : e.compareDocumentPosition
              ? !!(e.compareDocumentPosition(t) & 16)
              : !1
    : !1;
}
function rf() {
  for (var e = window, t = ci(); t instanceof e.HTMLIFrameElement; ) {
    try {
      var n = typeof t.contentWindow.location.href == "string";
    } catch {
      n = !1;
    }
    if (n) e = t.contentWindow;
    else break;
    t = ci(e.document);
  }
  return t;
}
function fa(e) {
  var t = e && e.nodeName && e.nodeName.toLowerCase();
  return (
    t &&
    ((t === "input" &&
      (e.type === "text" ||
        e.type === "search" ||
        e.type === "tel" ||
        e.type === "url" ||
        e.type === "password")) ||
      t === "textarea" ||
      e.contentEditable === "true")
  );
}
function zv(e) {
  var t = rf(),
    n = e.focusedElem,
    r = e.selectionRange;
  if (
    t !== n &&
    n &&
    n.ownerDocument &&
    nf(n.ownerDocument.documentElement, n)
  ) {
    if (r !== null && fa(n)) {
      if (
        ((t = r.start),
        (e = r.end),
        e === void 0 && (e = t),
        "selectionStart" in n)
      )
        ((n.selectionStart = t),
          (n.selectionEnd = Math.min(e, n.value.length)));
      else if (
        ((e = ((t = n.ownerDocument || document) && t.defaultView) || window),
        e.getSelection)
      ) {
        e = e.getSelection();
        var o = n.textContent.length,
          i = Math.min(r.start, o);
        ((r = r.end === void 0 ? i : Math.min(r.end, o)),
          !e.extend && i > r && ((o = r), (r = i), (i = o)),
          (o = bu(n, i)));
        var s = bu(n, r);
        o &&
          s &&
          (e.rangeCount !== 1 ||
            e.anchorNode !== o.node ||
            e.anchorOffset !== o.offset ||
            e.focusNode !== s.node ||
            e.focusOffset !== s.offset) &&
          ((t = t.createRange()),
          t.setStart(o.node, o.offset),
          e.removeAllRanges(),
          i > r
            ? (e.addRange(t), e.extend(s.node, s.offset))
            : (t.setEnd(s.node, s.offset), e.addRange(t)));
      }
    }
    for (t = [], e = n; (e = e.parentNode); )
      e.nodeType === 1 &&
        t.push({ element: e, left: e.scrollLeft, top: e.scrollTop });
    for (typeof n.focus == "function" && n.focus(), n = 0; n < t.length; n++)
      ((e = t[n]),
        (e.element.scrollLeft = e.left),
        (e.element.scrollTop = e.top));
  }
}
var $v = _t && "documentMode" in document && 11 >= document.documentMode,
  zn = null,
  fl = null,
  Wr = null,
  pl = !1;
function Iu(e, t, n) {
  var r = n.window === n ? n.document : n.nodeType === 9 ? n : n.ownerDocument;
  pl ||
    zn == null ||
    zn !== ci(r) ||
    ((r = zn),
    "selectionStart" in r && fa(r)
      ? (r = { start: r.selectionStart, end: r.selectionEnd })
      : ((r = (
          (r.ownerDocument && r.ownerDocument.defaultView) ||
          window
        ).getSelection()),
        (r = {
          anchorNode: r.anchorNode,
          anchorOffset: r.anchorOffset,
          focusNode: r.focusNode,
          focusOffset: r.focusOffset,
        })),
    (Wr && to(Wr, r)) ||
      ((Wr = r),
      (r = gi(fl, "onSelect")),
      0 < r.length &&
        ((t = new ua("onSelect", "select", null, t, n)),
        e.push({ event: t, listeners: r }),
        (t.target = zn))));
}
function jo(e, t) {
  var n = {};
  return (
    (n[e.toLowerCase()] = t.toLowerCase()),
    (n["Webkit" + e] = "webkit" + t),
    (n["Moz" + e] = "moz" + t),
    n
  );
}
var $n = {
    animationend: jo("Animation", "AnimationEnd"),
    animationiteration: jo("Animation", "AnimationIteration"),
    animationstart: jo("Animation", "AnimationStart"),
    transitionend: jo("Transition", "TransitionEnd"),
  },
  ys = {},
  of = {};
_t &&
  ((of = document.createElement("div").style),
  "AnimationEvent" in window ||
    (delete $n.animationend.animation,
    delete $n.animationiteration.animation,
    delete $n.animationstart.animation),
  "TransitionEvent" in window || delete $n.transitionend.transition);
function Ui(e) {
  if (ys[e]) return ys[e];
  if (!$n[e]) return e;
  var t = $n[e],
    n;
  for (n in t) if (t.hasOwnProperty(n) && n in of) return (ys[e] = t[n]);
  return e;
}
var sf = Ui("animationend"),
  lf = Ui("animationiteration"),
  af = Ui("animationstart"),
  uf = Ui("transitionend"),
  cf = new Map(),
  Lu =
    "abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(
      " ",
    );
function an(e, t) {
  (cf.set(e, t), _n(t, [e]));
}
for (var ws = 0; ws < Lu.length; ws++) {
  var xs = Lu[ws],
    Uv = xs.toLowerCase(),
    Wv = xs[0].toUpperCase() + xs.slice(1);
  an(Uv, "on" + Wv);
}
an(sf, "onAnimationEnd");
an(lf, "onAnimationIteration");
an(af, "onAnimationStart");
an("dblclick", "onDoubleClick");
an("focusin", "onFocus");
an("focusout", "onBlur");
an(uf, "onTransitionEnd");
dr("onMouseEnter", ["mouseout", "mouseover"]);
dr("onMouseLeave", ["mouseout", "mouseover"]);
dr("onPointerEnter", ["pointerout", "pointerover"]);
dr("onPointerLeave", ["pointerout", "pointerover"]);
_n(
  "onChange",
  "change click focusin focusout input keydown keyup selectionchange".split(
    " ",
  ),
);
_n(
  "onSelect",
  "focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(
    " ",
  ),
);
_n("onBeforeInput", ["compositionend", "keypress", "textInput", "paste"]);
_n(
  "onCompositionEnd",
  "compositionend focusout keydown keypress keyup mousedown".split(" "),
);
_n(
  "onCompositionStart",
  "compositionstart focusout keydown keypress keyup mousedown".split(" "),
);
_n(
  "onCompositionUpdate",
  "compositionupdate focusout keydown keypress keyup mousedown".split(" "),
);
var Ar =
    "abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(
      " ",
    ),
  Vv = new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));
function Au(e, t, n) {
  var r = e.type || "unknown-event";
  ((e.currentTarget = n), Um(r, t, void 0, e), (e.currentTarget = null));
}
function df(e, t) {
  t = (t & 4) !== 0;
  for (var n = 0; n < e.length; n++) {
    var r = e[n],
      o = r.event;
    r = r.listeners;
    e: {
      var i = void 0;
      if (t)
        for (var s = r.length - 1; 0 <= s; s--) {
          var l = r[s],
            a = l.instance,
            u = l.currentTarget;
          if (((l = l.listener), a !== i && o.isPropagationStopped())) break e;
          (Au(o, l, u), (i = a));
        }
      else
        for (s = 0; s < r.length; s++) {
          if (
            ((l = r[s]),
            (a = l.instance),
            (u = l.currentTarget),
            (l = l.listener),
            a !== i && o.isPropagationStopped())
          )
            break e;
          (Au(o, l, u), (i = a));
        }
    }
  }
  if (fi) throw ((e = al), (fi = !1), (al = null), e);
}
function G(e, t) {
  var n = t[yl];
  n === void 0 && (n = t[yl] = new Set());
  var r = e + "__bubble";
  n.has(r) || (ff(t, e, 2, !1), n.add(r));
}
function Ss(e, t, n) {
  var r = 0;
  (t && (r |= 4), ff(n, e, r, t));
}
var Do = "_reactListening" + Math.random().toString(36).slice(2);
function no(e) {
  if (!e[Do]) {
    ((e[Do] = !0),
      yd.forEach(function (n) {
        n !== "selectionchange" && (Vv.has(n) || Ss(n, !1, e), Ss(n, !0, e));
      }));
    var t = e.nodeType === 9 ? e : e.ownerDocument;
    t === null || t[Do] || ((t[Do] = !0), Ss("selectionchange", !1, t));
  }
}
function ff(e, t, n, r) {
  switch (Gd(t)) {
    case 1:
      var o = rv;
      break;
    case 4:
      o = ov;
      break;
    default:
      o = la;
  }
  ((n = o.bind(null, t, n, e)),
    (o = void 0),
    !ll ||
      (t !== "touchstart" && t !== "touchmove" && t !== "wheel") ||
      (o = !0),
    r
      ? o !== void 0
        ? e.addEventListener(t, n, { capture: !0, passive: o })
        : e.addEventListener(t, n, !0)
      : o !== void 0
        ? e.addEventListener(t, n, { passive: o })
        : e.addEventListener(t, n, !1));
}
function Es(e, t, n, r, o) {
  var i = r;
  if (!(t & 1) && !(t & 2) && r !== null)
    e: for (;;) {
      if (r === null) return;
      var s = r.tag;
      if (s === 3 || s === 4) {
        var l = r.stateNode.containerInfo;
        if (l === o || (l.nodeType === 8 && l.parentNode === o)) break;
        if (s === 4)
          for (s = r.return; s !== null; ) {
            var a = s.tag;
            if (
              (a === 3 || a === 4) &&
              ((a = s.stateNode.containerInfo),
              a === o || (a.nodeType === 8 && a.parentNode === o))
            )
              return;
            s = s.return;
          }
        for (; l !== null; ) {
          if (((s = hn(l)), s === null)) return;
          if (((a = s.tag), a === 5 || a === 6)) {
            r = i = s;
            continue e;
          }
          l = l.parentNode;
        }
      }
      r = r.return;
    }
  bd(function () {
    var u = i,
      v = ra(n),
      f = [];
    e: {
      var m = cf.get(e);
      if (m !== void 0) {
        var x = ua,
          E = e;
        switch (e) {
          case "keypress":
            if (Zo(n) === 0) break e;
          case "keydown":
          case "keyup":
            x = wv;
            break;
          case "focusin":
            ((E = "focus"), (x = ms));
            break;
          case "focusout":
            ((E = "blur"), (x = ms));
            break;
          case "beforeblur":
          case "afterblur":
            x = ms;
            break;
          case "click":
            if (n.button === 2) break e;
          case "auxclick":
          case "dblclick":
          case "mousedown":
          case "mousemove":
          case "mouseup":
          case "mouseout":
          case "mouseover":
          case "contextmenu":
            x = Pu;
            break;
          case "drag":
          case "dragend":
          case "dragenter":
          case "dragexit":
          case "dragleave":
          case "dragover":
          case "dragstart":
          case "drop":
            x = lv;
            break;
          case "touchcancel":
          case "touchend":
          case "touchmove":
          case "touchstart":
            x = Ev;
            break;
          case sf:
          case lf:
          case af:
            x = cv;
            break;
          case uf:
            x = kv;
            break;
          case "scroll":
            x = iv;
            break;
          case "wheel":
            x = Nv;
            break;
          case "copy":
          case "cut":
          case "paste":
            x = fv;
            break;
          case "gotpointercapture":
          case "lostpointercapture":
          case "pointercancel":
          case "pointerdown":
          case "pointermove":
          case "pointerout":
          case "pointerover":
          case "pointerup":
            x = Tu;
        }
        var g = (t & 4) !== 0,
          w = !g && e === "scroll",
          d = g ? (m !== null ? m + "Capture" : null) : m;
        g = [];
        for (var c = u, p; c !== null; ) {
          p = c;
          var S = p.stateNode;
          if (
            (p.tag === 5 &&
              S !== null &&
              ((p = S),
              d !== null && ((S = Yr(c, d)), S != null && g.push(ro(c, S, p)))),
            w)
          )
            break;
          c = c.return;
        }
        0 < g.length &&
          ((m = new x(m, E, null, n, v)), f.push({ event: m, listeners: g }));
      }
    }
    if (!(t & 7)) {
      e: {
        if (
          ((m = e === "mouseover" || e === "pointerover"),
          (x = e === "mouseout" || e === "pointerout"),
          m &&
            n !== il &&
            (E = n.relatedTarget || n.fromElement) &&
            (hn(E) || E[Mt]))
        )
          break e;
        if (
          (x || m) &&
          ((m =
            v.window === v
              ? v
              : (m = v.ownerDocument)
                ? m.defaultView || m.parentWindow
                : window),
          x
            ? ((E = n.relatedTarget || n.toElement),
              (x = u),
              (E = E ? hn(E) : null),
              E !== null &&
                ((w = Mn(E)), E !== w || (E.tag !== 5 && E.tag !== 6)) &&
                (E = null))
            : ((x = null), (E = u)),
          x !== E)
        ) {
          if (
            ((g = Pu),
            (S = "onMouseLeave"),
            (d = "onMouseEnter"),
            (c = "mouse"),
            (e === "pointerout" || e === "pointerover") &&
              ((g = Tu),
              (S = "onPointerLeave"),
              (d = "onPointerEnter"),
              (c = "pointer")),
            (w = x == null ? m : Un(x)),
            (p = E == null ? m : Un(E)),
            (m = new g(S, c + "leave", x, n, v)),
            (m.target = w),
            (m.relatedTarget = p),
            (S = null),
            hn(v) === u &&
              ((g = new g(d, c + "enter", E, n, v)),
              (g.target = p),
              (g.relatedTarget = w),
              (S = g)),
            (w = S),
            x && E)
          )
            t: {
              for (g = x, d = E, c = 0, p = g; p; p = On(p)) c++;
              for (p = 0, S = d; S; S = On(S)) p++;
              for (; 0 < c - p; ) ((g = On(g)), c--);
              for (; 0 < p - c; ) ((d = On(d)), p--);
              for (; c--; ) {
                if (g === d || (d !== null && g === d.alternate)) break t;
                ((g = On(g)), (d = On(d)));
              }
              g = null;
            }
          else g = null;
          (x !== null && Fu(f, m, x, g, !1),
            E !== null && w !== null && Fu(f, w, E, g, !0));
        }
      }
      e: {
        if (
          ((m = u ? Un(u) : window),
          (x = m.nodeName && m.nodeName.toLowerCase()),
          x === "select" || (x === "input" && m.type === "file"))
        )
          var C = Dv;
        else if (Mu(m))
          if (ef) C = Av;
          else {
            C = Iv;
            var N = bv;
          }
        else
          (x = m.nodeName) &&
            x.toLowerCase() === "input" &&
            (m.type === "checkbox" || m.type === "radio") &&
            (C = Lv);
        if (C && (C = C(e, u))) {
          Jd(f, C, n, v);
          break e;
        }
        (N && N(e, m, u),
          e === "focusout" &&
            (N = m._wrapperState) &&
            N.controlled &&
            m.type === "number" &&
            el(m, "number", m.value));
      }
      switch (((N = u ? Un(u) : window), e)) {
        case "focusin":
          (Mu(N) || N.contentEditable === "true") &&
            ((zn = N), (fl = u), (Wr = null));
          break;
        case "focusout":
          Wr = fl = zn = null;
          break;
        case "mousedown":
          pl = !0;
          break;
        case "contextmenu":
        case "mouseup":
        case "dragend":
          ((pl = !1), Iu(f, n, v));
          break;
        case "selectionchange":
          if ($v) break;
        case "keydown":
        case "keyup":
          Iu(f, n, v);
      }
      var k;
      if (da)
        e: {
          switch (e) {
            case "compositionstart":
              var T = "onCompositionStart";
              break e;
            case "compositionend":
              T = "onCompositionEnd";
              break e;
            case "compositionupdate":
              T = "onCompositionUpdate";
              break e;
          }
          T = void 0;
        }
      else
        Fn
          ? qd(e, n) && (T = "onCompositionEnd")
          : e === "keydown" && n.keyCode === 229 && (T = "onCompositionStart");
      (T &&
        (Yd &&
          n.locale !== "ko" &&
          (Fn || T !== "onCompositionStart"
            ? T === "onCompositionEnd" && Fn && (k = Xd())
            : ((Xt = v),
              (aa = "value" in Xt ? Xt.value : Xt.textContent),
              (Fn = !0))),
        (N = gi(u, T)),
        0 < N.length &&
          ((T = new Nu(T, e, null, n, v)),
          f.push({ event: T, listeners: N }),
          k ? (T.data = k) : ((k = Zd(n)), k !== null && (T.data = k)))),
        (k = Rv ? _v(e, n) : Mv(e, n)) &&
          ((u = gi(u, "onBeforeInput")),
          0 < u.length &&
            ((v = new Nu("onBeforeInput", "beforeinput", null, n, v)),
            f.push({ event: v, listeners: u }),
            (v.data = k))));
    }
    df(f, t);
  });
}
function ro(e, t, n) {
  return { instance: e, listener: t, currentTarget: n };
}
function gi(e, t) {
  for (var n = t + "Capture", r = []; e !== null; ) {
    var o = e,
      i = o.stateNode;
    (o.tag === 5 &&
      i !== null &&
      ((o = i),
      (i = Yr(e, n)),
      i != null && r.unshift(ro(e, i, o)),
      (i = Yr(e, t)),
      i != null && r.push(ro(e, i, o))),
      (e = e.return));
  }
  return r;
}
function On(e) {
  if (e === null) return null;
  do e = e.return;
  while (e && e.tag !== 5);
  return e || null;
}
function Fu(e, t, n, r, o) {
  for (var i = t._reactName, s = []; n !== null && n !== r; ) {
    var l = n,
      a = l.alternate,
      u = l.stateNode;
    if (a !== null && a === r) break;
    (l.tag === 5 &&
      u !== null &&
      ((l = u),
      o
        ? ((a = Yr(n, i)), a != null && s.unshift(ro(n, a, l)))
        : o || ((a = Yr(n, i)), a != null && s.push(ro(n, a, l)))),
      (n = n.return));
  }
  s.length !== 0 && e.push({ event: t, listeners: s });
}
var Bv = /\r\n?/g,
  Hv = /\u0000|\uFFFD/g;
function zu(e) {
  return (typeof e == "string" ? e : "" + e)
    .replace(
      Bv,
      `
`,
    )
    .replace(Hv, "");
}
function bo(e, t, n) {
  if (((t = zu(t)), zu(e) !== t && n)) throw Error(R(425));
}
function yi() {}
var hl = null,
  ml = null;
function vl(e, t) {
  return (
    e === "textarea" ||
    e === "noscript" ||
    typeof t.children == "string" ||
    typeof t.children == "number" ||
    (typeof t.dangerouslySetInnerHTML == "object" &&
      t.dangerouslySetInnerHTML !== null &&
      t.dangerouslySetInnerHTML.__html != null)
  );
}
var gl = typeof setTimeout == "function" ? setTimeout : void 0,
  Qv = typeof clearTimeout == "function" ? clearTimeout : void 0,
  $u = typeof Promise == "function" ? Promise : void 0,
  Kv =
    typeof queueMicrotask == "function"
      ? queueMicrotask
      : typeof $u < "u"
        ? function (e) {
            return $u.resolve(null).then(e).catch(Gv);
          }
        : gl;
function Gv(e) {
  setTimeout(function () {
    throw e;
  });
}
function Cs(e, t) {
  var n = t,
    r = 0;
  do {
    var o = n.nextSibling;
    if ((e.removeChild(n), o && o.nodeType === 8))
      if (((n = o.data), n === "/$")) {
        if (r === 0) {
          (e.removeChild(o), Jr(t));
          return;
        }
        r--;
      } else (n !== "$" && n !== "$?" && n !== "$!") || r++;
    n = o;
  } while (n);
  Jr(t);
}
function en(e) {
  for (; e != null; e = e.nextSibling) {
    var t = e.nodeType;
    if (t === 1 || t === 3) break;
    if (t === 8) {
      if (((t = e.data), t === "$" || t === "$!" || t === "$?")) break;
      if (t === "/$") return null;
    }
  }
  return e;
}
function Uu(e) {
  e = e.previousSibling;
  for (var t = 0; e; ) {
    if (e.nodeType === 8) {
      var n = e.data;
      if (n === "$" || n === "$!" || n === "$?") {
        if (t === 0) return e;
        t--;
      } else n === "/$" && t++;
    }
    e = e.previousSibling;
  }
  return null;
}
var Er = Math.random().toString(36).slice(2),
  gt = "__reactFiber$" + Er,
  oo = "__reactProps$" + Er,
  Mt = "__reactContainer$" + Er,
  yl = "__reactEvents$" + Er,
  Xv = "__reactListeners$" + Er,
  Yv = "__reactHandles$" + Er;
function hn(e) {
  var t = e[gt];
  if (t) return t;
  for (var n = e.parentNode; n; ) {
    if ((t = n[Mt] || n[gt])) {
      if (
        ((n = t.alternate),
        t.child !== null || (n !== null && n.child !== null))
      )
        for (e = Uu(e); e !== null; ) {
          if ((n = e[gt])) return n;
          e = Uu(e);
        }
      return t;
    }
    ((e = n), (n = e.parentNode));
  }
  return null;
}
function xo(e) {
  return (
    (e = e[gt] || e[Mt]),
    !e || (e.tag !== 5 && e.tag !== 6 && e.tag !== 13 && e.tag !== 3) ? null : e
  );
}
function Un(e) {
  if (e.tag === 5 || e.tag === 6) return e.stateNode;
  throw Error(R(33));
}
function Wi(e) {
  return e[oo] || null;
}
var wl = [],
  Wn = -1;
function un(e) {
  return { current: e };
}
function X(e) {
  0 > Wn || ((e.current = wl[Wn]), (wl[Wn] = null), Wn--);
}
function Q(e, t) {
  (Wn++, (wl[Wn] = e.current), (e.current = t));
}
var ln = {},
  Ce = un(ln),
  Ie = un(!1),
  Cn = ln;
function fr(e, t) {
  var n = e.type.contextTypes;
  if (!n) return ln;
  var r = e.stateNode;
  if (r && r.__reactInternalMemoizedUnmaskedChildContext === t)
    return r.__reactInternalMemoizedMaskedChildContext;
  var o = {},
    i;
  for (i in n) o[i] = t[i];
  return (
    r &&
      ((e = e.stateNode),
      (e.__reactInternalMemoizedUnmaskedChildContext = t),
      (e.__reactInternalMemoizedMaskedChildContext = o)),
    o
  );
}
function Le(e) {
  return ((e = e.childContextTypes), e != null);
}
function wi() {
  (X(Ie), X(Ce));
}
function Wu(e, t, n) {
  if (Ce.current !== ln) throw Error(R(168));
  (Q(Ce, t), Q(Ie, n));
}
function pf(e, t, n) {
  var r = e.stateNode;
  if (((t = t.childContextTypes), typeof r.getChildContext != "function"))
    return n;
  r = r.getChildContext();
  for (var o in r) if (!(o in t)) throw Error(R(108, bm(e) || "Unknown", o));
  return J({}, n, r);
}
function xi(e) {
  return (
    (e =
      ((e = e.stateNode) && e.__reactInternalMemoizedMergedChildContext) || ln),
    (Cn = Ce.current),
    Q(Ce, e),
    Q(Ie, Ie.current),
    !0
  );
}
function Vu(e, t, n) {
  var r = e.stateNode;
  if (!r) throw Error(R(169));
  (n
    ? ((e = pf(e, t, Cn)),
      (r.__reactInternalMemoizedMergedChildContext = e),
      X(Ie),
      X(Ce),
      Q(Ce, e))
    : X(Ie),
    Q(Ie, n));
}
var Pt = null,
  Vi = !1,
  ks = !1;
function hf(e) {
  Pt === null ? (Pt = [e]) : Pt.push(e);
}
function qv(e) {
  ((Vi = !0), hf(e));
}
function cn() {
  if (!ks && Pt !== null) {
    ks = !0;
    var e = 0,
      t = B;
    try {
      var n = Pt;
      for (B = 1; e < n.length; e++) {
        var r = n[e];
        do r = r(!0);
        while (r !== null);
      }
      ((Pt = null), (Vi = !1));
    } catch (o) {
      throw (Pt !== null && (Pt = Pt.slice(e + 1)), Fd(oa, cn), o);
    } finally {
      ((B = t), (ks = !1));
    }
  }
  return null;
}
var Vn = [],
  Bn = 0,
  Si = null,
  Ei = 0,
  Ge = [],
  Xe = 0,
  kn = null,
  Nt = 1,
  Tt = "";
function fn(e, t) {
  ((Vn[Bn++] = Ei), (Vn[Bn++] = Si), (Si = e), (Ei = t));
}
function mf(e, t, n) {
  ((Ge[Xe++] = Nt), (Ge[Xe++] = Tt), (Ge[Xe++] = kn), (kn = e));
  var r = Nt;
  e = Tt;
  var o = 32 - st(r) - 1;
  ((r &= ~(1 << o)), (n += 1));
  var i = 32 - st(t) + o;
  if (30 < i) {
    var s = o - (o % 5);
    ((i = (r & ((1 << s) - 1)).toString(32)),
      (r >>= s),
      (o -= s),
      (Nt = (1 << (32 - st(t) + o)) | (n << o) | r),
      (Tt = i + e));
  } else ((Nt = (1 << i) | (n << o) | r), (Tt = e));
}
function pa(e) {
  e.return !== null && (fn(e, 1), mf(e, 1, 0));
}
function ha(e) {
  for (; e === Si; )
    ((Si = Vn[--Bn]), (Vn[Bn] = null), (Ei = Vn[--Bn]), (Vn[Bn] = null));
  for (; e === kn; )
    ((kn = Ge[--Xe]),
      (Ge[Xe] = null),
      (Tt = Ge[--Xe]),
      (Ge[Xe] = null),
      (Nt = Ge[--Xe]),
      (Ge[Xe] = null));
}
var Ue = null,
  $e = null,
  Y = !1,
  it = null;
function vf(e, t) {
  var n = Ye(5, null, null, 0);
  ((n.elementType = "DELETED"),
    (n.stateNode = t),
    (n.return = e),
    (t = e.deletions),
    t === null ? ((e.deletions = [n]), (e.flags |= 16)) : t.push(n));
}
function Bu(e, t) {
  switch (e.tag) {
    case 5:
      var n = e.type;
      return (
        (t =
          t.nodeType !== 1 || n.toLowerCase() !== t.nodeName.toLowerCase()
            ? null
            : t),
        t !== null
          ? ((e.stateNode = t), (Ue = e), ($e = en(t.firstChild)), !0)
          : !1
      );
    case 6:
      return (
        (t = e.pendingProps === "" || t.nodeType !== 3 ? null : t),
        t !== null ? ((e.stateNode = t), (Ue = e), ($e = null), !0) : !1
      );
    case 13:
      return (
        (t = t.nodeType !== 8 ? null : t),
        t !== null
          ? ((n = kn !== null ? { id: Nt, overflow: Tt } : null),
            (e.memoizedState = {
              dehydrated: t,
              treeContext: n,
              retryLane: 1073741824,
            }),
            (n = Ye(18, null, null, 0)),
            (n.stateNode = t),
            (n.return = e),
            (e.child = n),
            (Ue = e),
            ($e = null),
            !0)
          : !1
      );
    default:
      return !1;
  }
}
function xl(e) {
  return (e.mode & 1) !== 0 && (e.flags & 128) === 0;
}
function Sl(e) {
  if (Y) {
    var t = $e;
    if (t) {
      var n = t;
      if (!Bu(e, t)) {
        if (xl(e)) throw Error(R(418));
        t = en(n.nextSibling);
        var r = Ue;
        t && Bu(e, t)
          ? vf(r, n)
          : ((e.flags = (e.flags & -4097) | 2), (Y = !1), (Ue = e));
      }
    } else {
      if (xl(e)) throw Error(R(418));
      ((e.flags = (e.flags & -4097) | 2), (Y = !1), (Ue = e));
    }
  }
}
function Hu(e) {
  for (e = e.return; e !== null && e.tag !== 5 && e.tag !== 3 && e.tag !== 13; )
    e = e.return;
  Ue = e;
}
function Io(e) {
  if (e !== Ue) return !1;
  if (!Y) return (Hu(e), (Y = !0), !1);
  var t;
  if (
    ((t = e.tag !== 3) &&
      !(t = e.tag !== 5) &&
      ((t = e.type),
      (t = t !== "head" && t !== "body" && !vl(e.type, e.memoizedProps))),
    t && (t = $e))
  ) {
    if (xl(e)) throw (gf(), Error(R(418)));
    for (; t; ) (vf(e, t), (t = en(t.nextSibling)));
  }
  if ((Hu(e), e.tag === 13)) {
    if (((e = e.memoizedState), (e = e !== null ? e.dehydrated : null), !e))
      throw Error(R(317));
    e: {
      for (e = e.nextSibling, t = 0; e; ) {
        if (e.nodeType === 8) {
          var n = e.data;
          if (n === "/$") {
            if (t === 0) {
              $e = en(e.nextSibling);
              break e;
            }
            t--;
          } else (n !== "$" && n !== "$!" && n !== "$?") || t++;
        }
        e = e.nextSibling;
      }
      $e = null;
    }
  } else $e = Ue ? en(e.stateNode.nextSibling) : null;
  return !0;
}
function gf() {
  for (var e = $e; e; ) e = en(e.nextSibling);
}
function pr() {
  (($e = Ue = null), (Y = !1));
}
function ma(e) {
  it === null ? (it = [e]) : it.push(e);
}
var Zv = Dt.ReactCurrentBatchConfig;
function _r(e, t, n) {
  if (
    ((e = n.ref), e !== null && typeof e != "function" && typeof e != "object")
  ) {
    if (n._owner) {
      if (((n = n._owner), n)) {
        if (n.tag !== 1) throw Error(R(309));
        var r = n.stateNode;
      }
      if (!r) throw Error(R(147, e));
      var o = r,
        i = "" + e;
      return t !== null &&
        t.ref !== null &&
        typeof t.ref == "function" &&
        t.ref._stringRef === i
        ? t.ref
        : ((t = function (s) {
            var l = o.refs;
            s === null ? delete l[i] : (l[i] = s);
          }),
          (t._stringRef = i),
          t);
    }
    if (typeof e != "string") throw Error(R(284));
    if (!n._owner) throw Error(R(290, e));
  }
  return e;
}
function Lo(e, t) {
  throw (
    (e = Object.prototype.toString.call(t)),
    Error(
      R(
        31,
        e === "[object Object]"
          ? "object with keys {" + Object.keys(t).join(", ") + "}"
          : e,
      ),
    )
  );
}
function Qu(e) {
  var t = e._init;
  return t(e._payload);
}
function yf(e) {
  function t(d, c) {
    if (e) {
      var p = d.deletions;
      p === null ? ((d.deletions = [c]), (d.flags |= 16)) : p.push(c);
    }
  }
  function n(d, c) {
    if (!e) return null;
    for (; c !== null; ) (t(d, c), (c = c.sibling));
    return null;
  }
  function r(d, c) {
    for (d = new Map(); c !== null; )
      (c.key !== null ? d.set(c.key, c) : d.set(c.index, c), (c = c.sibling));
    return d;
  }
  function o(d, c) {
    return ((d = on(d, c)), (d.index = 0), (d.sibling = null), d);
  }
  function i(d, c, p) {
    return (
      (d.index = p),
      e
        ? ((p = d.alternate),
          p !== null
            ? ((p = p.index), p < c ? ((d.flags |= 2), c) : p)
            : ((d.flags |= 2), c))
        : ((d.flags |= 1048576), c)
    );
  }
  function s(d) {
    return (e && d.alternate === null && (d.flags |= 2), d);
  }
  function l(d, c, p, S) {
    return c === null || c.tag !== 6
      ? ((c = Os(p, d.mode, S)), (c.return = d), c)
      : ((c = o(c, p)), (c.return = d), c);
  }
  function a(d, c, p, S) {
    var C = p.type;
    return C === An
      ? v(d, c, p.props.children, S, p.key)
      : c !== null &&
          (c.elementType === C ||
            (typeof C == "object" &&
              C !== null &&
              C.$$typeof === $t &&
              Qu(C) === c.type))
        ? ((S = o(c, p.props)), (S.ref = _r(d, c, p)), (S.return = d), S)
        : ((S = ii(p.type, p.key, p.props, null, d.mode, S)),
          (S.ref = _r(d, c, p)),
          (S.return = d),
          S);
  }
  function u(d, c, p, S) {
    return c === null ||
      c.tag !== 4 ||
      c.stateNode.containerInfo !== p.containerInfo ||
      c.stateNode.implementation !== p.implementation
      ? ((c = js(p, d.mode, S)), (c.return = d), c)
      : ((c = o(c, p.children || [])), (c.return = d), c);
  }
  function v(d, c, p, S, C) {
    return c === null || c.tag !== 7
      ? ((c = En(p, d.mode, S, C)), (c.return = d), c)
      : ((c = o(c, p)), (c.return = d), c);
  }
  function f(d, c, p) {
    if ((typeof c == "string" && c !== "") || typeof c == "number")
      return ((c = Os("" + c, d.mode, p)), (c.return = d), c);
    if (typeof c == "object" && c !== null) {
      switch (c.$$typeof) {
        case Po:
          return (
            (p = ii(c.type, c.key, c.props, null, d.mode, p)),
            (p.ref = _r(d, null, c)),
            (p.return = d),
            p
          );
        case Ln:
          return ((c = js(c, d.mode, p)), (c.return = d), c);
        case $t:
          var S = c._init;
          return f(d, S(c._payload), p);
      }
      if (Ir(c) || kr(c))
        return ((c = En(c, d.mode, p, null)), (c.return = d), c);
      Lo(d, c);
    }
    return null;
  }
  function m(d, c, p, S) {
    var C = c !== null ? c.key : null;
    if ((typeof p == "string" && p !== "") || typeof p == "number")
      return C !== null ? null : l(d, c, "" + p, S);
    if (typeof p == "object" && p !== null) {
      switch (p.$$typeof) {
        case Po:
          return p.key === C ? a(d, c, p, S) : null;
        case Ln:
          return p.key === C ? u(d, c, p, S) : null;
        case $t:
          return ((C = p._init), m(d, c, C(p._payload), S));
      }
      if (Ir(p) || kr(p)) return C !== null ? null : v(d, c, p, S, null);
      Lo(d, p);
    }
    return null;
  }
  function x(d, c, p, S, C) {
    if ((typeof S == "string" && S !== "") || typeof S == "number")
      return ((d = d.get(p) || null), l(c, d, "" + S, C));
    if (typeof S == "object" && S !== null) {
      switch (S.$$typeof) {
        case Po:
          return (
            (d = d.get(S.key === null ? p : S.key) || null),
            a(c, d, S, C)
          );
        case Ln:
          return (
            (d = d.get(S.key === null ? p : S.key) || null),
            u(c, d, S, C)
          );
        case $t:
          var N = S._init;
          return x(d, c, p, N(S._payload), C);
      }
      if (Ir(S) || kr(S)) return ((d = d.get(p) || null), v(c, d, S, C, null));
      Lo(c, S);
    }
    return null;
  }
  function E(d, c, p, S) {
    for (
      var C = null, N = null, k = c, T = (c = 0), b = null;
      k !== null && T < p.length;
      T++
    ) {
      k.index > T ? ((b = k), (k = null)) : (b = k.sibling);
      var D = m(d, k, p[T], S);
      if (D === null) {
        k === null && (k = b);
        break;
      }
      (e && k && D.alternate === null && t(d, k),
        (c = i(D, c, T)),
        N === null ? (C = D) : (N.sibling = D),
        (N = D),
        (k = b));
    }
    if (T === p.length) return (n(d, k), Y && fn(d, T), C);
    if (k === null) {
      for (; T < p.length; T++)
        ((k = f(d, p[T], S)),
          k !== null &&
            ((c = i(k, c, T)),
            N === null ? (C = k) : (N.sibling = k),
            (N = k)));
      return (Y && fn(d, T), C);
    }
    for (k = r(d, k); T < p.length; T++)
      ((b = x(k, d, T, p[T], S)),
        b !== null &&
          (e && b.alternate !== null && k.delete(b.key === null ? T : b.key),
          (c = i(b, c, T)),
          N === null ? (C = b) : (N.sibling = b),
          (N = b)));
    return (
      e &&
        k.forEach(function (H) {
          return t(d, H);
        }),
      Y && fn(d, T),
      C
    );
  }
  function g(d, c, p, S) {
    var C = kr(p);
    if (typeof C != "function") throw Error(R(150));
    if (((p = C.call(p)), p == null)) throw Error(R(151));
    for (
      var N = (C = null), k = c, T = (c = 0), b = null, D = p.next();
      k !== null && !D.done;
      T++, D = p.next()
    ) {
      k.index > T ? ((b = k), (k = null)) : (b = k.sibling);
      var H = m(d, k, D.value, S);
      if (H === null) {
        k === null && (k = b);
        break;
      }
      (e && k && H.alternate === null && t(d, k),
        (c = i(H, c, T)),
        N === null ? (C = H) : (N.sibling = H),
        (N = H),
        (k = b));
    }
    if (D.done) return (n(d, k), Y && fn(d, T), C);
    if (k === null) {
      for (; !D.done; T++, D = p.next())
        ((D = f(d, D.value, S)),
          D !== null &&
            ((c = i(D, c, T)),
            N === null ? (C = D) : (N.sibling = D),
            (N = D)));
      return (Y && fn(d, T), C);
    }
    for (k = r(d, k); !D.done; T++, D = p.next())
      ((D = x(k, d, T, D.value, S)),
        D !== null &&
          (e && D.alternate !== null && k.delete(D.key === null ? T : D.key),
          (c = i(D, c, T)),
          N === null ? (C = D) : (N.sibling = D),
          (N = D)));
    return (
      e &&
        k.forEach(function (z) {
          return t(d, z);
        }),
      Y && fn(d, T),
      C
    );
  }
  function w(d, c, p, S) {
    if (
      (typeof p == "object" &&
        p !== null &&
        p.type === An &&
        p.key === null &&
        (p = p.props.children),
      typeof p == "object" && p !== null)
    ) {
      switch (p.$$typeof) {
        case Po:
          e: {
            for (var C = p.key, N = c; N !== null; ) {
              if (N.key === C) {
                if (((C = p.type), C === An)) {
                  if (N.tag === 7) {
                    (n(d, N.sibling),
                      (c = o(N, p.props.children)),
                      (c.return = d),
                      (d = c));
                    break e;
                  }
                } else if (
                  N.elementType === C ||
                  (typeof C == "object" &&
                    C !== null &&
                    C.$$typeof === $t &&
                    Qu(C) === N.type)
                ) {
                  (n(d, N.sibling),
                    (c = o(N, p.props)),
                    (c.ref = _r(d, N, p)),
                    (c.return = d),
                    (d = c));
                  break e;
                }
                n(d, N);
                break;
              } else t(d, N);
              N = N.sibling;
            }
            p.type === An
              ? ((c = En(p.props.children, d.mode, S, p.key)),
                (c.return = d),
                (d = c))
              : ((S = ii(p.type, p.key, p.props, null, d.mode, S)),
                (S.ref = _r(d, c, p)),
                (S.return = d),
                (d = S));
          }
          return s(d);
        case Ln:
          e: {
            for (N = p.key; c !== null; ) {
              if (c.key === N)
                if (
                  c.tag === 4 &&
                  c.stateNode.containerInfo === p.containerInfo &&
                  c.stateNode.implementation === p.implementation
                ) {
                  (n(d, c.sibling),
                    (c = o(c, p.children || [])),
                    (c.return = d),
                    (d = c));
                  break e;
                } else {
                  n(d, c);
                  break;
                }
              else t(d, c);
              c = c.sibling;
            }
            ((c = js(p, d.mode, S)), (c.return = d), (d = c));
          }
          return s(d);
        case $t:
          return ((N = p._init), w(d, c, N(p._payload), S));
      }
      if (Ir(p)) return E(d, c, p, S);
      if (kr(p)) return g(d, c, p, S);
      Lo(d, p);
    }
    return (typeof p == "string" && p !== "") || typeof p == "number"
      ? ((p = "" + p),
        c !== null && c.tag === 6
          ? (n(d, c.sibling), (c = o(c, p)), (c.return = d), (d = c))
          : (n(d, c), (c = Os(p, d.mode, S)), (c.return = d), (d = c)),
        s(d))
      : n(d, c);
  }
  return w;
}
var hr = yf(!0),
  wf = yf(!1),
  Ci = un(null),
  ki = null,
  Hn = null,
  va = null;
function ga() {
  va = Hn = ki = null;
}
function ya(e) {
  var t = Ci.current;
  (X(Ci), (e._currentValue = t));
}
function El(e, t, n) {
  for (; e !== null; ) {
    var r = e.alternate;
    if (
      ((e.childLanes & t) !== t
        ? ((e.childLanes |= t), r !== null && (r.childLanes |= t))
        : r !== null && (r.childLanes & t) !== t && (r.childLanes |= t),
      e === n)
    )
      break;
    e = e.return;
  }
}
function Zn(e, t) {
  ((ki = e),
    (va = Hn = null),
    (e = e.dependencies),
    e !== null &&
      e.firstContext !== null &&
      (e.lanes & t && (be = !0), (e.firstContext = null)));
}
function Ze(e) {
  var t = e._currentValue;
  if (va !== e)
    if (((e = { context: e, memoizedValue: t, next: null }), Hn === null)) {
      if (ki === null) throw Error(R(308));
      ((Hn = e), (ki.dependencies = { lanes: 0, firstContext: e }));
    } else Hn = Hn.next = e;
  return t;
}
var mn = null;
function wa(e) {
  mn === null ? (mn = [e]) : mn.push(e);
}
function xf(e, t, n, r) {
  var o = t.interleaved;
  return (
    o === null ? ((n.next = n), wa(t)) : ((n.next = o.next), (o.next = n)),
    (t.interleaved = n),
    Ot(e, r)
  );
}
function Ot(e, t) {
  e.lanes |= t;
  var n = e.alternate;
  for (n !== null && (n.lanes |= t), n = e, e = e.return; e !== null; )
    ((e.childLanes |= t),
      (n = e.alternate),
      n !== null && (n.childLanes |= t),
      (n = e),
      (e = e.return));
  return n.tag === 3 ? n.stateNode : null;
}
var Ut = !1;
function xa(e) {
  e.updateQueue = {
    baseState: e.memoizedState,
    firstBaseUpdate: null,
    lastBaseUpdate: null,
    shared: { pending: null, interleaved: null, lanes: 0 },
    effects: null,
  };
}
function Sf(e, t) {
  ((e = e.updateQueue),
    t.updateQueue === e &&
      (t.updateQueue = {
        baseState: e.baseState,
        firstBaseUpdate: e.firstBaseUpdate,
        lastBaseUpdate: e.lastBaseUpdate,
        shared: e.shared,
        effects: e.effects,
      }));
}
function Rt(e, t) {
  return {
    eventTime: e,
    lane: t,
    tag: 0,
    payload: null,
    callback: null,
    next: null,
  };
}
function tn(e, t, n) {
  var r = e.updateQueue;
  if (r === null) return null;
  if (((r = r.shared), W & 2)) {
    var o = r.pending;
    return (
      o === null ? (t.next = t) : ((t.next = o.next), (o.next = t)),
      (r.pending = t),
      Ot(e, n)
    );
  }
  return (
    (o = r.interleaved),
    o === null ? ((t.next = t), wa(r)) : ((t.next = o.next), (o.next = t)),
    (r.interleaved = t),
    Ot(e, n)
  );
}
function Jo(e, t, n) {
  if (
    ((t = t.updateQueue), t !== null && ((t = t.shared), (n & 4194240) !== 0))
  ) {
    var r = t.lanes;
    ((r &= e.pendingLanes), (n |= r), (t.lanes = n), ia(e, n));
  }
}
function Ku(e, t) {
  var n = e.updateQueue,
    r = e.alternate;
  if (r !== null && ((r = r.updateQueue), n === r)) {
    var o = null,
      i = null;
    if (((n = n.firstBaseUpdate), n !== null)) {
      do {
        var s = {
          eventTime: n.eventTime,
          lane: n.lane,
          tag: n.tag,
          payload: n.payload,
          callback: n.callback,
          next: null,
        };
        (i === null ? (o = i = s) : (i = i.next = s), (n = n.next));
      } while (n !== null);
      i === null ? (o = i = t) : (i = i.next = t);
    } else o = i = t;
    ((n = {
      baseState: r.baseState,
      firstBaseUpdate: o,
      lastBaseUpdate: i,
      shared: r.shared,
      effects: r.effects,
    }),
      (e.updateQueue = n));
    return;
  }
  ((e = n.lastBaseUpdate),
    e === null ? (n.firstBaseUpdate = t) : (e.next = t),
    (n.lastBaseUpdate = t));
}
function Pi(e, t, n, r) {
  var o = e.updateQueue;
  Ut = !1;
  var i = o.firstBaseUpdate,
    s = o.lastBaseUpdate,
    l = o.shared.pending;
  if (l !== null) {
    o.shared.pending = null;
    var a = l,
      u = a.next;
    ((a.next = null), s === null ? (i = u) : (s.next = u), (s = a));
    var v = e.alternate;
    v !== null &&
      ((v = v.updateQueue),
      (l = v.lastBaseUpdate),
      l !== s &&
        (l === null ? (v.firstBaseUpdate = u) : (l.next = u),
        (v.lastBaseUpdate = a)));
  }
  if (i !== null) {
    var f = o.baseState;
    ((s = 0), (v = u = a = null), (l = i));
    do {
      var m = l.lane,
        x = l.eventTime;
      if ((r & m) === m) {
        v !== null &&
          (v = v.next =
            {
              eventTime: x,
              lane: 0,
              tag: l.tag,
              payload: l.payload,
              callback: l.callback,
              next: null,
            });
        e: {
          var E = e,
            g = l;
          switch (((m = t), (x = n), g.tag)) {
            case 1:
              if (((E = g.payload), typeof E == "function")) {
                f = E.call(x, f, m);
                break e;
              }
              f = E;
              break e;
            case 3:
              E.flags = (E.flags & -65537) | 128;
            case 0:
              if (
                ((E = g.payload),
                (m = typeof E == "function" ? E.call(x, f, m) : E),
                m == null)
              )
                break e;
              f = J({}, f, m);
              break e;
            case 2:
              Ut = !0;
          }
        }
        l.callback !== null &&
          l.lane !== 0 &&
          ((e.flags |= 64),
          (m = o.effects),
          m === null ? (o.effects = [l]) : m.push(l));
      } else
        ((x = {
          eventTime: x,
          lane: m,
          tag: l.tag,
          payload: l.payload,
          callback: l.callback,
          next: null,
        }),
          v === null ? ((u = v = x), (a = f)) : (v = v.next = x),
          (s |= m));
      if (((l = l.next), l === null)) {
        if (((l = o.shared.pending), l === null)) break;
        ((m = l),
          (l = m.next),
          (m.next = null),
          (o.lastBaseUpdate = m),
          (o.shared.pending = null));
      }
    } while (!0);
    if (
      (v === null && (a = f),
      (o.baseState = a),
      (o.firstBaseUpdate = u),
      (o.lastBaseUpdate = v),
      (t = o.shared.interleaved),
      t !== null)
    ) {
      o = t;
      do ((s |= o.lane), (o = o.next));
      while (o !== t);
    } else i === null && (o.shared.lanes = 0);
    ((Nn |= s), (e.lanes = s), (e.memoizedState = f));
  }
}
function Gu(e, t, n) {
  if (((e = t.effects), (t.effects = null), e !== null))
    for (t = 0; t < e.length; t++) {
      var r = e[t],
        o = r.callback;
      if (o !== null) {
        if (((r.callback = null), (r = n), typeof o != "function"))
          throw Error(R(191, o));
        o.call(r);
      }
    }
}
var So = {},
  xt = un(So),
  io = un(So),
  so = un(So);
function vn(e) {
  if (e === So) throw Error(R(174));
  return e;
}
function Sa(e, t) {
  switch ((Q(so, t), Q(io, e), Q(xt, So), (e = t.nodeType), e)) {
    case 9:
    case 11:
      t = (t = t.documentElement) ? t.namespaceURI : nl(null, "");
      break;
    default:
      ((e = e === 8 ? t.parentNode : t),
        (t = e.namespaceURI || null),
        (e = e.tagName),
        (t = nl(t, e)));
  }
  (X(xt), Q(xt, t));
}
function mr() {
  (X(xt), X(io), X(so));
}
function Ef(e) {
  vn(so.current);
  var t = vn(xt.current),
    n = nl(t, e.type);
  t !== n && (Q(io, e), Q(xt, n));
}
function Ea(e) {
  io.current === e && (X(xt), X(io));
}
var q = un(0);
function Ni(e) {
  for (var t = e; t !== null; ) {
    if (t.tag === 13) {
      var n = t.memoizedState;
      if (
        n !== null &&
        ((n = n.dehydrated), n === null || n.data === "$?" || n.data === "$!")
      )
        return t;
    } else if (t.tag === 19 && t.memoizedProps.revealOrder !== void 0) {
      if (t.flags & 128) return t;
    } else if (t.child !== null) {
      ((t.child.return = t), (t = t.child));
      continue;
    }
    if (t === e) break;
    for (; t.sibling === null; ) {
      if (t.return === null || t.return === e) return null;
      t = t.return;
    }
    ((t.sibling.return = t.return), (t = t.sibling));
  }
  return null;
}
var Ps = [];
function Ca() {
  for (var e = 0; e < Ps.length; e++)
    Ps[e]._workInProgressVersionPrimary = null;
  Ps.length = 0;
}
var ei = Dt.ReactCurrentDispatcher,
  Ns = Dt.ReactCurrentBatchConfig,
  Pn = 0,
  Z = null,
  ae = null,
  fe = null,
  Ti = !1,
  Vr = !1,
  lo = 0,
  Jv = 0;
function we() {
  throw Error(R(321));
}
function ka(e, t) {
  if (t === null) return !1;
  for (var n = 0; n < t.length && n < e.length; n++)
    if (!at(e[n], t[n])) return !1;
  return !0;
}
function Pa(e, t, n, r, o, i) {
  if (
    ((Pn = i),
    (Z = t),
    (t.memoizedState = null),
    (t.updateQueue = null),
    (t.lanes = 0),
    (ei.current = e === null || e.memoizedState === null ? rg : og),
    (e = n(r, o)),
    Vr)
  ) {
    i = 0;
    do {
      if (((Vr = !1), (lo = 0), 25 <= i)) throw Error(R(301));
      ((i += 1),
        (fe = ae = null),
        (t.updateQueue = null),
        (ei.current = ig),
        (e = n(r, o)));
    } while (Vr);
  }
  if (
    ((ei.current = Ri),
    (t = ae !== null && ae.next !== null),
    (Pn = 0),
    (fe = ae = Z = null),
    (Ti = !1),
    t)
  )
    throw Error(R(300));
  return e;
}
function Na() {
  var e = lo !== 0;
  return ((lo = 0), e);
}
function pt() {
  var e = {
    memoizedState: null,
    baseState: null,
    baseQueue: null,
    queue: null,
    next: null,
  };
  return (fe === null ? (Z.memoizedState = fe = e) : (fe = fe.next = e), fe);
}
function Je() {
  if (ae === null) {
    var e = Z.alternate;
    e = e !== null ? e.memoizedState : null;
  } else e = ae.next;
  var t = fe === null ? Z.memoizedState : fe.next;
  if (t !== null) ((fe = t), (ae = e));
  else {
    if (e === null) throw Error(R(310));
    ((ae = e),
      (e = {
        memoizedState: ae.memoizedState,
        baseState: ae.baseState,
        baseQueue: ae.baseQueue,
        queue: ae.queue,
        next: null,
      }),
      fe === null ? (Z.memoizedState = fe = e) : (fe = fe.next = e));
  }
  return fe;
}
function ao(e, t) {
  return typeof t == "function" ? t(e) : t;
}
function Ts(e) {
  var t = Je(),
    n = t.queue;
  if (n === null) throw Error(R(311));
  n.lastRenderedReducer = e;
  var r = ae,
    o = r.baseQueue,
    i = n.pending;
  if (i !== null) {
    if (o !== null) {
      var s = o.next;
      ((o.next = i.next), (i.next = s));
    }
    ((r.baseQueue = o = i), (n.pending = null));
  }
  if (o !== null) {
    ((i = o.next), (r = r.baseState));
    var l = (s = null),
      a = null,
      u = i;
    do {
      var v = u.lane;
      if ((Pn & v) === v)
        (a !== null &&
          (a = a.next =
            {
              lane: 0,
              action: u.action,
              hasEagerState: u.hasEagerState,
              eagerState: u.eagerState,
              next: null,
            }),
          (r = u.hasEagerState ? u.eagerState : e(r, u.action)));
      else {
        var f = {
          lane: v,
          action: u.action,
          hasEagerState: u.hasEagerState,
          eagerState: u.eagerState,
          next: null,
        };
        (a === null ? ((l = a = f), (s = r)) : (a = a.next = f),
          (Z.lanes |= v),
          (Nn |= v));
      }
      u = u.next;
    } while (u !== null && u !== i);
    (a === null ? (s = r) : (a.next = l),
      at(r, t.memoizedState) || (be = !0),
      (t.memoizedState = r),
      (t.baseState = s),
      (t.baseQueue = a),
      (n.lastRenderedState = r));
  }
  if (((e = n.interleaved), e !== null)) {
    o = e;
    do ((i = o.lane), (Z.lanes |= i), (Nn |= i), (o = o.next));
    while (o !== e);
  } else o === null && (n.lanes = 0);
  return [t.memoizedState, n.dispatch];
}
function Rs(e) {
  var t = Je(),
    n = t.queue;
  if (n === null) throw Error(R(311));
  n.lastRenderedReducer = e;
  var r = n.dispatch,
    o = n.pending,
    i = t.memoizedState;
  if (o !== null) {
    n.pending = null;
    var s = (o = o.next);
    do ((i = e(i, s.action)), (s = s.next));
    while (s !== o);
    (at(i, t.memoizedState) || (be = !0),
      (t.memoizedState = i),
      t.baseQueue === null && (t.baseState = i),
      (n.lastRenderedState = i));
  }
  return [i, r];
}
function Cf() {}
function kf(e, t) {
  var n = Z,
    r = Je(),
    o = t(),
    i = !at(r.memoizedState, o);
  if (
    (i && ((r.memoizedState = o), (be = !0)),
    (r = r.queue),
    Ta(Tf.bind(null, n, r, e), [e]),
    r.getSnapshot !== t || i || (fe !== null && fe.memoizedState.tag & 1))
  ) {
    if (
      ((n.flags |= 2048),
      uo(9, Nf.bind(null, n, r, o, t), void 0, null),
      he === null)
    )
      throw Error(R(349));
    Pn & 30 || Pf(n, t, o);
  }
  return o;
}
function Pf(e, t, n) {
  ((e.flags |= 16384),
    (e = { getSnapshot: t, value: n }),
    (t = Z.updateQueue),
    t === null
      ? ((t = { lastEffect: null, stores: null }),
        (Z.updateQueue = t),
        (t.stores = [e]))
      : ((n = t.stores), n === null ? (t.stores = [e]) : n.push(e)));
}
function Nf(e, t, n, r) {
  ((t.value = n), (t.getSnapshot = r), Rf(t) && _f(e));
}
function Tf(e, t, n) {
  return n(function () {
    Rf(t) && _f(e);
  });
}
function Rf(e) {
  var t = e.getSnapshot;
  e = e.value;
  try {
    var n = t();
    return !at(e, n);
  } catch {
    return !0;
  }
}
function _f(e) {
  var t = Ot(e, 1);
  t !== null && lt(t, e, 1, -1);
}
function Xu(e) {
  var t = pt();
  return (
    typeof e == "function" && (e = e()),
    (t.memoizedState = t.baseState = e),
    (e = {
      pending: null,
      interleaved: null,
      lanes: 0,
      dispatch: null,
      lastRenderedReducer: ao,
      lastRenderedState: e,
    }),
    (t.queue = e),
    (e = e.dispatch = ng.bind(null, Z, e)),
    [t.memoizedState, e]
  );
}
function uo(e, t, n, r) {
  return (
    (e = { tag: e, create: t, destroy: n, deps: r, next: null }),
    (t = Z.updateQueue),
    t === null
      ? ((t = { lastEffect: null, stores: null }),
        (Z.updateQueue = t),
        (t.lastEffect = e.next = e))
      : ((n = t.lastEffect),
        n === null
          ? (t.lastEffect = e.next = e)
          : ((r = n.next), (n.next = e), (e.next = r), (t.lastEffect = e))),
    e
  );
}
function Mf() {
  return Je().memoizedState;
}
function ti(e, t, n, r) {
  var o = pt();
  ((Z.flags |= e),
    (o.memoizedState = uo(1 | t, n, void 0, r === void 0 ? null : r)));
}
function Bi(e, t, n, r) {
  var o = Je();
  r = r === void 0 ? null : r;
  var i = void 0;
  if (ae !== null) {
    var s = ae.memoizedState;
    if (((i = s.destroy), r !== null && ka(r, s.deps))) {
      o.memoizedState = uo(t, n, i, r);
      return;
    }
  }
  ((Z.flags |= e), (o.memoizedState = uo(1 | t, n, i, r)));
}
function Yu(e, t) {
  return ti(8390656, 8, e, t);
}
function Ta(e, t) {
  return Bi(2048, 8, e, t);
}
function Of(e, t) {
  return Bi(4, 2, e, t);
}
function jf(e, t) {
  return Bi(4, 4, e, t);
}
function Df(e, t) {
  if (typeof t == "function")
    return (
      (e = e()),
      t(e),
      function () {
        t(null);
      }
    );
  if (t != null)
    return (
      (e = e()),
      (t.current = e),
      function () {
        t.current = null;
      }
    );
}
function bf(e, t, n) {
  return (
    (n = n != null ? n.concat([e]) : null),
    Bi(4, 4, Df.bind(null, t, e), n)
  );
}
function Ra() {}
function If(e, t) {
  var n = Je();
  t = t === void 0 ? null : t;
  var r = n.memoizedState;
  return r !== null && t !== null && ka(t, r[1])
    ? r[0]
    : ((n.memoizedState = [e, t]), e);
}
function Lf(e, t) {
  var n = Je();
  t = t === void 0 ? null : t;
  var r = n.memoizedState;
  return r !== null && t !== null && ka(t, r[1])
    ? r[0]
    : ((e = e()), (n.memoizedState = [e, t]), e);
}
function Af(e, t, n) {
  return Pn & 21
    ? (at(n, t) || ((n = Ud()), (Z.lanes |= n), (Nn |= n), (e.baseState = !0)),
      t)
    : (e.baseState && ((e.baseState = !1), (be = !0)), (e.memoizedState = n));
}
function eg(e, t) {
  var n = B;
  ((B = n !== 0 && 4 > n ? n : 4), e(!0));
  var r = Ns.transition;
  Ns.transition = {};
  try {
    (e(!1), t());
  } finally {
    ((B = n), (Ns.transition = r));
  }
}
function Ff() {
  return Je().memoizedState;
}
function tg(e, t, n) {
  var r = rn(e);
  if (
    ((n = {
      lane: r,
      action: n,
      hasEagerState: !1,
      eagerState: null,
      next: null,
    }),
    zf(e))
  )
    $f(t, n);
  else if (((n = xf(e, t, n, r)), n !== null)) {
    var o = _e();
    (lt(n, e, r, o), Uf(n, t, r));
  }
}
function ng(e, t, n) {
  var r = rn(e),
    o = { lane: r, action: n, hasEagerState: !1, eagerState: null, next: null };
  if (zf(e)) $f(t, o);
  else {
    var i = e.alternate;
    if (
      e.lanes === 0 &&
      (i === null || i.lanes === 0) &&
      ((i = t.lastRenderedReducer), i !== null)
    )
      try {
        var s = t.lastRenderedState,
          l = i(s, n);
        if (((o.hasEagerState = !0), (o.eagerState = l), at(l, s))) {
          var a = t.interleaved;
          (a === null
            ? ((o.next = o), wa(t))
            : ((o.next = a.next), (a.next = o)),
            (t.interleaved = o));
          return;
        }
      } catch {
      } finally {
      }
    ((n = xf(e, t, o, r)),
      n !== null && ((o = _e()), lt(n, e, r, o), Uf(n, t, r)));
  }
}
function zf(e) {
  var t = e.alternate;
  return e === Z || (t !== null && t === Z);
}
function $f(e, t) {
  Vr = Ti = !0;
  var n = e.pending;
  (n === null ? (t.next = t) : ((t.next = n.next), (n.next = t)),
    (e.pending = t));
}
function Uf(e, t, n) {
  if (n & 4194240) {
    var r = t.lanes;
    ((r &= e.pendingLanes), (n |= r), (t.lanes = n), ia(e, n));
  }
}
var Ri = {
    readContext: Ze,
    useCallback: we,
    useContext: we,
    useEffect: we,
    useImperativeHandle: we,
    useInsertionEffect: we,
    useLayoutEffect: we,
    useMemo: we,
    useReducer: we,
    useRef: we,
    useState: we,
    useDebugValue: we,
    useDeferredValue: we,
    useTransition: we,
    useMutableSource: we,
    useSyncExternalStore: we,
    useId: we,
    unstable_isNewReconciler: !1,
  },
  rg = {
    readContext: Ze,
    useCallback: function (e, t) {
      return ((pt().memoizedState = [e, t === void 0 ? null : t]), e);
    },
    useContext: Ze,
    useEffect: Yu,
    useImperativeHandle: function (e, t, n) {
      return (
        (n = n != null ? n.concat([e]) : null),
        ti(4194308, 4, Df.bind(null, t, e), n)
      );
    },
    useLayoutEffect: function (e, t) {
      return ti(4194308, 4, e, t);
    },
    useInsertionEffect: function (e, t) {
      return ti(4, 2, e, t);
    },
    useMemo: function (e, t) {
      var n = pt();
      return (
        (t = t === void 0 ? null : t),
        (e = e()),
        (n.memoizedState = [e, t]),
        e
      );
    },
    useReducer: function (e, t, n) {
      var r = pt();
      return (
        (t = n !== void 0 ? n(t) : t),
        (r.memoizedState = r.baseState = t),
        (e = {
          pending: null,
          interleaved: null,
          lanes: 0,
          dispatch: null,
          lastRenderedReducer: e,
          lastRenderedState: t,
        }),
        (r.queue = e),
        (e = e.dispatch = tg.bind(null, Z, e)),
        [r.memoizedState, e]
      );
    },
    useRef: function (e) {
      var t = pt();
      return ((e = { current: e }), (t.memoizedState = e));
    },
    useState: Xu,
    useDebugValue: Ra,
    useDeferredValue: function (e) {
      return (pt().memoizedState = e);
    },
    useTransition: function () {
      var e = Xu(!1),
        t = e[0];
      return ((e = eg.bind(null, e[1])), (pt().memoizedState = e), [t, e]);
    },
    useMutableSource: function () {},
    useSyncExternalStore: function (e, t, n) {
      var r = Z,
        o = pt();
      if (Y) {
        if (n === void 0) throw Error(R(407));
        n = n();
      } else {
        if (((n = t()), he === null)) throw Error(R(349));
        Pn & 30 || Pf(r, t, n);
      }
      o.memoizedState = n;
      var i = { value: n, getSnapshot: t };
      return (
        (o.queue = i),
        Yu(Tf.bind(null, r, i, e), [e]),
        (r.flags |= 2048),
        uo(9, Nf.bind(null, r, i, n, t), void 0, null),
        n
      );
    },
    useId: function () {
      var e = pt(),
        t = he.identifierPrefix;
      if (Y) {
        var n = Tt,
          r = Nt;
        ((n = (r & ~(1 << (32 - st(r) - 1))).toString(32) + n),
          (t = ":" + t + "R" + n),
          (n = lo++),
          0 < n && (t += "H" + n.toString(32)),
          (t += ":"));
      } else ((n = Jv++), (t = ":" + t + "r" + n.toString(32) + ":"));
      return (e.memoizedState = t);
    },
    unstable_isNewReconciler: !1,
  },
  og = {
    readContext: Ze,
    useCallback: If,
    useContext: Ze,
    useEffect: Ta,
    useImperativeHandle: bf,
    useInsertionEffect: Of,
    useLayoutEffect: jf,
    useMemo: Lf,
    useReducer: Ts,
    useRef: Mf,
    useState: function () {
      return Ts(ao);
    },
    useDebugValue: Ra,
    useDeferredValue: function (e) {
      var t = Je();
      return Af(t, ae.memoizedState, e);
    },
    useTransition: function () {
      var e = Ts(ao)[0],
        t = Je().memoizedState;
      return [e, t];
    },
    useMutableSource: Cf,
    useSyncExternalStore: kf,
    useId: Ff,
    unstable_isNewReconciler: !1,
  },
  ig = {
    readContext: Ze,
    useCallback: If,
    useContext: Ze,
    useEffect: Ta,
    useImperativeHandle: bf,
    useInsertionEffect: Of,
    useLayoutEffect: jf,
    useMemo: Lf,
    useReducer: Rs,
    useRef: Mf,
    useState: function () {
      return Rs(ao);
    },
    useDebugValue: Ra,
    useDeferredValue: function (e) {
      var t = Je();
      return ae === null ? (t.memoizedState = e) : Af(t, ae.memoizedState, e);
    },
    useTransition: function () {
      var e = Rs(ao)[0],
        t = Je().memoizedState;
      return [e, t];
    },
    useMutableSource: Cf,
    useSyncExternalStore: kf,
    useId: Ff,
    unstable_isNewReconciler: !1,
  };
function tt(e, t) {
  if (e && e.defaultProps) {
    ((t = J({}, t)), (e = e.defaultProps));
    for (var n in e) t[n] === void 0 && (t[n] = e[n]);
    return t;
  }
  return t;
}
function Cl(e, t, n, r) {
  ((t = e.memoizedState),
    (n = n(r, t)),
    (n = n == null ? t : J({}, t, n)),
    (e.memoizedState = n),
    e.lanes === 0 && (e.updateQueue.baseState = n));
}
var Hi = {
  isMounted: function (e) {
    return (e = e._reactInternals) ? Mn(e) === e : !1;
  },
  enqueueSetState: function (e, t, n) {
    e = e._reactInternals;
    var r = _e(),
      o = rn(e),
      i = Rt(r, o);
    ((i.payload = t),
      n != null && (i.callback = n),
      (t = tn(e, i, o)),
      t !== null && (lt(t, e, o, r), Jo(t, e, o)));
  },
  enqueueReplaceState: function (e, t, n) {
    e = e._reactInternals;
    var r = _e(),
      o = rn(e),
      i = Rt(r, o);
    ((i.tag = 1),
      (i.payload = t),
      n != null && (i.callback = n),
      (t = tn(e, i, o)),
      t !== null && (lt(t, e, o, r), Jo(t, e, o)));
  },
  enqueueForceUpdate: function (e, t) {
    e = e._reactInternals;
    var n = _e(),
      r = rn(e),
      o = Rt(n, r);
    ((o.tag = 2),
      t != null && (o.callback = t),
      (t = tn(e, o, r)),
      t !== null && (lt(t, e, r, n), Jo(t, e, r)));
  },
};
function qu(e, t, n, r, o, i, s) {
  return (
    (e = e.stateNode),
    typeof e.shouldComponentUpdate == "function"
      ? e.shouldComponentUpdate(r, i, s)
      : t.prototype && t.prototype.isPureReactComponent
        ? !to(n, r) || !to(o, i)
        : !0
  );
}
function Wf(e, t, n) {
  var r = !1,
    o = ln,
    i = t.contextType;
  return (
    typeof i == "object" && i !== null
      ? (i = Ze(i))
      : ((o = Le(t) ? Cn : Ce.current),
        (r = t.contextTypes),
        (i = (r = r != null) ? fr(e, o) : ln)),
    (t = new t(n, i)),
    (e.memoizedState = t.state !== null && t.state !== void 0 ? t.state : null),
    (t.updater = Hi),
    (e.stateNode = t),
    (t._reactInternals = e),
    r &&
      ((e = e.stateNode),
      (e.__reactInternalMemoizedUnmaskedChildContext = o),
      (e.__reactInternalMemoizedMaskedChildContext = i)),
    t
  );
}
function Zu(e, t, n, r) {
  ((e = t.state),
    typeof t.componentWillReceiveProps == "function" &&
      t.componentWillReceiveProps(n, r),
    typeof t.UNSAFE_componentWillReceiveProps == "function" &&
      t.UNSAFE_componentWillReceiveProps(n, r),
    t.state !== e && Hi.enqueueReplaceState(t, t.state, null));
}
function kl(e, t, n, r) {
  var o = e.stateNode;
  ((o.props = n), (o.state = e.memoizedState), (o.refs = {}), xa(e));
  var i = t.contextType;
  (typeof i == "object" && i !== null
    ? (o.context = Ze(i))
    : ((i = Le(t) ? Cn : Ce.current), (o.context = fr(e, i))),
    (o.state = e.memoizedState),
    (i = t.getDerivedStateFromProps),
    typeof i == "function" && (Cl(e, t, i, n), (o.state = e.memoizedState)),
    typeof t.getDerivedStateFromProps == "function" ||
      typeof o.getSnapshotBeforeUpdate == "function" ||
      (typeof o.UNSAFE_componentWillMount != "function" &&
        typeof o.componentWillMount != "function") ||
      ((t = o.state),
      typeof o.componentWillMount == "function" && o.componentWillMount(),
      typeof o.UNSAFE_componentWillMount == "function" &&
        o.UNSAFE_componentWillMount(),
      t !== o.state && Hi.enqueueReplaceState(o, o.state, null),
      Pi(e, n, o, r),
      (o.state = e.memoizedState)),
    typeof o.componentDidMount == "function" && (e.flags |= 4194308));
}
function vr(e, t) {
  try {
    var n = "",
      r = t;
    do ((n += Dm(r)), (r = r.return));
    while (r);
    var o = n;
  } catch (i) {
    o =
      `
Error generating stack: ` +
      i.message +
      `
` +
      i.stack;
  }
  return { value: e, source: t, stack: o, digest: null };
}
function _s(e, t, n) {
  return { value: e, source: null, stack: n ?? null, digest: t ?? null };
}
function Pl(e, t) {
  try {
    console.error(t.value);
  } catch (n) {
    setTimeout(function () {
      throw n;
    });
  }
}
var sg = typeof WeakMap == "function" ? WeakMap : Map;
function Vf(e, t, n) {
  ((n = Rt(-1, n)), (n.tag = 3), (n.payload = { element: null }));
  var r = t.value;
  return (
    (n.callback = function () {
      (Mi || ((Mi = !0), (Il = r)), Pl(e, t));
    }),
    n
  );
}
function Bf(e, t, n) {
  ((n = Rt(-1, n)), (n.tag = 3));
  var r = e.type.getDerivedStateFromError;
  if (typeof r == "function") {
    var o = t.value;
    ((n.payload = function () {
      return r(o);
    }),
      (n.callback = function () {
        Pl(e, t);
      }));
  }
  var i = e.stateNode;
  return (
    i !== null &&
      typeof i.componentDidCatch == "function" &&
      (n.callback = function () {
        (Pl(e, t),
          typeof r != "function" &&
            (nn === null ? (nn = new Set([this])) : nn.add(this)));
        var s = t.stack;
        this.componentDidCatch(t.value, {
          componentStack: s !== null ? s : "",
        });
      }),
    n
  );
}
function Ju(e, t, n) {
  var r = e.pingCache;
  if (r === null) {
    r = e.pingCache = new sg();
    var o = new Set();
    r.set(t, o);
  } else ((o = r.get(t)), o === void 0 && ((o = new Set()), r.set(t, o)));
  o.has(n) || (o.add(n), (e = xg.bind(null, e, t, n)), t.then(e, e));
}
function ec(e) {
  do {
    var t;
    if (
      ((t = e.tag === 13) &&
        ((t = e.memoizedState), (t = t !== null ? t.dehydrated !== null : !0)),
      t)
    )
      return e;
    e = e.return;
  } while (e !== null);
  return null;
}
function tc(e, t, n, r, o) {
  return e.mode & 1
    ? ((e.flags |= 65536), (e.lanes = o), e)
    : (e === t
        ? (e.flags |= 65536)
        : ((e.flags |= 128),
          (n.flags |= 131072),
          (n.flags &= -52805),
          n.tag === 1 &&
            (n.alternate === null
              ? (n.tag = 17)
              : ((t = Rt(-1, 1)), (t.tag = 2), tn(n, t, 1))),
          (n.lanes |= 1)),
      e);
}
var lg = Dt.ReactCurrentOwner,
  be = !1;
function Te(e, t, n, r) {
  t.child = e === null ? wf(t, null, n, r) : hr(t, e.child, n, r);
}
function nc(e, t, n, r, o) {
  n = n.render;
  var i = t.ref;
  return (
    Zn(t, o),
    (r = Pa(e, t, n, r, i, o)),
    (n = Na()),
    e !== null && !be
      ? ((t.updateQueue = e.updateQueue),
        (t.flags &= -2053),
        (e.lanes &= ~o),
        jt(e, t, o))
      : (Y && n && pa(t), (t.flags |= 1), Te(e, t, r, o), t.child)
  );
}
function rc(e, t, n, r, o) {
  if (e === null) {
    var i = n.type;
    return typeof i == "function" &&
      !La(i) &&
      i.defaultProps === void 0 &&
      n.compare === null &&
      n.defaultProps === void 0
      ? ((t.tag = 15), (t.type = i), Hf(e, t, i, r, o))
      : ((e = ii(n.type, null, r, t, t.mode, o)),
        (e.ref = t.ref),
        (e.return = t),
        (t.child = e));
  }
  if (((i = e.child), !(e.lanes & o))) {
    var s = i.memoizedProps;
    if (
      ((n = n.compare), (n = n !== null ? n : to), n(s, r) && e.ref === t.ref)
    )
      return jt(e, t, o);
  }
  return (
    (t.flags |= 1),
    (e = on(i, r)),
    (e.ref = t.ref),
    (e.return = t),
    (t.child = e)
  );
}
function Hf(e, t, n, r, o) {
  if (e !== null) {
    var i = e.memoizedProps;
    if (to(i, r) && e.ref === t.ref)
      if (((be = !1), (t.pendingProps = r = i), (e.lanes & o) !== 0))
        e.flags & 131072 && (be = !0);
      else return ((t.lanes = e.lanes), jt(e, t, o));
  }
  return Nl(e, t, n, r, o);
}
function Qf(e, t, n) {
  var r = t.pendingProps,
    o = r.children,
    i = e !== null ? e.memoizedState : null;
  if (r.mode === "hidden")
    if (!(t.mode & 1))
      ((t.memoizedState = { baseLanes: 0, cachePool: null, transitions: null }),
        Q(Kn, ze),
        (ze |= n));
    else {
      if (!(n & 1073741824))
        return (
          (e = i !== null ? i.baseLanes | n : n),
          (t.lanes = t.childLanes = 1073741824),
          (t.memoizedState = {
            baseLanes: e,
            cachePool: null,
            transitions: null,
          }),
          (t.updateQueue = null),
          Q(Kn, ze),
          (ze |= e),
          null
        );
      ((t.memoizedState = { baseLanes: 0, cachePool: null, transitions: null }),
        (r = i !== null ? i.baseLanes : n),
        Q(Kn, ze),
        (ze |= r));
    }
  else
    (i !== null ? ((r = i.baseLanes | n), (t.memoizedState = null)) : (r = n),
      Q(Kn, ze),
      (ze |= r));
  return (Te(e, t, o, n), t.child);
}
function Kf(e, t) {
  var n = t.ref;
  ((e === null && n !== null) || (e !== null && e.ref !== n)) &&
    ((t.flags |= 512), (t.flags |= 2097152));
}
function Nl(e, t, n, r, o) {
  var i = Le(n) ? Cn : Ce.current;
  return (
    (i = fr(t, i)),
    Zn(t, o),
    (n = Pa(e, t, n, r, i, o)),
    (r = Na()),
    e !== null && !be
      ? ((t.updateQueue = e.updateQueue),
        (t.flags &= -2053),
        (e.lanes &= ~o),
        jt(e, t, o))
      : (Y && r && pa(t), (t.flags |= 1), Te(e, t, n, o), t.child)
  );
}
function oc(e, t, n, r, o) {
  if (Le(n)) {
    var i = !0;
    xi(t);
  } else i = !1;
  if ((Zn(t, o), t.stateNode === null))
    (ni(e, t), Wf(t, n, r), kl(t, n, r, o), (r = !0));
  else if (e === null) {
    var s = t.stateNode,
      l = t.memoizedProps;
    s.props = l;
    var a = s.context,
      u = n.contextType;
    typeof u == "object" && u !== null
      ? (u = Ze(u))
      : ((u = Le(n) ? Cn : Ce.current), (u = fr(t, u)));
    var v = n.getDerivedStateFromProps,
      f =
        typeof v == "function" ||
        typeof s.getSnapshotBeforeUpdate == "function";
    (f ||
      (typeof s.UNSAFE_componentWillReceiveProps != "function" &&
        typeof s.componentWillReceiveProps != "function") ||
      ((l !== r || a !== u) && Zu(t, s, r, u)),
      (Ut = !1));
    var m = t.memoizedState;
    ((s.state = m),
      Pi(t, r, s, o),
      (a = t.memoizedState),
      l !== r || m !== a || Ie.current || Ut
        ? (typeof v == "function" && (Cl(t, n, v, r), (a = t.memoizedState)),
          (l = Ut || qu(t, n, l, r, m, a, u))
            ? (f ||
                (typeof s.UNSAFE_componentWillMount != "function" &&
                  typeof s.componentWillMount != "function") ||
                (typeof s.componentWillMount == "function" &&
                  s.componentWillMount(),
                typeof s.UNSAFE_componentWillMount == "function" &&
                  s.UNSAFE_componentWillMount()),
              typeof s.componentDidMount == "function" && (t.flags |= 4194308))
            : (typeof s.componentDidMount == "function" && (t.flags |= 4194308),
              (t.memoizedProps = r),
              (t.memoizedState = a)),
          (s.props = r),
          (s.state = a),
          (s.context = u),
          (r = l))
        : (typeof s.componentDidMount == "function" && (t.flags |= 4194308),
          (r = !1)));
  } else {
    ((s = t.stateNode),
      Sf(e, t),
      (l = t.memoizedProps),
      (u = t.type === t.elementType ? l : tt(t.type, l)),
      (s.props = u),
      (f = t.pendingProps),
      (m = s.context),
      (a = n.contextType),
      typeof a == "object" && a !== null
        ? (a = Ze(a))
        : ((a = Le(n) ? Cn : Ce.current), (a = fr(t, a))));
    var x = n.getDerivedStateFromProps;
    ((v =
      typeof x == "function" ||
      typeof s.getSnapshotBeforeUpdate == "function") ||
      (typeof s.UNSAFE_componentWillReceiveProps != "function" &&
        typeof s.componentWillReceiveProps != "function") ||
      ((l !== f || m !== a) && Zu(t, s, r, a)),
      (Ut = !1),
      (m = t.memoizedState),
      (s.state = m),
      Pi(t, r, s, o));
    var E = t.memoizedState;
    l !== f || m !== E || Ie.current || Ut
      ? (typeof x == "function" && (Cl(t, n, x, r), (E = t.memoizedState)),
        (u = Ut || qu(t, n, u, r, m, E, a) || !1)
          ? (v ||
              (typeof s.UNSAFE_componentWillUpdate != "function" &&
                typeof s.componentWillUpdate != "function") ||
              (typeof s.componentWillUpdate == "function" &&
                s.componentWillUpdate(r, E, a),
              typeof s.UNSAFE_componentWillUpdate == "function" &&
                s.UNSAFE_componentWillUpdate(r, E, a)),
            typeof s.componentDidUpdate == "function" && (t.flags |= 4),
            typeof s.getSnapshotBeforeUpdate == "function" && (t.flags |= 1024))
          : (typeof s.componentDidUpdate != "function" ||
              (l === e.memoizedProps && m === e.memoizedState) ||
              (t.flags |= 4),
            typeof s.getSnapshotBeforeUpdate != "function" ||
              (l === e.memoizedProps && m === e.memoizedState) ||
              (t.flags |= 1024),
            (t.memoizedProps = r),
            (t.memoizedState = E)),
        (s.props = r),
        (s.state = E),
        (s.context = a),
        (r = u))
      : (typeof s.componentDidUpdate != "function" ||
          (l === e.memoizedProps && m === e.memoizedState) ||
          (t.flags |= 4),
        typeof s.getSnapshotBeforeUpdate != "function" ||
          (l === e.memoizedProps && m === e.memoizedState) ||
          (t.flags |= 1024),
        (r = !1));
  }
  return Tl(e, t, n, r, i, o);
}
function Tl(e, t, n, r, o, i) {
  Kf(e, t);
  var s = (t.flags & 128) !== 0;
  if (!r && !s) return (o && Vu(t, n, !1), jt(e, t, i));
  ((r = t.stateNode), (lg.current = t));
  var l =
    s && typeof n.getDerivedStateFromError != "function" ? null : r.render();
  return (
    (t.flags |= 1),
    e !== null && s
      ? ((t.child = hr(t, e.child, null, i)), (t.child = hr(t, null, l, i)))
      : Te(e, t, l, i),
    (t.memoizedState = r.state),
    o && Vu(t, n, !0),
    t.child
  );
}
function Gf(e) {
  var t = e.stateNode;
  (t.pendingContext
    ? Wu(e, t.pendingContext, t.pendingContext !== t.context)
    : t.context && Wu(e, t.context, !1),
    Sa(e, t.containerInfo));
}
function ic(e, t, n, r, o) {
  return (pr(), ma(o), (t.flags |= 256), Te(e, t, n, r), t.child);
}
var Rl = { dehydrated: null, treeContext: null, retryLane: 0 };
function _l(e) {
  return { baseLanes: e, cachePool: null, transitions: null };
}
function Xf(e, t, n) {
  var r = t.pendingProps,
    o = q.current,
    i = !1,
    s = (t.flags & 128) !== 0,
    l;
  if (
    ((l = s) ||
      (l = e !== null && e.memoizedState === null ? !1 : (o & 2) !== 0),
    l
      ? ((i = !0), (t.flags &= -129))
      : (e === null || e.memoizedState !== null) && (o |= 1),
    Q(q, o & 1),
    e === null)
  )
    return (
      Sl(t),
      (e = t.memoizedState),
      e !== null && ((e = e.dehydrated), e !== null)
        ? (t.mode & 1
            ? e.data === "$!"
              ? (t.lanes = 8)
              : (t.lanes = 1073741824)
            : (t.lanes = 1),
          null)
        : ((s = r.children),
          (e = r.fallback),
          i
            ? ((r = t.mode),
              (i = t.child),
              (s = { mode: "hidden", children: s }),
              !(r & 1) && i !== null
                ? ((i.childLanes = 0), (i.pendingProps = s))
                : (i = Gi(s, r, 0, null)),
              (e = En(e, r, n, null)),
              (i.return = t),
              (e.return = t),
              (i.sibling = e),
              (t.child = i),
              (t.child.memoizedState = _l(n)),
              (t.memoizedState = Rl),
              e)
            : _a(t, s))
    );
  if (((o = e.memoizedState), o !== null && ((l = o.dehydrated), l !== null)))
    return ag(e, t, s, r, l, o, n);
  if (i) {
    ((i = r.fallback), (s = t.mode), (o = e.child), (l = o.sibling));
    var a = { mode: "hidden", children: r.children };
    return (
      !(s & 1) && t.child !== o
        ? ((r = t.child),
          (r.childLanes = 0),
          (r.pendingProps = a),
          (t.deletions = null))
        : ((r = on(o, a)), (r.subtreeFlags = o.subtreeFlags & 14680064)),
      l !== null ? (i = on(l, i)) : ((i = En(i, s, n, null)), (i.flags |= 2)),
      (i.return = t),
      (r.return = t),
      (r.sibling = i),
      (t.child = r),
      (r = i),
      (i = t.child),
      (s = e.child.memoizedState),
      (s =
        s === null
          ? _l(n)
          : {
              baseLanes: s.baseLanes | n,
              cachePool: null,
              transitions: s.transitions,
            }),
      (i.memoizedState = s),
      (i.childLanes = e.childLanes & ~n),
      (t.memoizedState = Rl),
      r
    );
  }
  return (
    (i = e.child),
    (e = i.sibling),
    (r = on(i, { mode: "visible", children: r.children })),
    !(t.mode & 1) && (r.lanes = n),
    (r.return = t),
    (r.sibling = null),
    e !== null &&
      ((n = t.deletions),
      n === null ? ((t.deletions = [e]), (t.flags |= 16)) : n.push(e)),
    (t.child = r),
    (t.memoizedState = null),
    r
  );
}
function _a(e, t) {
  return (
    (t = Gi({ mode: "visible", children: t }, e.mode, 0, null)),
    (t.return = e),
    (e.child = t)
  );
}
function Ao(e, t, n, r) {
  return (
    r !== null && ma(r),
    hr(t, e.child, null, n),
    (e = _a(t, t.pendingProps.children)),
    (e.flags |= 2),
    (t.memoizedState = null),
    e
  );
}
function ag(e, t, n, r, o, i, s) {
  if (n)
    return t.flags & 256
      ? ((t.flags &= -257), (r = _s(Error(R(422)))), Ao(e, t, s, r))
      : t.memoizedState !== null
        ? ((t.child = e.child), (t.flags |= 128), null)
        : ((i = r.fallback),
          (o = t.mode),
          (r = Gi({ mode: "visible", children: r.children }, o, 0, null)),
          (i = En(i, o, s, null)),
          (i.flags |= 2),
          (r.return = t),
          (i.return = t),
          (r.sibling = i),
          (t.child = r),
          t.mode & 1 && hr(t, e.child, null, s),
          (t.child.memoizedState = _l(s)),
          (t.memoizedState = Rl),
          i);
  if (!(t.mode & 1)) return Ao(e, t, s, null);
  if (o.data === "$!") {
    if (((r = o.nextSibling && o.nextSibling.dataset), r)) var l = r.dgst;
    return (
      (r = l),
      (i = Error(R(419))),
      (r = _s(i, r, void 0)),
      Ao(e, t, s, r)
    );
  }
  if (((l = (s & e.childLanes) !== 0), be || l)) {
    if (((r = he), r !== null)) {
      switch (s & -s) {
        case 4:
          o = 2;
          break;
        case 16:
          o = 8;
          break;
        case 64:
        case 128:
        case 256:
        case 512:
        case 1024:
        case 2048:
        case 4096:
        case 8192:
        case 16384:
        case 32768:
        case 65536:
        case 131072:
        case 262144:
        case 524288:
        case 1048576:
        case 2097152:
        case 4194304:
        case 8388608:
        case 16777216:
        case 33554432:
        case 67108864:
          o = 32;
          break;
        case 536870912:
          o = 268435456;
          break;
        default:
          o = 0;
      }
      ((o = o & (r.suspendedLanes | s) ? 0 : o),
        o !== 0 &&
          o !== i.retryLane &&
          ((i.retryLane = o), Ot(e, o), lt(r, e, o, -1)));
    }
    return (Ia(), (r = _s(Error(R(421)))), Ao(e, t, s, r));
  }
  return o.data === "$?"
    ? ((t.flags |= 128),
      (t.child = e.child),
      (t = Sg.bind(null, e)),
      (o._reactRetry = t),
      null)
    : ((e = i.treeContext),
      ($e = en(o.nextSibling)),
      (Ue = t),
      (Y = !0),
      (it = null),
      e !== null &&
        ((Ge[Xe++] = Nt),
        (Ge[Xe++] = Tt),
        (Ge[Xe++] = kn),
        (Nt = e.id),
        (Tt = e.overflow),
        (kn = t)),
      (t = _a(t, r.children)),
      (t.flags |= 4096),
      t);
}
function sc(e, t, n) {
  e.lanes |= t;
  var r = e.alternate;
  (r !== null && (r.lanes |= t), El(e.return, t, n));
}
function Ms(e, t, n, r, o) {
  var i = e.memoizedState;
  i === null
    ? (e.memoizedState = {
        isBackwards: t,
        rendering: null,
        renderingStartTime: 0,
        last: r,
        tail: n,
        tailMode: o,
      })
    : ((i.isBackwards = t),
      (i.rendering = null),
      (i.renderingStartTime = 0),
      (i.last = r),
      (i.tail = n),
      (i.tailMode = o));
}
function Yf(e, t, n) {
  var r = t.pendingProps,
    o = r.revealOrder,
    i = r.tail;
  if ((Te(e, t, r.children, n), (r = q.current), r & 2))
    ((r = (r & 1) | 2), (t.flags |= 128));
  else {
    if (e !== null && e.flags & 128)
      e: for (e = t.child; e !== null; ) {
        if (e.tag === 13) e.memoizedState !== null && sc(e, n, t);
        else if (e.tag === 19) sc(e, n, t);
        else if (e.child !== null) {
          ((e.child.return = e), (e = e.child));
          continue;
        }
        if (e === t) break e;
        for (; e.sibling === null; ) {
          if (e.return === null || e.return === t) break e;
          e = e.return;
        }
        ((e.sibling.return = e.return), (e = e.sibling));
      }
    r &= 1;
  }
  if ((Q(q, r), !(t.mode & 1))) t.memoizedState = null;
  else
    switch (o) {
      case "forwards":
        for (n = t.child, o = null; n !== null; )
          ((e = n.alternate),
            e !== null && Ni(e) === null && (o = n),
            (n = n.sibling));
        ((n = o),
          n === null
            ? ((o = t.child), (t.child = null))
            : ((o = n.sibling), (n.sibling = null)),
          Ms(t, !1, o, n, i));
        break;
      case "backwards":
        for (n = null, o = t.child, t.child = null; o !== null; ) {
          if (((e = o.alternate), e !== null && Ni(e) === null)) {
            t.child = o;
            break;
          }
          ((e = o.sibling), (o.sibling = n), (n = o), (o = e));
        }
        Ms(t, !0, n, null, i);
        break;
      case "together":
        Ms(t, !1, null, null, void 0);
        break;
      default:
        t.memoizedState = null;
    }
  return t.child;
}
function ni(e, t) {
  !(t.mode & 1) &&
    e !== null &&
    ((e.alternate = null), (t.alternate = null), (t.flags |= 2));
}
function jt(e, t, n) {
  if (
    (e !== null && (t.dependencies = e.dependencies),
    (Nn |= t.lanes),
    !(n & t.childLanes))
  )
    return null;
  if (e !== null && t.child !== e.child) throw Error(R(153));
  if (t.child !== null) {
    for (
      e = t.child, n = on(e, e.pendingProps), t.child = n, n.return = t;
      e.sibling !== null;

    )
      ((e = e.sibling),
        (n = n.sibling = on(e, e.pendingProps)),
        (n.return = t));
    n.sibling = null;
  }
  return t.child;
}
function ug(e, t, n) {
  switch (t.tag) {
    case 3:
      (Gf(t), pr());
      break;
    case 5:
      Ef(t);
      break;
    case 1:
      Le(t.type) && xi(t);
      break;
    case 4:
      Sa(t, t.stateNode.containerInfo);
      break;
    case 10:
      var r = t.type._context,
        o = t.memoizedProps.value;
      (Q(Ci, r._currentValue), (r._currentValue = o));
      break;
    case 13:
      if (((r = t.memoizedState), r !== null))
        return r.dehydrated !== null
          ? (Q(q, q.current & 1), (t.flags |= 128), null)
          : n & t.child.childLanes
            ? Xf(e, t, n)
            : (Q(q, q.current & 1),
              (e = jt(e, t, n)),
              e !== null ? e.sibling : null);
      Q(q, q.current & 1);
      break;
    case 19:
      if (((r = (n & t.childLanes) !== 0), e.flags & 128)) {
        if (r) return Yf(e, t, n);
        t.flags |= 128;
      }
      if (
        ((o = t.memoizedState),
        o !== null &&
          ((o.rendering = null), (o.tail = null), (o.lastEffect = null)),
        Q(q, q.current),
        r)
      )
        break;
      return null;
    case 22:
    case 23:
      return ((t.lanes = 0), Qf(e, t, n));
  }
  return jt(e, t, n);
}
var qf, Ml, Zf, Jf;
qf = function (e, t) {
  for (var n = t.child; n !== null; ) {
    if (n.tag === 5 || n.tag === 6) e.appendChild(n.stateNode);
    else if (n.tag !== 4 && n.child !== null) {
      ((n.child.return = n), (n = n.child));
      continue;
    }
    if (n === t) break;
    for (; n.sibling === null; ) {
      if (n.return === null || n.return === t) return;
      n = n.return;
    }
    ((n.sibling.return = n.return), (n = n.sibling));
  }
};
Ml = function () {};
Zf = function (e, t, n, r) {
  var o = e.memoizedProps;
  if (o !== r) {
    ((e = t.stateNode), vn(xt.current));
    var i = null;
    switch (n) {
      case "input":
        ((o = Zs(e, o)), (r = Zs(e, r)), (i = []));
        break;
      case "select":
        ((o = J({}, o, { value: void 0 })),
          (r = J({}, r, { value: void 0 })),
          (i = []));
        break;
      case "textarea":
        ((o = tl(e, o)), (r = tl(e, r)), (i = []));
        break;
      default:
        typeof o.onClick != "function" &&
          typeof r.onClick == "function" &&
          (e.onclick = yi);
    }
    rl(n, r);
    var s;
    n = null;
    for (u in o)
      if (!r.hasOwnProperty(u) && o.hasOwnProperty(u) && o[u] != null)
        if (u === "style") {
          var l = o[u];
          for (s in l) l.hasOwnProperty(s) && (n || (n = {}), (n[s] = ""));
        } else
          u !== "dangerouslySetInnerHTML" &&
            u !== "children" &&
            u !== "suppressContentEditableWarning" &&
            u !== "suppressHydrationWarning" &&
            u !== "autoFocus" &&
            (Gr.hasOwnProperty(u)
              ? i || (i = [])
              : (i = i || []).push(u, null));
    for (u in r) {
      var a = r[u];
      if (
        ((l = o != null ? o[u] : void 0),
        r.hasOwnProperty(u) && a !== l && (a != null || l != null))
      )
        if (u === "style")
          if (l) {
            for (s in l)
              !l.hasOwnProperty(s) ||
                (a && a.hasOwnProperty(s)) ||
                (n || (n = {}), (n[s] = ""));
            for (s in a)
              a.hasOwnProperty(s) &&
                l[s] !== a[s] &&
                (n || (n = {}), (n[s] = a[s]));
          } else (n || (i || (i = []), i.push(u, n)), (n = a));
        else
          u === "dangerouslySetInnerHTML"
            ? ((a = a ? a.__html : void 0),
              (l = l ? l.__html : void 0),
              a != null && l !== a && (i = i || []).push(u, a))
            : u === "children"
              ? (typeof a != "string" && typeof a != "number") ||
                (i = i || []).push(u, "" + a)
              : u !== "suppressContentEditableWarning" &&
                u !== "suppressHydrationWarning" &&
                (Gr.hasOwnProperty(u)
                  ? (a != null && u === "onScroll" && G("scroll", e),
                    i || l === a || (i = []))
                  : (i = i || []).push(u, a));
    }
    n && (i = i || []).push("style", n);
    var u = i;
    (t.updateQueue = u) && (t.flags |= 4);
  }
};
Jf = function (e, t, n, r) {
  n !== r && (t.flags |= 4);
};
function Mr(e, t) {
  if (!Y)
    switch (e.tailMode) {
      case "hidden":
        t = e.tail;
        for (var n = null; t !== null; )
          (t.alternate !== null && (n = t), (t = t.sibling));
        n === null ? (e.tail = null) : (n.sibling = null);
        break;
      case "collapsed":
        n = e.tail;
        for (var r = null; n !== null; )
          (n.alternate !== null && (r = n), (n = n.sibling));
        r === null
          ? t || e.tail === null
            ? (e.tail = null)
            : (e.tail.sibling = null)
          : (r.sibling = null);
    }
}
function xe(e) {
  var t = e.alternate !== null && e.alternate.child === e.child,
    n = 0,
    r = 0;
  if (t)
    for (var o = e.child; o !== null; )
      ((n |= o.lanes | o.childLanes),
        (r |= o.subtreeFlags & 14680064),
        (r |= o.flags & 14680064),
        (o.return = e),
        (o = o.sibling));
  else
    for (o = e.child; o !== null; )
      ((n |= o.lanes | o.childLanes),
        (r |= o.subtreeFlags),
        (r |= o.flags),
        (o.return = e),
        (o = o.sibling));
  return ((e.subtreeFlags |= r), (e.childLanes = n), t);
}
function cg(e, t, n) {
  var r = t.pendingProps;
  switch ((ha(t), t.tag)) {
    case 2:
    case 16:
    case 15:
    case 0:
    case 11:
    case 7:
    case 8:
    case 12:
    case 9:
    case 14:
      return (xe(t), null);
    case 1:
      return (Le(t.type) && wi(), xe(t), null);
    case 3:
      return (
        (r = t.stateNode),
        mr(),
        X(Ie),
        X(Ce),
        Ca(),
        r.pendingContext &&
          ((r.context = r.pendingContext), (r.pendingContext = null)),
        (e === null || e.child === null) &&
          (Io(t)
            ? (t.flags |= 4)
            : e === null ||
              (e.memoizedState.isDehydrated && !(t.flags & 256)) ||
              ((t.flags |= 1024), it !== null && (Fl(it), (it = null)))),
        Ml(e, t),
        xe(t),
        null
      );
    case 5:
      Ea(t);
      var o = vn(so.current);
      if (((n = t.type), e !== null && t.stateNode != null))
        (Zf(e, t, n, r, o),
          e.ref !== t.ref && ((t.flags |= 512), (t.flags |= 2097152)));
      else {
        if (!r) {
          if (t.stateNode === null) throw Error(R(166));
          return (xe(t), null);
        }
        if (((e = vn(xt.current)), Io(t))) {
          ((r = t.stateNode), (n = t.type));
          var i = t.memoizedProps;
          switch (((r[gt] = t), (r[oo] = i), (e = (t.mode & 1) !== 0), n)) {
            case "dialog":
              (G("cancel", r), G("close", r));
              break;
            case "iframe":
            case "object":
            case "embed":
              G("load", r);
              break;
            case "video":
            case "audio":
              for (o = 0; o < Ar.length; o++) G(Ar[o], r);
              break;
            case "source":
              G("error", r);
              break;
            case "img":
            case "image":
            case "link":
              (G("error", r), G("load", r));
              break;
            case "details":
              G("toggle", r);
              break;
            case "input":
              (mu(r, i), G("invalid", r));
              break;
            case "select":
              ((r._wrapperState = { wasMultiple: !!i.multiple }),
                G("invalid", r));
              break;
            case "textarea":
              (gu(r, i), G("invalid", r));
          }
          (rl(n, i), (o = null));
          for (var s in i)
            if (i.hasOwnProperty(s)) {
              var l = i[s];
              s === "children"
                ? typeof l == "string"
                  ? r.textContent !== l &&
                    (i.suppressHydrationWarning !== !0 &&
                      bo(r.textContent, l, e),
                    (o = ["children", l]))
                  : typeof l == "number" &&
                    r.textContent !== "" + l &&
                    (i.suppressHydrationWarning !== !0 &&
                      bo(r.textContent, l, e),
                    (o = ["children", "" + l]))
                : Gr.hasOwnProperty(s) &&
                  l != null &&
                  s === "onScroll" &&
                  G("scroll", r);
            }
          switch (n) {
            case "input":
              (No(r), vu(r, i, !0));
              break;
            case "textarea":
              (No(r), yu(r));
              break;
            case "select":
            case "option":
              break;
            default:
              typeof i.onClick == "function" && (r.onclick = yi);
          }
          ((r = o), (t.updateQueue = r), r !== null && (t.flags |= 4));
        } else {
          ((s = o.nodeType === 9 ? o : o.ownerDocument),
            e === "http://www.w3.org/1999/xhtml" && (e = Nd(n)),
            e === "http://www.w3.org/1999/xhtml"
              ? n === "script"
                ? ((e = s.createElement("div")),
                  (e.innerHTML = "<script><\/script>"),
                  (e = e.removeChild(e.firstChild)))
                : typeof r.is == "string"
                  ? (e = s.createElement(n, { is: r.is }))
                  : ((e = s.createElement(n)),
                    n === "select" &&
                      ((s = e),
                      r.multiple
                        ? (s.multiple = !0)
                        : r.size && (s.size = r.size)))
              : (e = s.createElementNS(e, n)),
            (e[gt] = t),
            (e[oo] = r),
            qf(e, t, !1, !1),
            (t.stateNode = e));
          e: {
            switch (((s = ol(n, r)), n)) {
              case "dialog":
                (G("cancel", e), G("close", e), (o = r));
                break;
              case "iframe":
              case "object":
              case "embed":
                (G("load", e), (o = r));
                break;
              case "video":
              case "audio":
                for (o = 0; o < Ar.length; o++) G(Ar[o], e);
                o = r;
                break;
              case "source":
                (G("error", e), (o = r));
                break;
              case "img":
              case "image":
              case "link":
                (G("error", e), G("load", e), (o = r));
                break;
              case "details":
                (G("toggle", e), (o = r));
                break;
              case "input":
                (mu(e, r), (o = Zs(e, r)), G("invalid", e));
                break;
              case "option":
                o = r;
                break;
              case "select":
                ((e._wrapperState = { wasMultiple: !!r.multiple }),
                  (o = J({}, r, { value: void 0 })),
                  G("invalid", e));
                break;
              case "textarea":
                (gu(e, r), (o = tl(e, r)), G("invalid", e));
                break;
              default:
                o = r;
            }
            (rl(n, o), (l = o));
            for (i in l)
              if (l.hasOwnProperty(i)) {
                var a = l[i];
                i === "style"
                  ? _d(e, a)
                  : i === "dangerouslySetInnerHTML"
                    ? ((a = a ? a.__html : void 0), a != null && Td(e, a))
                    : i === "children"
                      ? typeof a == "string"
                        ? (n !== "textarea" || a !== "") && Xr(e, a)
                        : typeof a == "number" && Xr(e, "" + a)
                      : i !== "suppressContentEditableWarning" &&
                        i !== "suppressHydrationWarning" &&
                        i !== "autoFocus" &&
                        (Gr.hasOwnProperty(i)
                          ? a != null && i === "onScroll" && G("scroll", e)
                          : a != null && Jl(e, i, a, s));
              }
            switch (n) {
              case "input":
                (No(e), vu(e, r, !1));
                break;
              case "textarea":
                (No(e), yu(e));
                break;
              case "option":
                r.value != null && e.setAttribute("value", "" + sn(r.value));
                break;
              case "select":
                ((e.multiple = !!r.multiple),
                  (i = r.value),
                  i != null
                    ? Gn(e, !!r.multiple, i, !1)
                    : r.defaultValue != null &&
                      Gn(e, !!r.multiple, r.defaultValue, !0));
                break;
              default:
                typeof o.onClick == "function" && (e.onclick = yi);
            }
            switch (n) {
              case "button":
              case "input":
              case "select":
              case "textarea":
                r = !!r.autoFocus;
                break e;
              case "img":
                r = !0;
                break e;
              default:
                r = !1;
            }
          }
          r && (t.flags |= 4);
        }
        t.ref !== null && ((t.flags |= 512), (t.flags |= 2097152));
      }
      return (xe(t), null);
    case 6:
      if (e && t.stateNode != null) Jf(e, t, e.memoizedProps, r);
      else {
        if (typeof r != "string" && t.stateNode === null) throw Error(R(166));
        if (((n = vn(so.current)), vn(xt.current), Io(t))) {
          if (
            ((r = t.stateNode),
            (n = t.memoizedProps),
            (r[gt] = t),
            (i = r.nodeValue !== n) && ((e = Ue), e !== null))
          )
            switch (e.tag) {
              case 3:
                bo(r.nodeValue, n, (e.mode & 1) !== 0);
                break;
              case 5:
                e.memoizedProps.suppressHydrationWarning !== !0 &&
                  bo(r.nodeValue, n, (e.mode & 1) !== 0);
            }
          i && (t.flags |= 4);
        } else
          ((r = (n.nodeType === 9 ? n : n.ownerDocument).createTextNode(r)),
            (r[gt] = t),
            (t.stateNode = r));
      }
      return (xe(t), null);
    case 13:
      if (
        (X(q),
        (r = t.memoizedState),
        e === null ||
          (e.memoizedState !== null && e.memoizedState.dehydrated !== null))
      ) {
        if (Y && $e !== null && t.mode & 1 && !(t.flags & 128))
          (gf(), pr(), (t.flags |= 98560), (i = !1));
        else if (((i = Io(t)), r !== null && r.dehydrated !== null)) {
          if (e === null) {
            if (!i) throw Error(R(318));
            if (
              ((i = t.memoizedState),
              (i = i !== null ? i.dehydrated : null),
              !i)
            )
              throw Error(R(317));
            i[gt] = t;
          } else
            (pr(),
              !(t.flags & 128) && (t.memoizedState = null),
              (t.flags |= 4));
          (xe(t), (i = !1));
        } else (it !== null && (Fl(it), (it = null)), (i = !0));
        if (!i) return t.flags & 65536 ? t : null;
      }
      return t.flags & 128
        ? ((t.lanes = n), t)
        : ((r = r !== null),
          r !== (e !== null && e.memoizedState !== null) &&
            r &&
            ((t.child.flags |= 8192),
            t.mode & 1 &&
              (e === null || q.current & 1 ? ue === 0 && (ue = 3) : Ia())),
          t.updateQueue !== null && (t.flags |= 4),
          xe(t),
          null);
    case 4:
      return (
        mr(),
        Ml(e, t),
        e === null && no(t.stateNode.containerInfo),
        xe(t),
        null
      );
    case 10:
      return (ya(t.type._context), xe(t), null);
    case 17:
      return (Le(t.type) && wi(), xe(t), null);
    case 19:
      if ((X(q), (i = t.memoizedState), i === null)) return (xe(t), null);
      if (((r = (t.flags & 128) !== 0), (s = i.rendering), s === null))
        if (r) Mr(i, !1);
        else {
          if (ue !== 0 || (e !== null && e.flags & 128))
            for (e = t.child; e !== null; ) {
              if (((s = Ni(e)), s !== null)) {
                for (
                  t.flags |= 128,
                    Mr(i, !1),
                    r = s.updateQueue,
                    r !== null && ((t.updateQueue = r), (t.flags |= 4)),
                    t.subtreeFlags = 0,
                    r = n,
                    n = t.child;
                  n !== null;

                )
                  ((i = n),
                    (e = r),
                    (i.flags &= 14680066),
                    (s = i.alternate),
                    s === null
                      ? ((i.childLanes = 0),
                        (i.lanes = e),
                        (i.child = null),
                        (i.subtreeFlags = 0),
                        (i.memoizedProps = null),
                        (i.memoizedState = null),
                        (i.updateQueue = null),
                        (i.dependencies = null),
                        (i.stateNode = null))
                      : ((i.childLanes = s.childLanes),
                        (i.lanes = s.lanes),
                        (i.child = s.child),
                        (i.subtreeFlags = 0),
                        (i.deletions = null),
                        (i.memoizedProps = s.memoizedProps),
                        (i.memoizedState = s.memoizedState),
                        (i.updateQueue = s.updateQueue),
                        (i.type = s.type),
                        (e = s.dependencies),
                        (i.dependencies =
                          e === null
                            ? null
                            : {
                                lanes: e.lanes,
                                firstContext: e.firstContext,
                              })),
                    (n = n.sibling));
                return (Q(q, (q.current & 1) | 2), t.child);
              }
              e = e.sibling;
            }
          i.tail !== null &&
            oe() > gr &&
            ((t.flags |= 128), (r = !0), Mr(i, !1), (t.lanes = 4194304));
        }
      else {
        if (!r)
          if (((e = Ni(s)), e !== null)) {
            if (
              ((t.flags |= 128),
              (r = !0),
              (n = e.updateQueue),
              n !== null && ((t.updateQueue = n), (t.flags |= 4)),
              Mr(i, !0),
              i.tail === null && i.tailMode === "hidden" && !s.alternate && !Y)
            )
              return (xe(t), null);
          } else
            2 * oe() - i.renderingStartTime > gr &&
              n !== 1073741824 &&
              ((t.flags |= 128), (r = !0), Mr(i, !1), (t.lanes = 4194304));
        i.isBackwards
          ? ((s.sibling = t.child), (t.child = s))
          : ((n = i.last),
            n !== null ? (n.sibling = s) : (t.child = s),
            (i.last = s));
      }
      return i.tail !== null
        ? ((t = i.tail),
          (i.rendering = t),
          (i.tail = t.sibling),
          (i.renderingStartTime = oe()),
          (t.sibling = null),
          (n = q.current),
          Q(q, r ? (n & 1) | 2 : n & 1),
          t)
        : (xe(t), null);
    case 22:
    case 23:
      return (
        ba(),
        (r = t.memoizedState !== null),
        e !== null && (e.memoizedState !== null) !== r && (t.flags |= 8192),
        r && t.mode & 1
          ? ze & 1073741824 && (xe(t), t.subtreeFlags & 6 && (t.flags |= 8192))
          : xe(t),
        null
      );
    case 24:
      return null;
    case 25:
      return null;
  }
  throw Error(R(156, t.tag));
}
function dg(e, t) {
  switch ((ha(t), t.tag)) {
    case 1:
      return (
        Le(t.type) && wi(),
        (e = t.flags),
        e & 65536 ? ((t.flags = (e & -65537) | 128), t) : null
      );
    case 3:
      return (
        mr(),
        X(Ie),
        X(Ce),
        Ca(),
        (e = t.flags),
        e & 65536 && !(e & 128) ? ((t.flags = (e & -65537) | 128), t) : null
      );
    case 5:
      return (Ea(t), null);
    case 13:
      if ((X(q), (e = t.memoizedState), e !== null && e.dehydrated !== null)) {
        if (t.alternate === null) throw Error(R(340));
        pr();
      }
      return (
        (e = t.flags),
        e & 65536 ? ((t.flags = (e & -65537) | 128), t) : null
      );
    case 19:
      return (X(q), null);
    case 4:
      return (mr(), null);
    case 10:
      return (ya(t.type._context), null);
    case 22:
    case 23:
      return (ba(), null);
    case 24:
      return null;
    default:
      return null;
  }
}
var Fo = !1,
  Ee = !1,
  fg = typeof WeakSet == "function" ? WeakSet : Set,
  O = null;
function Qn(e, t) {
  var n = e.ref;
  if (n !== null)
    if (typeof n == "function")
      try {
        n(null);
      } catch (r) {
        ne(e, t, r);
      }
    else n.current = null;
}
function Ol(e, t, n) {
  try {
    n();
  } catch (r) {
    ne(e, t, r);
  }
}
var lc = !1;
function pg(e, t) {
  if (((hl = mi), (e = rf()), fa(e))) {
    if ("selectionStart" in e)
      var n = { start: e.selectionStart, end: e.selectionEnd };
    else
      e: {
        n = ((n = e.ownerDocument) && n.defaultView) || window;
        var r = n.getSelection && n.getSelection();
        if (r && r.rangeCount !== 0) {
          n = r.anchorNode;
          var o = r.anchorOffset,
            i = r.focusNode;
          r = r.focusOffset;
          try {
            (n.nodeType, i.nodeType);
          } catch {
            n = null;
            break e;
          }
          var s = 0,
            l = -1,
            a = -1,
            u = 0,
            v = 0,
            f = e,
            m = null;
          t: for (;;) {
            for (
              var x;
              f !== n || (o !== 0 && f.nodeType !== 3) || (l = s + o),
                f !== i || (r !== 0 && f.nodeType !== 3) || (a = s + r),
                f.nodeType === 3 && (s += f.nodeValue.length),
                (x = f.firstChild) !== null;

            )
              ((m = f), (f = x));
            for (;;) {
              if (f === e) break t;
              if (
                (m === n && ++u === o && (l = s),
                m === i && ++v === r && (a = s),
                (x = f.nextSibling) !== null)
              )
                break;
              ((f = m), (m = f.parentNode));
            }
            f = x;
          }
          n = l === -1 || a === -1 ? null : { start: l, end: a };
        } else n = null;
      }
    n = n || { start: 0, end: 0 };
  } else n = null;
  for (ml = { focusedElem: e, selectionRange: n }, mi = !1, O = t; O !== null; )
    if (((t = O), (e = t.child), (t.subtreeFlags & 1028) !== 0 && e !== null))
      ((e.return = t), (O = e));
    else
      for (; O !== null; ) {
        t = O;
        try {
          var E = t.alternate;
          if (t.flags & 1024)
            switch (t.tag) {
              case 0:
              case 11:
              case 15:
                break;
              case 1:
                if (E !== null) {
                  var g = E.memoizedProps,
                    w = E.memoizedState,
                    d = t.stateNode,
                    c = d.getSnapshotBeforeUpdate(
                      t.elementType === t.type ? g : tt(t.type, g),
                      w,
                    );
                  d.__reactInternalSnapshotBeforeUpdate = c;
                }
                break;
              case 3:
                var p = t.stateNode.containerInfo;
                p.nodeType === 1
                  ? (p.textContent = "")
                  : p.nodeType === 9 &&
                    p.documentElement &&
                    p.removeChild(p.documentElement);
                break;
              case 5:
              case 6:
              case 4:
              case 17:
                break;
              default:
                throw Error(R(163));
            }
        } catch (S) {
          ne(t, t.return, S);
        }
        if (((e = t.sibling), e !== null)) {
          ((e.return = t.return), (O = e));
          break;
        }
        O = t.return;
      }
  return ((E = lc), (lc = !1), E);
}
function Br(e, t, n) {
  var r = t.updateQueue;
  if (((r = r !== null ? r.lastEffect : null), r !== null)) {
    var o = (r = r.next);
    do {
      if ((o.tag & e) === e) {
        var i = o.destroy;
        ((o.destroy = void 0), i !== void 0 && Ol(t, n, i));
      }
      o = o.next;
    } while (o !== r);
  }
}
function Qi(e, t) {
  if (
    ((t = t.updateQueue), (t = t !== null ? t.lastEffect : null), t !== null)
  ) {
    var n = (t = t.next);
    do {
      if ((n.tag & e) === e) {
        var r = n.create;
        n.destroy = r();
      }
      n = n.next;
    } while (n !== t);
  }
}
function jl(e) {
  var t = e.ref;
  if (t !== null) {
    var n = e.stateNode;
    switch (e.tag) {
      case 5:
        e = n;
        break;
      default:
        e = n;
    }
    typeof t == "function" ? t(e) : (t.current = e);
  }
}
function ep(e) {
  var t = e.alternate;
  (t !== null && ((e.alternate = null), ep(t)),
    (e.child = null),
    (e.deletions = null),
    (e.sibling = null),
    e.tag === 5 &&
      ((t = e.stateNode),
      t !== null &&
        (delete t[gt], delete t[oo], delete t[yl], delete t[Xv], delete t[Yv])),
    (e.stateNode = null),
    (e.return = null),
    (e.dependencies = null),
    (e.memoizedProps = null),
    (e.memoizedState = null),
    (e.pendingProps = null),
    (e.stateNode = null),
    (e.updateQueue = null));
}
function tp(e) {
  return e.tag === 5 || e.tag === 3 || e.tag === 4;
}
function ac(e) {
  e: for (;;) {
    for (; e.sibling === null; ) {
      if (e.return === null || tp(e.return)) return null;
      e = e.return;
    }
    for (
      e.sibling.return = e.return, e = e.sibling;
      e.tag !== 5 && e.tag !== 6 && e.tag !== 18;

    ) {
      if (e.flags & 2 || e.child === null || e.tag === 4) continue e;
      ((e.child.return = e), (e = e.child));
    }
    if (!(e.flags & 2)) return e.stateNode;
  }
}
function Dl(e, t, n) {
  var r = e.tag;
  if (r === 5 || r === 6)
    ((e = e.stateNode),
      t
        ? n.nodeType === 8
          ? n.parentNode.insertBefore(e, t)
          : n.insertBefore(e, t)
        : (n.nodeType === 8
            ? ((t = n.parentNode), t.insertBefore(e, n))
            : ((t = n), t.appendChild(e)),
          (n = n._reactRootContainer),
          n != null || t.onclick !== null || (t.onclick = yi)));
  else if (r !== 4 && ((e = e.child), e !== null))
    for (Dl(e, t, n), e = e.sibling; e !== null; )
      (Dl(e, t, n), (e = e.sibling));
}
function bl(e, t, n) {
  var r = e.tag;
  if (r === 5 || r === 6)
    ((e = e.stateNode), t ? n.insertBefore(e, t) : n.appendChild(e));
  else if (r !== 4 && ((e = e.child), e !== null))
    for (bl(e, t, n), e = e.sibling; e !== null; )
      (bl(e, t, n), (e = e.sibling));
}
var me = null,
  ot = !1;
function bt(e, t, n) {
  for (n = n.child; n !== null; ) (np(e, t, n), (n = n.sibling));
}
function np(e, t, n) {
  if (wt && typeof wt.onCommitFiberUnmount == "function")
    try {
      wt.onCommitFiberUnmount(Fi, n);
    } catch {}
  switch (n.tag) {
    case 5:
      Ee || Qn(n, t);
    case 6:
      var r = me,
        o = ot;
      ((me = null),
        bt(e, t, n),
        (me = r),
        (ot = o),
        me !== null &&
          (ot
            ? ((e = me),
              (n = n.stateNode),
              e.nodeType === 8 ? e.parentNode.removeChild(n) : e.removeChild(n))
            : me.removeChild(n.stateNode)));
      break;
    case 18:
      me !== null &&
        (ot
          ? ((e = me),
            (n = n.stateNode),
            e.nodeType === 8
              ? Cs(e.parentNode, n)
              : e.nodeType === 1 && Cs(e, n),
            Jr(e))
          : Cs(me, n.stateNode));
      break;
    case 4:
      ((r = me),
        (o = ot),
        (me = n.stateNode.containerInfo),
        (ot = !0),
        bt(e, t, n),
        (me = r),
        (ot = o));
      break;
    case 0:
    case 11:
    case 14:
    case 15:
      if (
        !Ee &&
        ((r = n.updateQueue), r !== null && ((r = r.lastEffect), r !== null))
      ) {
        o = r = r.next;
        do {
          var i = o,
            s = i.destroy;
          ((i = i.tag),
            s !== void 0 && (i & 2 || i & 4) && Ol(n, t, s),
            (o = o.next));
        } while (o !== r);
      }
      bt(e, t, n);
      break;
    case 1:
      if (
        !Ee &&
        (Qn(n, t),
        (r = n.stateNode),
        typeof r.componentWillUnmount == "function")
      )
        try {
          ((r.props = n.memoizedProps),
            (r.state = n.memoizedState),
            r.componentWillUnmount());
        } catch (l) {
          ne(n, t, l);
        }
      bt(e, t, n);
      break;
    case 21:
      bt(e, t, n);
      break;
    case 22:
      n.mode & 1
        ? ((Ee = (r = Ee) || n.memoizedState !== null), bt(e, t, n), (Ee = r))
        : bt(e, t, n);
      break;
    default:
      bt(e, t, n);
  }
}
function uc(e) {
  var t = e.updateQueue;
  if (t !== null) {
    e.updateQueue = null;
    var n = e.stateNode;
    (n === null && (n = e.stateNode = new fg()),
      t.forEach(function (r) {
        var o = Eg.bind(null, e, r);
        n.has(r) || (n.add(r), r.then(o, o));
      }));
  }
}
function et(e, t) {
  var n = t.deletions;
  if (n !== null)
    for (var r = 0; r < n.length; r++) {
      var o = n[r];
      try {
        var i = e,
          s = t,
          l = s;
        e: for (; l !== null; ) {
          switch (l.tag) {
            case 5:
              ((me = l.stateNode), (ot = !1));
              break e;
            case 3:
              ((me = l.stateNode.containerInfo), (ot = !0));
              break e;
            case 4:
              ((me = l.stateNode.containerInfo), (ot = !0));
              break e;
          }
          l = l.return;
        }
        if (me === null) throw Error(R(160));
        (np(i, s, o), (me = null), (ot = !1));
        var a = o.alternate;
        (a !== null && (a.return = null), (o.return = null));
      } catch (u) {
        ne(o, t, u);
      }
    }
  if (t.subtreeFlags & 12854)
    for (t = t.child; t !== null; ) (rp(t, e), (t = t.sibling));
}
function rp(e, t) {
  var n = e.alternate,
    r = e.flags;
  switch (e.tag) {
    case 0:
    case 11:
    case 14:
    case 15:
      if ((et(t, e), ft(e), r & 4)) {
        try {
          (Br(3, e, e.return), Qi(3, e));
        } catch (g) {
          ne(e, e.return, g);
        }
        try {
          Br(5, e, e.return);
        } catch (g) {
          ne(e, e.return, g);
        }
      }
      break;
    case 1:
      (et(t, e), ft(e), r & 512 && n !== null && Qn(n, n.return));
      break;
    case 5:
      if (
        (et(t, e),
        ft(e),
        r & 512 && n !== null && Qn(n, n.return),
        e.flags & 32)
      ) {
        var o = e.stateNode;
        try {
          Xr(o, "");
        } catch (g) {
          ne(e, e.return, g);
        }
      }
      if (r & 4 && ((o = e.stateNode), o != null)) {
        var i = e.memoizedProps,
          s = n !== null ? n.memoizedProps : i,
          l = e.type,
          a = e.updateQueue;
        if (((e.updateQueue = null), a !== null))
          try {
            (l === "input" && i.type === "radio" && i.name != null && kd(o, i),
              ol(l, s));
            var u = ol(l, i);
            for (s = 0; s < a.length; s += 2) {
              var v = a[s],
                f = a[s + 1];
              v === "style"
                ? _d(o, f)
                : v === "dangerouslySetInnerHTML"
                  ? Td(o, f)
                  : v === "children"
                    ? Xr(o, f)
                    : Jl(o, v, f, u);
            }
            switch (l) {
              case "input":
                Js(o, i);
                break;
              case "textarea":
                Pd(o, i);
                break;
              case "select":
                var m = o._wrapperState.wasMultiple;
                o._wrapperState.wasMultiple = !!i.multiple;
                var x = i.value;
                x != null
                  ? Gn(o, !!i.multiple, x, !1)
                  : m !== !!i.multiple &&
                    (i.defaultValue != null
                      ? Gn(o, !!i.multiple, i.defaultValue, !0)
                      : Gn(o, !!i.multiple, i.multiple ? [] : "", !1));
            }
            o[oo] = i;
          } catch (g) {
            ne(e, e.return, g);
          }
      }
      break;
    case 6:
      if ((et(t, e), ft(e), r & 4)) {
        if (e.stateNode === null) throw Error(R(162));
        ((o = e.stateNode), (i = e.memoizedProps));
        try {
          o.nodeValue = i;
        } catch (g) {
          ne(e, e.return, g);
        }
      }
      break;
    case 3:
      if (
        (et(t, e), ft(e), r & 4 && n !== null && n.memoizedState.isDehydrated)
      )
        try {
          Jr(t.containerInfo);
        } catch (g) {
          ne(e, e.return, g);
        }
      break;
    case 4:
      (et(t, e), ft(e));
      break;
    case 13:
      (et(t, e),
        ft(e),
        (o = e.child),
        o.flags & 8192 &&
          ((i = o.memoizedState !== null),
          (o.stateNode.isHidden = i),
          !i ||
            (o.alternate !== null && o.alternate.memoizedState !== null) ||
            (ja = oe())),
        r & 4 && uc(e));
      break;
    case 22:
      if (
        ((v = n !== null && n.memoizedState !== null),
        e.mode & 1 ? ((Ee = (u = Ee) || v), et(t, e), (Ee = u)) : et(t, e),
        ft(e),
        r & 8192)
      ) {
        if (
          ((u = e.memoizedState !== null),
          (e.stateNode.isHidden = u) && !v && e.mode & 1)
        )
          for (O = e, v = e.child; v !== null; ) {
            for (f = O = v; O !== null; ) {
              switch (((m = O), (x = m.child), m.tag)) {
                case 0:
                case 11:
                case 14:
                case 15:
                  Br(4, m, m.return);
                  break;
                case 1:
                  Qn(m, m.return);
                  var E = m.stateNode;
                  if (typeof E.componentWillUnmount == "function") {
                    ((r = m), (n = m.return));
                    try {
                      ((t = r),
                        (E.props = t.memoizedProps),
                        (E.state = t.memoizedState),
                        E.componentWillUnmount());
                    } catch (g) {
                      ne(r, n, g);
                    }
                  }
                  break;
                case 5:
                  Qn(m, m.return);
                  break;
                case 22:
                  if (m.memoizedState !== null) {
                    dc(f);
                    continue;
                  }
              }
              x !== null ? ((x.return = m), (O = x)) : dc(f);
            }
            v = v.sibling;
          }
        e: for (v = null, f = e; ; ) {
          if (f.tag === 5) {
            if (v === null) {
              v = f;
              try {
                ((o = f.stateNode),
                  u
                    ? ((i = o.style),
                      typeof i.setProperty == "function"
                        ? i.setProperty("display", "none", "important")
                        : (i.display = "none"))
                    : ((l = f.stateNode),
                      (a = f.memoizedProps.style),
                      (s =
                        a != null && a.hasOwnProperty("display")
                          ? a.display
                          : null),
                      (l.style.display = Rd("display", s))));
              } catch (g) {
                ne(e, e.return, g);
              }
            }
          } else if (f.tag === 6) {
            if (v === null)
              try {
                f.stateNode.nodeValue = u ? "" : f.memoizedProps;
              } catch (g) {
                ne(e, e.return, g);
              }
          } else if (
            ((f.tag !== 22 && f.tag !== 23) ||
              f.memoizedState === null ||
              f === e) &&
            f.child !== null
          ) {
            ((f.child.return = f), (f = f.child));
            continue;
          }
          if (f === e) break e;
          for (; f.sibling === null; ) {
            if (f.return === null || f.return === e) break e;
            (v === f && (v = null), (f = f.return));
          }
          (v === f && (v = null),
            (f.sibling.return = f.return),
            (f = f.sibling));
        }
      }
      break;
    case 19:
      (et(t, e), ft(e), r & 4 && uc(e));
      break;
    case 21:
      break;
    default:
      (et(t, e), ft(e));
  }
}
function ft(e) {
  var t = e.flags;
  if (t & 2) {
    try {
      e: {
        for (var n = e.return; n !== null; ) {
          if (tp(n)) {
            var r = n;
            break e;
          }
          n = n.return;
        }
        throw Error(R(160));
      }
      switch (r.tag) {
        case 5:
          var o = r.stateNode;
          r.flags & 32 && (Xr(o, ""), (r.flags &= -33));
          var i = ac(e);
          bl(e, i, o);
          break;
        case 3:
        case 4:
          var s = r.stateNode.containerInfo,
            l = ac(e);
          Dl(e, l, s);
          break;
        default:
          throw Error(R(161));
      }
    } catch (a) {
      ne(e, e.return, a);
    }
    e.flags &= -3;
  }
  t & 4096 && (e.flags &= -4097);
}
function hg(e, t, n) {
  ((O = e), op(e));
}
function op(e, t, n) {
  for (var r = (e.mode & 1) !== 0; O !== null; ) {
    var o = O,
      i = o.child;
    if (o.tag === 22 && r) {
      var s = o.memoizedState !== null || Fo;
      if (!s) {
        var l = o.alternate,
          a = (l !== null && l.memoizedState !== null) || Ee;
        l = Fo;
        var u = Ee;
        if (((Fo = s), (Ee = a) && !u))
          for (O = o; O !== null; )
            ((s = O),
              (a = s.child),
              s.tag === 22 && s.memoizedState !== null
                ? fc(o)
                : a !== null
                  ? ((a.return = s), (O = a))
                  : fc(o));
        for (; i !== null; ) ((O = i), op(i), (i = i.sibling));
        ((O = o), (Fo = l), (Ee = u));
      }
      cc(e);
    } else
      o.subtreeFlags & 8772 && i !== null ? ((i.return = o), (O = i)) : cc(e);
  }
}
function cc(e) {
  for (; O !== null; ) {
    var t = O;
    if (t.flags & 8772) {
      var n = t.alternate;
      try {
        if (t.flags & 8772)
          switch (t.tag) {
            case 0:
            case 11:
            case 15:
              Ee || Qi(5, t);
              break;
            case 1:
              var r = t.stateNode;
              if (t.flags & 4 && !Ee)
                if (n === null) r.componentDidMount();
                else {
                  var o =
                    t.elementType === t.type
                      ? n.memoizedProps
                      : tt(t.type, n.memoizedProps);
                  r.componentDidUpdate(
                    o,
                    n.memoizedState,
                    r.__reactInternalSnapshotBeforeUpdate,
                  );
                }
              var i = t.updateQueue;
              i !== null && Gu(t, i, r);
              break;
            case 3:
              var s = t.updateQueue;
              if (s !== null) {
                if (((n = null), t.child !== null))
                  switch (t.child.tag) {
                    case 5:
                      n = t.child.stateNode;
                      break;
                    case 1:
                      n = t.child.stateNode;
                  }
                Gu(t, s, n);
              }
              break;
            case 5:
              var l = t.stateNode;
              if (n === null && t.flags & 4) {
                n = l;
                var a = t.memoizedProps;
                switch (t.type) {
                  case "button":
                  case "input":
                  case "select":
                  case "textarea":
                    a.autoFocus && n.focus();
                    break;
                  case "img":
                    a.src && (n.src = a.src);
                }
              }
              break;
            case 6:
              break;
            case 4:
              break;
            case 12:
              break;
            case 13:
              if (t.memoizedState === null) {
                var u = t.alternate;
                if (u !== null) {
                  var v = u.memoizedState;
                  if (v !== null) {
                    var f = v.dehydrated;
                    f !== null && Jr(f);
                  }
                }
              }
              break;
            case 19:
            case 17:
            case 21:
            case 22:
            case 23:
            case 25:
              break;
            default:
              throw Error(R(163));
          }
        Ee || (t.flags & 512 && jl(t));
      } catch (m) {
        ne(t, t.return, m);
      }
    }
    if (t === e) {
      O = null;
      break;
    }
    if (((n = t.sibling), n !== null)) {
      ((n.return = t.return), (O = n));
      break;
    }
    O = t.return;
  }
}
function dc(e) {
  for (; O !== null; ) {
    var t = O;
    if (t === e) {
      O = null;
      break;
    }
    var n = t.sibling;
    if (n !== null) {
      ((n.return = t.return), (O = n));
      break;
    }
    O = t.return;
  }
}
function fc(e) {
  for (; O !== null; ) {
    var t = O;
    try {
      switch (t.tag) {
        case 0:
        case 11:
        case 15:
          var n = t.return;
          try {
            Qi(4, t);
          } catch (a) {
            ne(t, n, a);
          }
          break;
        case 1:
          var r = t.stateNode;
          if (typeof r.componentDidMount == "function") {
            var o = t.return;
            try {
              r.componentDidMount();
            } catch (a) {
              ne(t, o, a);
            }
          }
          var i = t.return;
          try {
            jl(t);
          } catch (a) {
            ne(t, i, a);
          }
          break;
        case 5:
          var s = t.return;
          try {
            jl(t);
          } catch (a) {
            ne(t, s, a);
          }
      }
    } catch (a) {
      ne(t, t.return, a);
    }
    if (t === e) {
      O = null;
      break;
    }
    var l = t.sibling;
    if (l !== null) {
      ((l.return = t.return), (O = l));
      break;
    }
    O = t.return;
  }
}
var mg = Math.ceil,
  _i = Dt.ReactCurrentDispatcher,
  Ma = Dt.ReactCurrentOwner,
  qe = Dt.ReactCurrentBatchConfig,
  W = 0,
  he = null,
  se = null,
  ve = 0,
  ze = 0,
  Kn = un(0),
  ue = 0,
  co = null,
  Nn = 0,
  Ki = 0,
  Oa = 0,
  Hr = null,
  De = null,
  ja = 0,
  gr = 1 / 0,
  kt = null,
  Mi = !1,
  Il = null,
  nn = null,
  zo = !1,
  Yt = null,
  Oi = 0,
  Qr = 0,
  Ll = null,
  ri = -1,
  oi = 0;
function _e() {
  return W & 6 ? oe() : ri !== -1 ? ri : (ri = oe());
}
function rn(e) {
  return e.mode & 1
    ? W & 2 && ve !== 0
      ? ve & -ve
      : Zv.transition !== null
        ? (oi === 0 && (oi = Ud()), oi)
        : ((e = B),
          e !== 0 || ((e = window.event), (e = e === void 0 ? 16 : Gd(e.type))),
          e)
    : 1;
}
function lt(e, t, n, r) {
  if (50 < Qr) throw ((Qr = 0), (Ll = null), Error(R(185)));
  (yo(e, n, r),
    (!(W & 2) || e !== he) &&
      (e === he && (!(W & 2) && (Ki |= n), ue === 4 && Vt(e, ve)),
      Ae(e, r),
      n === 1 && W === 0 && !(t.mode & 1) && ((gr = oe() + 500), Vi && cn())));
}
function Ae(e, t) {
  var n = e.callbackNode;
  Zm(e, t);
  var r = hi(e, e === he ? ve : 0);
  if (r === 0)
    (n !== null && Su(n), (e.callbackNode = null), (e.callbackPriority = 0));
  else if (((t = r & -r), e.callbackPriority !== t)) {
    if ((n != null && Su(n), t === 1))
      (e.tag === 0 ? qv(pc.bind(null, e)) : hf(pc.bind(null, e)),
        Kv(function () {
          !(W & 6) && cn();
        }),
        (n = null));
    else {
      switch (Wd(r)) {
        case 1:
          n = oa;
          break;
        case 4:
          n = zd;
          break;
        case 16:
          n = pi;
          break;
        case 536870912:
          n = $d;
          break;
        default:
          n = pi;
      }
      n = fp(n, ip.bind(null, e));
    }
    ((e.callbackPriority = t), (e.callbackNode = n));
  }
}
function ip(e, t) {
  if (((ri = -1), (oi = 0), W & 6)) throw Error(R(327));
  var n = e.callbackNode;
  if (Jn() && e.callbackNode !== n) return null;
  var r = hi(e, e === he ? ve : 0);
  if (r === 0) return null;
  if (r & 30 || r & e.expiredLanes || t) t = ji(e, r);
  else {
    t = r;
    var o = W;
    W |= 2;
    var i = lp();
    (he !== e || ve !== t) && ((kt = null), (gr = oe() + 500), Sn(e, t));
    do
      try {
        yg();
        break;
      } catch (l) {
        sp(e, l);
      }
    while (!0);
    (ga(),
      (_i.current = i),
      (W = o),
      se !== null ? (t = 0) : ((he = null), (ve = 0), (t = ue)));
  }
  if (t !== 0) {
    if (
      (t === 2 && ((o = ul(e)), o !== 0 && ((r = o), (t = Al(e, o)))), t === 1)
    )
      throw ((n = co), Sn(e, 0), Vt(e, r), Ae(e, oe()), n);
    if (t === 6) Vt(e, r);
    else {
      if (
        ((o = e.current.alternate),
        !(r & 30) &&
          !vg(o) &&
          ((t = ji(e, r)),
          t === 2 && ((i = ul(e)), i !== 0 && ((r = i), (t = Al(e, i)))),
          t === 1))
      )
        throw ((n = co), Sn(e, 0), Vt(e, r), Ae(e, oe()), n);
      switch (((e.finishedWork = o), (e.finishedLanes = r), t)) {
        case 0:
        case 1:
          throw Error(R(345));
        case 2:
          pn(e, De, kt);
          break;
        case 3:
          if (
            (Vt(e, r), (r & 130023424) === r && ((t = ja + 500 - oe()), 10 < t))
          ) {
            if (hi(e, 0) !== 0) break;
            if (((o = e.suspendedLanes), (o & r) !== r)) {
              (_e(), (e.pingedLanes |= e.suspendedLanes & o));
              break;
            }
            e.timeoutHandle = gl(pn.bind(null, e, De, kt), t);
            break;
          }
          pn(e, De, kt);
          break;
        case 4:
          if ((Vt(e, r), (r & 4194240) === r)) break;
          for (t = e.eventTimes, o = -1; 0 < r; ) {
            var s = 31 - st(r);
            ((i = 1 << s), (s = t[s]), s > o && (o = s), (r &= ~i));
          }
          if (
            ((r = o),
            (r = oe() - r),
            (r =
              (120 > r
                ? 120
                : 480 > r
                  ? 480
                  : 1080 > r
                    ? 1080
                    : 1920 > r
                      ? 1920
                      : 3e3 > r
                        ? 3e3
                        : 4320 > r
                          ? 4320
                          : 1960 * mg(r / 1960)) - r),
            10 < r)
          ) {
            e.timeoutHandle = gl(pn.bind(null, e, De, kt), r);
            break;
          }
          pn(e, De, kt);
          break;
        case 5:
          pn(e, De, kt);
          break;
        default:
          throw Error(R(329));
      }
    }
  }
  return (Ae(e, oe()), e.callbackNode === n ? ip.bind(null, e) : null);
}
function Al(e, t) {
  var n = Hr;
  return (
    e.current.memoizedState.isDehydrated && (Sn(e, t).flags |= 256),
    (e = ji(e, t)),
    e !== 2 && ((t = De), (De = n), t !== null && Fl(t)),
    e
  );
}
function Fl(e) {
  De === null ? (De = e) : De.push.apply(De, e);
}
function vg(e) {
  for (var t = e; ; ) {
    if (t.flags & 16384) {
      var n = t.updateQueue;
      if (n !== null && ((n = n.stores), n !== null))
        for (var r = 0; r < n.length; r++) {
          var o = n[r],
            i = o.getSnapshot;
          o = o.value;
          try {
            if (!at(i(), o)) return !1;
          } catch {
            return !1;
          }
        }
    }
    if (((n = t.child), t.subtreeFlags & 16384 && n !== null))
      ((n.return = t), (t = n));
    else {
      if (t === e) break;
      for (; t.sibling === null; ) {
        if (t.return === null || t.return === e) return !0;
        t = t.return;
      }
      ((t.sibling.return = t.return), (t = t.sibling));
    }
  }
  return !0;
}
function Vt(e, t) {
  for (
    t &= ~Oa,
      t &= ~Ki,
      e.suspendedLanes |= t,
      e.pingedLanes &= ~t,
      e = e.expirationTimes;
    0 < t;

  ) {
    var n = 31 - st(t),
      r = 1 << n;
    ((e[n] = -1), (t &= ~r));
  }
}
function pc(e) {
  if (W & 6) throw Error(R(327));
  Jn();
  var t = hi(e, 0);
  if (!(t & 1)) return (Ae(e, oe()), null);
  var n = ji(e, t);
  if (e.tag !== 0 && n === 2) {
    var r = ul(e);
    r !== 0 && ((t = r), (n = Al(e, r)));
  }
  if (n === 1) throw ((n = co), Sn(e, 0), Vt(e, t), Ae(e, oe()), n);
  if (n === 6) throw Error(R(345));
  return (
    (e.finishedWork = e.current.alternate),
    (e.finishedLanes = t),
    pn(e, De, kt),
    Ae(e, oe()),
    null
  );
}
function Da(e, t) {
  var n = W;
  W |= 1;
  try {
    return e(t);
  } finally {
    ((W = n), W === 0 && ((gr = oe() + 500), Vi && cn()));
  }
}
function Tn(e) {
  Yt !== null && Yt.tag === 0 && !(W & 6) && Jn();
  var t = W;
  W |= 1;
  var n = qe.transition,
    r = B;
  try {
    if (((qe.transition = null), (B = 1), e)) return e();
  } finally {
    ((B = r), (qe.transition = n), (W = t), !(W & 6) && cn());
  }
}
function ba() {
  ((ze = Kn.current), X(Kn));
}
function Sn(e, t) {
  ((e.finishedWork = null), (e.finishedLanes = 0));
  var n = e.timeoutHandle;
  if ((n !== -1 && ((e.timeoutHandle = -1), Qv(n)), se !== null))
    for (n = se.return; n !== null; ) {
      var r = n;
      switch ((ha(r), r.tag)) {
        case 1:
          ((r = r.type.childContextTypes), r != null && wi());
          break;
        case 3:
          (mr(), X(Ie), X(Ce), Ca());
          break;
        case 5:
          Ea(r);
          break;
        case 4:
          mr();
          break;
        case 13:
          X(q);
          break;
        case 19:
          X(q);
          break;
        case 10:
          ya(r.type._context);
          break;
        case 22:
        case 23:
          ba();
      }
      n = n.return;
    }
  if (
    ((he = e),
    (se = e = on(e.current, null)),
    (ve = ze = t),
    (ue = 0),
    (co = null),
    (Oa = Ki = Nn = 0),
    (De = Hr = null),
    mn !== null)
  ) {
    for (t = 0; t < mn.length; t++)
      if (((n = mn[t]), (r = n.interleaved), r !== null)) {
        n.interleaved = null;
        var o = r.next,
          i = n.pending;
        if (i !== null) {
          var s = i.next;
          ((i.next = o), (r.next = s));
        }
        n.pending = r;
      }
    mn = null;
  }
  return e;
}
function sp(e, t) {
  do {
    var n = se;
    try {
      if ((ga(), (ei.current = Ri), Ti)) {
        for (var r = Z.memoizedState; r !== null; ) {
          var o = r.queue;
          (o !== null && (o.pending = null), (r = r.next));
        }
        Ti = !1;
      }
      if (
        ((Pn = 0),
        (fe = ae = Z = null),
        (Vr = !1),
        (lo = 0),
        (Ma.current = null),
        n === null || n.return === null)
      ) {
        ((ue = 1), (co = t), (se = null));
        break;
      }
      e: {
        var i = e,
          s = n.return,
          l = n,
          a = t;
        if (
          ((t = ve),
          (l.flags |= 32768),
          a !== null && typeof a == "object" && typeof a.then == "function")
        ) {
          var u = a,
            v = l,
            f = v.tag;
          if (!(v.mode & 1) && (f === 0 || f === 11 || f === 15)) {
            var m = v.alternate;
            m
              ? ((v.updateQueue = m.updateQueue),
                (v.memoizedState = m.memoizedState),
                (v.lanes = m.lanes))
              : ((v.updateQueue = null), (v.memoizedState = null));
          }
          var x = ec(s);
          if (x !== null) {
            ((x.flags &= -257),
              tc(x, s, l, i, t),
              x.mode & 1 && Ju(i, u, t),
              (t = x),
              (a = u));
            var E = t.updateQueue;
            if (E === null) {
              var g = new Set();
              (g.add(a), (t.updateQueue = g));
            } else E.add(a);
            break e;
          } else {
            if (!(t & 1)) {
              (Ju(i, u, t), Ia());
              break e;
            }
            a = Error(R(426));
          }
        } else if (Y && l.mode & 1) {
          var w = ec(s);
          if (w !== null) {
            (!(w.flags & 65536) && (w.flags |= 256),
              tc(w, s, l, i, t),
              ma(vr(a, l)));
            break e;
          }
        }
        ((i = a = vr(a, l)),
          ue !== 4 && (ue = 2),
          Hr === null ? (Hr = [i]) : Hr.push(i),
          (i = s));
        do {
          switch (i.tag) {
            case 3:
              ((i.flags |= 65536), (t &= -t), (i.lanes |= t));
              var d = Vf(i, a, t);
              Ku(i, d);
              break e;
            case 1:
              l = a;
              var c = i.type,
                p = i.stateNode;
              if (
                !(i.flags & 128) &&
                (typeof c.getDerivedStateFromError == "function" ||
                  (p !== null &&
                    typeof p.componentDidCatch == "function" &&
                    (nn === null || !nn.has(p))))
              ) {
                ((i.flags |= 65536), (t &= -t), (i.lanes |= t));
                var S = Bf(i, l, t);
                Ku(i, S);
                break e;
              }
          }
          i = i.return;
        } while (i !== null);
      }
      up(n);
    } catch (C) {
      ((t = C), se === n && n !== null && (se = n = n.return));
      continue;
    }
    break;
  } while (!0);
}
function lp() {
  var e = _i.current;
  return ((_i.current = Ri), e === null ? Ri : e);
}
function Ia() {
  ((ue === 0 || ue === 3 || ue === 2) && (ue = 4),
    he === null || (!(Nn & 268435455) && !(Ki & 268435455)) || Vt(he, ve));
}
function ji(e, t) {
  var n = W;
  W |= 2;
  var r = lp();
  (he !== e || ve !== t) && ((kt = null), Sn(e, t));
  do
    try {
      gg();
      break;
    } catch (o) {
      sp(e, o);
    }
  while (!0);
  if ((ga(), (W = n), (_i.current = r), se !== null)) throw Error(R(261));
  return ((he = null), (ve = 0), ue);
}
function gg() {
  for (; se !== null; ) ap(se);
}
function yg() {
  for (; se !== null && !Vm(); ) ap(se);
}
function ap(e) {
  var t = dp(e.alternate, e, ze);
  ((e.memoizedProps = e.pendingProps),
    t === null ? up(e) : (se = t),
    (Ma.current = null));
}
function up(e) {
  var t = e;
  do {
    var n = t.alternate;
    if (((e = t.return), t.flags & 32768)) {
      if (((n = dg(n, t)), n !== null)) {
        ((n.flags &= 32767), (se = n));
        return;
      }
      if (e !== null)
        ((e.flags |= 32768), (e.subtreeFlags = 0), (e.deletions = null));
      else {
        ((ue = 6), (se = null));
        return;
      }
    } else if (((n = cg(n, t, ze)), n !== null)) {
      se = n;
      return;
    }
    if (((t = t.sibling), t !== null)) {
      se = t;
      return;
    }
    se = t = e;
  } while (t !== null);
  ue === 0 && (ue = 5);
}
function pn(e, t, n) {
  var r = B,
    o = qe.transition;
  try {
    ((qe.transition = null), (B = 1), wg(e, t, n, r));
  } finally {
    ((qe.transition = o), (B = r));
  }
  return null;
}
function wg(e, t, n, r) {
  do Jn();
  while (Yt !== null);
  if (W & 6) throw Error(R(327));
  n = e.finishedWork;
  var o = e.finishedLanes;
  if (n === null) return null;
  if (((e.finishedWork = null), (e.finishedLanes = 0), n === e.current))
    throw Error(R(177));
  ((e.callbackNode = null), (e.callbackPriority = 0));
  var i = n.lanes | n.childLanes;
  if (
    (Jm(e, i),
    e === he && ((se = he = null), (ve = 0)),
    (!(n.subtreeFlags & 2064) && !(n.flags & 2064)) ||
      zo ||
      ((zo = !0),
      fp(pi, function () {
        return (Jn(), null);
      })),
    (i = (n.flags & 15990) !== 0),
    n.subtreeFlags & 15990 || i)
  ) {
    ((i = qe.transition), (qe.transition = null));
    var s = B;
    B = 1;
    var l = W;
    ((W |= 4),
      (Ma.current = null),
      pg(e, n),
      rp(n, e),
      zv(ml),
      (mi = !!hl),
      (ml = hl = null),
      (e.current = n),
      hg(n),
      Bm(),
      (W = l),
      (B = s),
      (qe.transition = i));
  } else e.current = n;
  if (
    (zo && ((zo = !1), (Yt = e), (Oi = o)),
    (i = e.pendingLanes),
    i === 0 && (nn = null),
    Km(n.stateNode),
    Ae(e, oe()),
    t !== null)
  )
    for (r = e.onRecoverableError, n = 0; n < t.length; n++)
      ((o = t[n]), r(o.value, { componentStack: o.stack, digest: o.digest }));
  if (Mi) throw ((Mi = !1), (e = Il), (Il = null), e);
  return (
    Oi & 1 && e.tag !== 0 && Jn(),
    (i = e.pendingLanes),
    i & 1 ? (e === Ll ? Qr++ : ((Qr = 0), (Ll = e))) : (Qr = 0),
    cn(),
    null
  );
}
function Jn() {
  if (Yt !== null) {
    var e = Wd(Oi),
      t = qe.transition,
      n = B;
    try {
      if (((qe.transition = null), (B = 16 > e ? 16 : e), Yt === null))
        var r = !1;
      else {
        if (((e = Yt), (Yt = null), (Oi = 0), W & 6)) throw Error(R(331));
        var o = W;
        for (W |= 4, O = e.current; O !== null; ) {
          var i = O,
            s = i.child;
          if (O.flags & 16) {
            var l = i.deletions;
            if (l !== null) {
              for (var a = 0; a < l.length; a++) {
                var u = l[a];
                for (O = u; O !== null; ) {
                  var v = O;
                  switch (v.tag) {
                    case 0:
                    case 11:
                    case 15:
                      Br(8, v, i);
                  }
                  var f = v.child;
                  if (f !== null) ((f.return = v), (O = f));
                  else
                    for (; O !== null; ) {
                      v = O;
                      var m = v.sibling,
                        x = v.return;
                      if ((ep(v), v === u)) {
                        O = null;
                        break;
                      }
                      if (m !== null) {
                        ((m.return = x), (O = m));
                        break;
                      }
                      O = x;
                    }
                }
              }
              var E = i.alternate;
              if (E !== null) {
                var g = E.child;
                if (g !== null) {
                  E.child = null;
                  do {
                    var w = g.sibling;
                    ((g.sibling = null), (g = w));
                  } while (g !== null);
                }
              }
              O = i;
            }
          }
          if (i.subtreeFlags & 2064 && s !== null) ((s.return = i), (O = s));
          else
            e: for (; O !== null; ) {
              if (((i = O), i.flags & 2048))
                switch (i.tag) {
                  case 0:
                  case 11:
                  case 15:
                    Br(9, i, i.return);
                }
              var d = i.sibling;
              if (d !== null) {
                ((d.return = i.return), (O = d));
                break e;
              }
              O = i.return;
            }
        }
        var c = e.current;
        for (O = c; O !== null; ) {
          s = O;
          var p = s.child;
          if (s.subtreeFlags & 2064 && p !== null) ((p.return = s), (O = p));
          else
            e: for (s = c; O !== null; ) {
              if (((l = O), l.flags & 2048))
                try {
                  switch (l.tag) {
                    case 0:
                    case 11:
                    case 15:
                      Qi(9, l);
                  }
                } catch (C) {
                  ne(l, l.return, C);
                }
              if (l === s) {
                O = null;
                break e;
              }
              var S = l.sibling;
              if (S !== null) {
                ((S.return = l.return), (O = S));
                break e;
              }
              O = l.return;
            }
        }
        if (
          ((W = o), cn(), wt && typeof wt.onPostCommitFiberRoot == "function")
        )
          try {
            wt.onPostCommitFiberRoot(Fi, e);
          } catch {}
        r = !0;
      }
      return r;
    } finally {
      ((B = n), (qe.transition = t));
    }
  }
  return !1;
}
function hc(e, t, n) {
  ((t = vr(n, t)),
    (t = Vf(e, t, 1)),
    (e = tn(e, t, 1)),
    (t = _e()),
    e !== null && (yo(e, 1, t), Ae(e, t)));
}
function ne(e, t, n) {
  if (e.tag === 3) hc(e, e, n);
  else
    for (; t !== null; ) {
      if (t.tag === 3) {
        hc(t, e, n);
        break;
      } else if (t.tag === 1) {
        var r = t.stateNode;
        if (
          typeof t.type.getDerivedStateFromError == "function" ||
          (typeof r.componentDidCatch == "function" &&
            (nn === null || !nn.has(r)))
        ) {
          ((e = vr(n, e)),
            (e = Bf(t, e, 1)),
            (t = tn(t, e, 1)),
            (e = _e()),
            t !== null && (yo(t, 1, e), Ae(t, e)));
          break;
        }
      }
      t = t.return;
    }
}
function xg(e, t, n) {
  var r = e.pingCache;
  (r !== null && r.delete(t),
    (t = _e()),
    (e.pingedLanes |= e.suspendedLanes & n),
    he === e &&
      (ve & n) === n &&
      (ue === 4 || (ue === 3 && (ve & 130023424) === ve && 500 > oe() - ja)
        ? Sn(e, 0)
        : (Oa |= n)),
    Ae(e, t));
}
function cp(e, t) {
  t === 0 &&
    (e.mode & 1
      ? ((t = _o), (_o <<= 1), !(_o & 130023424) && (_o = 4194304))
      : (t = 1));
  var n = _e();
  ((e = Ot(e, t)), e !== null && (yo(e, t, n), Ae(e, n)));
}
function Sg(e) {
  var t = e.memoizedState,
    n = 0;
  (t !== null && (n = t.retryLane), cp(e, n));
}
function Eg(e, t) {
  var n = 0;
  switch (e.tag) {
    case 13:
      var r = e.stateNode,
        o = e.memoizedState;
      o !== null && (n = o.retryLane);
      break;
    case 19:
      r = e.stateNode;
      break;
    default:
      throw Error(R(314));
  }
  (r !== null && r.delete(t), cp(e, n));
}
var dp;
dp = function (e, t, n) {
  if (e !== null)
    if (e.memoizedProps !== t.pendingProps || Ie.current) be = !0;
    else {
      if (!(e.lanes & n) && !(t.flags & 128)) return ((be = !1), ug(e, t, n));
      be = !!(e.flags & 131072);
    }
  else ((be = !1), Y && t.flags & 1048576 && mf(t, Ei, t.index));
  switch (((t.lanes = 0), t.tag)) {
    case 2:
      var r = t.type;
      (ni(e, t), (e = t.pendingProps));
      var o = fr(t, Ce.current);
      (Zn(t, n), (o = Pa(null, t, r, e, o, n)));
      var i = Na();
      return (
        (t.flags |= 1),
        typeof o == "object" &&
        o !== null &&
        typeof o.render == "function" &&
        o.$$typeof === void 0
          ? ((t.tag = 1),
            (t.memoizedState = null),
            (t.updateQueue = null),
            Le(r) ? ((i = !0), xi(t)) : (i = !1),
            (t.memoizedState =
              o.state !== null && o.state !== void 0 ? o.state : null),
            xa(t),
            (o.updater = Hi),
            (t.stateNode = o),
            (o._reactInternals = t),
            kl(t, r, e, n),
            (t = Tl(null, t, r, !0, i, n)))
          : ((t.tag = 0), Y && i && pa(t), Te(null, t, o, n), (t = t.child)),
        t
      );
    case 16:
      r = t.elementType;
      e: {
        switch (
          (ni(e, t),
          (e = t.pendingProps),
          (o = r._init),
          (r = o(r._payload)),
          (t.type = r),
          (o = t.tag = kg(r)),
          (e = tt(r, e)),
          o)
        ) {
          case 0:
            t = Nl(null, t, r, e, n);
            break e;
          case 1:
            t = oc(null, t, r, e, n);
            break e;
          case 11:
            t = nc(null, t, r, e, n);
            break e;
          case 14:
            t = rc(null, t, r, tt(r.type, e), n);
            break e;
        }
        throw Error(R(306, r, ""));
      }
      return t;
    case 0:
      return (
        (r = t.type),
        (o = t.pendingProps),
        (o = t.elementType === r ? o : tt(r, o)),
        Nl(e, t, r, o, n)
      );
    case 1:
      return (
        (r = t.type),
        (o = t.pendingProps),
        (o = t.elementType === r ? o : tt(r, o)),
        oc(e, t, r, o, n)
      );
    case 3:
      e: {
        if ((Gf(t), e === null)) throw Error(R(387));
        ((r = t.pendingProps),
          (i = t.memoizedState),
          (o = i.element),
          Sf(e, t),
          Pi(t, r, null, n));
        var s = t.memoizedState;
        if (((r = s.element), i.isDehydrated))
          if (
            ((i = {
              element: r,
              isDehydrated: !1,
              cache: s.cache,
              pendingSuspenseBoundaries: s.pendingSuspenseBoundaries,
              transitions: s.transitions,
            }),
            (t.updateQueue.baseState = i),
            (t.memoizedState = i),
            t.flags & 256)
          ) {
            ((o = vr(Error(R(423)), t)), (t = ic(e, t, r, n, o)));
            break e;
          } else if (r !== o) {
            ((o = vr(Error(R(424)), t)), (t = ic(e, t, r, n, o)));
            break e;
          } else
            for (
              $e = en(t.stateNode.containerInfo.firstChild),
                Ue = t,
                Y = !0,
                it = null,
                n = wf(t, null, r, n),
                t.child = n;
              n;

            )
              ((n.flags = (n.flags & -3) | 4096), (n = n.sibling));
        else {
          if ((pr(), r === o)) {
            t = jt(e, t, n);
            break e;
          }
          Te(e, t, r, n);
        }
        t = t.child;
      }
      return t;
    case 5:
      return (
        Ef(t),
        e === null && Sl(t),
        (r = t.type),
        (o = t.pendingProps),
        (i = e !== null ? e.memoizedProps : null),
        (s = o.children),
        vl(r, o) ? (s = null) : i !== null && vl(r, i) && (t.flags |= 32),
        Kf(e, t),
        Te(e, t, s, n),
        t.child
      );
    case 6:
      return (e === null && Sl(t), null);
    case 13:
      return Xf(e, t, n);
    case 4:
      return (
        Sa(t, t.stateNode.containerInfo),
        (r = t.pendingProps),
        e === null ? (t.child = hr(t, null, r, n)) : Te(e, t, r, n),
        t.child
      );
    case 11:
      return (
        (r = t.type),
        (o = t.pendingProps),
        (o = t.elementType === r ? o : tt(r, o)),
        nc(e, t, r, o, n)
      );
    case 7:
      return (Te(e, t, t.pendingProps, n), t.child);
    case 8:
      return (Te(e, t, t.pendingProps.children, n), t.child);
    case 12:
      return (Te(e, t, t.pendingProps.children, n), t.child);
    case 10:
      e: {
        if (
          ((r = t.type._context),
          (o = t.pendingProps),
          (i = t.memoizedProps),
          (s = o.value),
          Q(Ci, r._currentValue),
          (r._currentValue = s),
          i !== null)
        )
          if (at(i.value, s)) {
            if (i.children === o.children && !Ie.current) {
              t = jt(e, t, n);
              break e;
            }
          } else
            for (i = t.child, i !== null && (i.return = t); i !== null; ) {
              var l = i.dependencies;
              if (l !== null) {
                s = i.child;
                for (var a = l.firstContext; a !== null; ) {
                  if (a.context === r) {
                    if (i.tag === 1) {
                      ((a = Rt(-1, n & -n)), (a.tag = 2));
                      var u = i.updateQueue;
                      if (u !== null) {
                        u = u.shared;
                        var v = u.pending;
                        (v === null
                          ? (a.next = a)
                          : ((a.next = v.next), (v.next = a)),
                          (u.pending = a));
                      }
                    }
                    ((i.lanes |= n),
                      (a = i.alternate),
                      a !== null && (a.lanes |= n),
                      El(i.return, n, t),
                      (l.lanes |= n));
                    break;
                  }
                  a = a.next;
                }
              } else if (i.tag === 10) s = i.type === t.type ? null : i.child;
              else if (i.tag === 18) {
                if (((s = i.return), s === null)) throw Error(R(341));
                ((s.lanes |= n),
                  (l = s.alternate),
                  l !== null && (l.lanes |= n),
                  El(s, n, t),
                  (s = i.sibling));
              } else s = i.child;
              if (s !== null) s.return = i;
              else
                for (s = i; s !== null; ) {
                  if (s === t) {
                    s = null;
                    break;
                  }
                  if (((i = s.sibling), i !== null)) {
                    ((i.return = s.return), (s = i));
                    break;
                  }
                  s = s.return;
                }
              i = s;
            }
        (Te(e, t, o.children, n), (t = t.child));
      }
      return t;
    case 9:
      return (
        (o = t.type),
        (r = t.pendingProps.children),
        Zn(t, n),
        (o = Ze(o)),
        (r = r(o)),
        (t.flags |= 1),
        Te(e, t, r, n),
        t.child
      );
    case 14:
      return (
        (r = t.type),
        (o = tt(r, t.pendingProps)),
        (o = tt(r.type, o)),
        rc(e, t, r, o, n)
      );
    case 15:
      return Hf(e, t, t.type, t.pendingProps, n);
    case 17:
      return (
        (r = t.type),
        (o = t.pendingProps),
        (o = t.elementType === r ? o : tt(r, o)),
        ni(e, t),
        (t.tag = 1),
        Le(r) ? ((e = !0), xi(t)) : (e = !1),
        Zn(t, n),
        Wf(t, r, o),
        kl(t, r, o, n),
        Tl(null, t, r, !0, e, n)
      );
    case 19:
      return Yf(e, t, n);
    case 22:
      return Qf(e, t, n);
  }
  throw Error(R(156, t.tag));
};
function fp(e, t) {
  return Fd(e, t);
}
function Cg(e, t, n, r) {
  ((this.tag = e),
    (this.key = n),
    (this.sibling =
      this.child =
      this.return =
      this.stateNode =
      this.type =
      this.elementType =
        null),
    (this.index = 0),
    (this.ref = null),
    (this.pendingProps = t),
    (this.dependencies =
      this.memoizedState =
      this.updateQueue =
      this.memoizedProps =
        null),
    (this.mode = r),
    (this.subtreeFlags = this.flags = 0),
    (this.deletions = null),
    (this.childLanes = this.lanes = 0),
    (this.alternate = null));
}
function Ye(e, t, n, r) {
  return new Cg(e, t, n, r);
}
function La(e) {
  return ((e = e.prototype), !(!e || !e.isReactComponent));
}
function kg(e) {
  if (typeof e == "function") return La(e) ? 1 : 0;
  if (e != null) {
    if (((e = e.$$typeof), e === ta)) return 11;
    if (e === na) return 14;
  }
  return 2;
}
function on(e, t) {
  var n = e.alternate;
  return (
    n === null
      ? ((n = Ye(e.tag, t, e.key, e.mode)),
        (n.elementType = e.elementType),
        (n.type = e.type),
        (n.stateNode = e.stateNode),
        (n.alternate = e),
        (e.alternate = n))
      : ((n.pendingProps = t),
        (n.type = e.type),
        (n.flags = 0),
        (n.subtreeFlags = 0),
        (n.deletions = null)),
    (n.flags = e.flags & 14680064),
    (n.childLanes = e.childLanes),
    (n.lanes = e.lanes),
    (n.child = e.child),
    (n.memoizedProps = e.memoizedProps),
    (n.memoizedState = e.memoizedState),
    (n.updateQueue = e.updateQueue),
    (t = e.dependencies),
    (n.dependencies =
      t === null ? null : { lanes: t.lanes, firstContext: t.firstContext }),
    (n.sibling = e.sibling),
    (n.index = e.index),
    (n.ref = e.ref),
    n
  );
}
function ii(e, t, n, r, o, i) {
  var s = 2;
  if (((r = e), typeof e == "function")) La(e) && (s = 1);
  else if (typeof e == "string") s = 5;
  else
    e: switch (e) {
      case An:
        return En(n.children, o, i, t);
      case ea:
        ((s = 8), (o |= 8));
        break;
      case Gs:
        return (
          (e = Ye(12, n, t, o | 2)),
          (e.elementType = Gs),
          (e.lanes = i),
          e
        );
      case Xs:
        return ((e = Ye(13, n, t, o)), (e.elementType = Xs), (e.lanes = i), e);
      case Ys:
        return ((e = Ye(19, n, t, o)), (e.elementType = Ys), (e.lanes = i), e);
      case Sd:
        return Gi(n, o, i, t);
      default:
        if (typeof e == "object" && e !== null)
          switch (e.$$typeof) {
            case wd:
              s = 10;
              break e;
            case xd:
              s = 9;
              break e;
            case ta:
              s = 11;
              break e;
            case na:
              s = 14;
              break e;
            case $t:
              ((s = 16), (r = null));
              break e;
          }
        throw Error(R(130, e == null ? e : typeof e, ""));
    }
  return (
    (t = Ye(s, n, t, o)),
    (t.elementType = e),
    (t.type = r),
    (t.lanes = i),
    t
  );
}
function En(e, t, n, r) {
  return ((e = Ye(7, e, r, t)), (e.lanes = n), e);
}
function Gi(e, t, n, r) {
  return (
    (e = Ye(22, e, r, t)),
    (e.elementType = Sd),
    (e.lanes = n),
    (e.stateNode = { isHidden: !1 }),
    e
  );
}
function Os(e, t, n) {
  return ((e = Ye(6, e, null, t)), (e.lanes = n), e);
}
function js(e, t, n) {
  return (
    (t = Ye(4, e.children !== null ? e.children : [], e.key, t)),
    (t.lanes = n),
    (t.stateNode = {
      containerInfo: e.containerInfo,
      pendingChildren: null,
      implementation: e.implementation,
    }),
    t
  );
}
function Pg(e, t, n, r, o) {
  ((this.tag = t),
    (this.containerInfo = e),
    (this.finishedWork =
      this.pingCache =
      this.current =
      this.pendingChildren =
        null),
    (this.timeoutHandle = -1),
    (this.callbackNode = this.pendingContext = this.context = null),
    (this.callbackPriority = 0),
    (this.eventTimes = fs(0)),
    (this.expirationTimes = fs(-1)),
    (this.entangledLanes =
      this.finishedLanes =
      this.mutableReadLanes =
      this.expiredLanes =
      this.pingedLanes =
      this.suspendedLanes =
      this.pendingLanes =
        0),
    (this.entanglements = fs(0)),
    (this.identifierPrefix = r),
    (this.onRecoverableError = o),
    (this.mutableSourceEagerHydrationData = null));
}
function Aa(e, t, n, r, o, i, s, l, a) {
  return (
    (e = new Pg(e, t, n, l, a)),
    t === 1 ? ((t = 1), i === !0 && (t |= 8)) : (t = 0),
    (i = Ye(3, null, null, t)),
    (e.current = i),
    (i.stateNode = e),
    (i.memoizedState = {
      element: r,
      isDehydrated: n,
      cache: null,
      transitions: null,
      pendingSuspenseBoundaries: null,
    }),
    xa(i),
    e
  );
}
function Ng(e, t, n) {
  var r = 3 < arguments.length && arguments[3] !== void 0 ? arguments[3] : null;
  return {
    $$typeof: Ln,
    key: r == null ? null : "" + r,
    children: e,
    containerInfo: t,
    implementation: n,
  };
}
function pp(e) {
  if (!e) return ln;
  e = e._reactInternals;
  e: {
    if (Mn(e) !== e || e.tag !== 1) throw Error(R(170));
    var t = e;
    do {
      switch (t.tag) {
        case 3:
          t = t.stateNode.context;
          break e;
        case 1:
          if (Le(t.type)) {
            t = t.stateNode.__reactInternalMemoizedMergedChildContext;
            break e;
          }
      }
      t = t.return;
    } while (t !== null);
    throw Error(R(171));
  }
  if (e.tag === 1) {
    var n = e.type;
    if (Le(n)) return pf(e, n, t);
  }
  return t;
}
function hp(e, t, n, r, o, i, s, l, a) {
  return (
    (e = Aa(n, r, !0, e, o, i, s, l, a)),
    (e.context = pp(null)),
    (n = e.current),
    (r = _e()),
    (o = rn(n)),
    (i = Rt(r, o)),
    (i.callback = t ?? null),
    tn(n, i, o),
    (e.current.lanes = o),
    yo(e, o, r),
    Ae(e, r),
    e
  );
}
function Xi(e, t, n, r) {
  var o = t.current,
    i = _e(),
    s = rn(o);
  return (
    (n = pp(n)),
    t.context === null ? (t.context = n) : (t.pendingContext = n),
    (t = Rt(i, s)),
    (t.payload = { element: e }),
    (r = r === void 0 ? null : r),
    r !== null && (t.callback = r),
    (e = tn(o, t, s)),
    e !== null && (lt(e, o, s, i), Jo(e, o, s)),
    s
  );
}
function Di(e) {
  if (((e = e.current), !e.child)) return null;
  switch (e.child.tag) {
    case 5:
      return e.child.stateNode;
    default:
      return e.child.stateNode;
  }
}
function mc(e, t) {
  if (((e = e.memoizedState), e !== null && e.dehydrated !== null)) {
    var n = e.retryLane;
    e.retryLane = n !== 0 && n < t ? n : t;
  }
}
function Fa(e, t) {
  (mc(e, t), (e = e.alternate) && mc(e, t));
}
function Tg() {
  return null;
}
var mp =
  typeof reportError == "function"
    ? reportError
    : function (e) {
        console.error(e);
      };
function za(e) {
  this._internalRoot = e;
}
Yi.prototype.render = za.prototype.render = function (e) {
  var t = this._internalRoot;
  if (t === null) throw Error(R(409));
  Xi(e, t, null, null);
};
Yi.prototype.unmount = za.prototype.unmount = function () {
  var e = this._internalRoot;
  if (e !== null) {
    this._internalRoot = null;
    var t = e.containerInfo;
    (Tn(function () {
      Xi(null, e, null, null);
    }),
      (t[Mt] = null));
  }
};
function Yi(e) {
  this._internalRoot = e;
}
Yi.prototype.unstable_scheduleHydration = function (e) {
  if (e) {
    var t = Hd();
    e = { blockedOn: null, target: e, priority: t };
    for (var n = 0; n < Wt.length && t !== 0 && t < Wt[n].priority; n++);
    (Wt.splice(n, 0, e), n === 0 && Kd(e));
  }
};
function $a(e) {
  return !(!e || (e.nodeType !== 1 && e.nodeType !== 9 && e.nodeType !== 11));
}
function qi(e) {
  return !(
    !e ||
    (e.nodeType !== 1 &&
      e.nodeType !== 9 &&
      e.nodeType !== 11 &&
      (e.nodeType !== 8 || e.nodeValue !== " react-mount-point-unstable "))
  );
}
function vc() {}
function Rg(e, t, n, r, o) {
  if (o) {
    if (typeof r == "function") {
      var i = r;
      r = function () {
        var u = Di(s);
        i.call(u);
      };
    }
    var s = hp(t, r, e, 0, null, !1, !1, "", vc);
    return (
      (e._reactRootContainer = s),
      (e[Mt] = s.current),
      no(e.nodeType === 8 ? e.parentNode : e),
      Tn(),
      s
    );
  }
  for (; (o = e.lastChild); ) e.removeChild(o);
  if (typeof r == "function") {
    var l = r;
    r = function () {
      var u = Di(a);
      l.call(u);
    };
  }
  var a = Aa(e, 0, !1, null, null, !1, !1, "", vc);
  return (
    (e._reactRootContainer = a),
    (e[Mt] = a.current),
    no(e.nodeType === 8 ? e.parentNode : e),
    Tn(function () {
      Xi(t, a, n, r);
    }),
    a
  );
}
function Zi(e, t, n, r, o) {
  var i = n._reactRootContainer;
  if (i) {
    var s = i;
    if (typeof o == "function") {
      var l = o;
      o = function () {
        var a = Di(s);
        l.call(a);
      };
    }
    Xi(t, s, e, o);
  } else s = Rg(n, t, e, o, r);
  return Di(s);
}
Vd = function (e) {
  switch (e.tag) {
    case 3:
      var t = e.stateNode;
      if (t.current.memoizedState.isDehydrated) {
        var n = Lr(t.pendingLanes);
        n !== 0 &&
          (ia(t, n | 1), Ae(t, oe()), !(W & 6) && ((gr = oe() + 500), cn()));
      }
      break;
    case 13:
      (Tn(function () {
        var r = Ot(e, 1);
        if (r !== null) {
          var o = _e();
          lt(r, e, 1, o);
        }
      }),
        Fa(e, 1));
  }
};
sa = function (e) {
  if (e.tag === 13) {
    var t = Ot(e, 134217728);
    if (t !== null) {
      var n = _e();
      lt(t, e, 134217728, n);
    }
    Fa(e, 134217728);
  }
};
Bd = function (e) {
  if (e.tag === 13) {
    var t = rn(e),
      n = Ot(e, t);
    if (n !== null) {
      var r = _e();
      lt(n, e, t, r);
    }
    Fa(e, t);
  }
};
Hd = function () {
  return B;
};
Qd = function (e, t) {
  var n = B;
  try {
    return ((B = e), t());
  } finally {
    B = n;
  }
};
sl = function (e, t, n) {
  switch (t) {
    case "input":
      if ((Js(e, n), (t = n.name), n.type === "radio" && t != null)) {
        for (n = e; n.parentNode; ) n = n.parentNode;
        for (
          n = n.querySelectorAll(
            "input[name=" + JSON.stringify("" + t) + '][type="radio"]',
          ),
            t = 0;
          t < n.length;
          t++
        ) {
          var r = n[t];
          if (r !== e && r.form === e.form) {
            var o = Wi(r);
            if (!o) throw Error(R(90));
            (Cd(r), Js(r, o));
          }
        }
      }
      break;
    case "textarea":
      Pd(e, n);
      break;
    case "select":
      ((t = n.value), t != null && Gn(e, !!n.multiple, t, !1));
  }
};
jd = Da;
Dd = Tn;
var _g = { usingClientEntryPoint: !1, Events: [xo, Un, Wi, Md, Od, Da] },
  Or = {
    findFiberByHostInstance: hn,
    bundleType: 0,
    version: "18.3.1",
    rendererPackageName: "react-dom",
  },
  Mg = {
    bundleType: Or.bundleType,
    version: Or.version,
    rendererPackageName: Or.rendererPackageName,
    rendererConfig: Or.rendererConfig,
    overrideHookState: null,
    overrideHookStateDeletePath: null,
    overrideHookStateRenamePath: null,
    overrideProps: null,
    overridePropsDeletePath: null,
    overridePropsRenamePath: null,
    setErrorHandler: null,
    setSuspenseHandler: null,
    scheduleUpdate: null,
    currentDispatcherRef: Dt.ReactCurrentDispatcher,
    findHostInstanceByFiber: function (e) {
      return ((e = Ld(e)), e === null ? null : e.stateNode);
    },
    findFiberByHostInstance: Or.findFiberByHostInstance || Tg,
    findHostInstancesForRefresh: null,
    scheduleRefresh: null,
    scheduleRoot: null,
    setRefreshHandler: null,
    getCurrentFiber: null,
    reconcilerVersion: "18.3.1-next-f1338f8080-20240426",
  };
if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ < "u") {
  var $o = __REACT_DEVTOOLS_GLOBAL_HOOK__;
  if (!$o.isDisabled && $o.supportsFiber)
    try {
      ((Fi = $o.inject(Mg)), (wt = $o));
    } catch {}
}
Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = _g;
Ve.createPortal = function (e, t) {
  var n = 2 < arguments.length && arguments[2] !== void 0 ? arguments[2] : null;
  if (!$a(t)) throw Error(R(200));
  return Ng(e, t, null, n);
};
Ve.createRoot = function (e, t) {
  if (!$a(e)) throw Error(R(299));
  var n = !1,
    r = "",
    o = mp;
  return (
    t != null &&
      (t.unstable_strictMode === !0 && (n = !0),
      t.identifierPrefix !== void 0 && (r = t.identifierPrefix),
      t.onRecoverableError !== void 0 && (o = t.onRecoverableError)),
    (t = Aa(e, 1, !1, null, null, n, !1, r, o)),
    (e[Mt] = t.current),
    no(e.nodeType === 8 ? e.parentNode : e),
    new za(t)
  );
};
Ve.findDOMNode = function (e) {
  if (e == null) return null;
  if (e.nodeType === 1) return e;
  var t = e._reactInternals;
  if (t === void 0)
    throw typeof e.render == "function"
      ? Error(R(188))
      : ((e = Object.keys(e).join(",")), Error(R(268, e)));
  return ((e = Ld(t)), (e = e === null ? null : e.stateNode), e);
};
Ve.flushSync = function (e) {
  return Tn(e);
};
Ve.hydrate = function (e, t, n) {
  if (!qi(t)) throw Error(R(200));
  return Zi(null, e, t, !0, n);
};
Ve.hydrateRoot = function (e, t, n) {
  if (!$a(e)) throw Error(R(405));
  var r = (n != null && n.hydratedSources) || null,
    o = !1,
    i = "",
    s = mp;
  if (
    (n != null &&
      (n.unstable_strictMode === !0 && (o = !0),
      n.identifierPrefix !== void 0 && (i = n.identifierPrefix),
      n.onRecoverableError !== void 0 && (s = n.onRecoverableError)),
    (t = hp(t, null, e, 1, n ?? null, o, !1, i, s)),
    (e[Mt] = t.current),
    no(e),
    r)
  )
    for (e = 0; e < r.length; e++)
      ((n = r[e]),
        (o = n._getVersion),
        (o = o(n._source)),
        t.mutableSourceEagerHydrationData == null
          ? (t.mutableSourceEagerHydrationData = [n, o])
          : t.mutableSourceEagerHydrationData.push(n, o));
  return new Yi(t);
};
Ve.render = function (e, t, n) {
  if (!qi(t)) throw Error(R(200));
  return Zi(null, e, t, !1, n);
};
Ve.unmountComponentAtNode = function (e) {
  if (!qi(e)) throw Error(R(40));
  return e._reactRootContainer
    ? (Tn(function () {
        Zi(null, null, e, !1, function () {
          ((e._reactRootContainer = null), (e[Mt] = null));
        });
      }),
      !0)
    : !1;
};
Ve.unstable_batchedUpdates = Da;
Ve.unstable_renderSubtreeIntoContainer = function (e, t, n, r) {
  if (!qi(n)) throw Error(R(200));
  if (e == null || e._reactInternals === void 0) throw Error(R(38));
  return Zi(e, t, n, !1, r);
};
Ve.version = "18.3.1-next-f1338f8080-20240426";
function vp() {
  if (
    !(
      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ > "u" ||
      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE != "function"
    )
  )
    try {
      __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vp);
    } catch (e) {
      console.error(e);
    }
}
(vp(), (md.exports = Ve));
var Ji = md.exports;
const Og = nd(Ji);
var gp,
  gc = Ji;
((gp = gc.createRoot), gc.hydrateRoot);
var es = class {
    constructor() {
      ((this.listeners = new Set()),
        (this.subscribe = this.subscribe.bind(this)));
    }
    subscribe(e) {
      return (
        this.listeners.add(e),
        this.onSubscribe(),
        () => {
          (this.listeners.delete(e), this.onUnsubscribe());
        }
      );
    }
    hasListeners() {
      return this.listeners.size > 0;
    }
    onSubscribe() {}
    onUnsubscribe() {}
  },
  ts = typeof window > "u" || "Deno" in globalThis;
function nt() {}
function jg(e, t) {
  return typeof e == "function" ? e(t) : e;
}
function Dg(e) {
  return typeof e == "number" && e >= 0 && e !== 1 / 0;
}
function bg(e, t) {
  return Math.max(e + (t || 0) - Date.now(), 0);
}
function yc(e, t) {
  return typeof e == "function" ? e(t) : e;
}
function Ig(e, t) {
  return typeof e == "function" ? e(t) : e;
}
function wc(e, t) {
  const {
    type: n = "all",
    exact: r,
    fetchStatus: o,
    predicate: i,
    queryKey: s,
    stale: l,
  } = e;
  if (s) {
    if (r) {
      if (t.queryHash !== Ua(s, t.options)) return !1;
    } else if (!po(t.queryKey, s)) return !1;
  }
  if (n !== "all") {
    const a = t.isActive();
    if ((n === "active" && !a) || (n === "inactive" && a)) return !1;
  }
  return !(
    (typeof l == "boolean" && t.isStale() !== l) ||
    (o && o !== t.state.fetchStatus) ||
    (i && !i(t))
  );
}
function xc(e, t) {
  const { exact: n, status: r, predicate: o, mutationKey: i } = e;
  if (i) {
    if (!t.options.mutationKey) return !1;
    if (n) {
      if (fo(t.options.mutationKey) !== fo(i)) return !1;
    } else if (!po(t.options.mutationKey, i)) return !1;
  }
  return !((r && t.state.status !== r) || (o && !o(t)));
}
function Ua(e, t) {
  return ((t == null ? void 0 : t.queryKeyHashFn) || fo)(e);
}
function fo(e) {
  return JSON.stringify(e, (t, n) =>
    zl(n)
      ? Object.keys(n)
          .sort()
          .reduce((r, o) => ((r[o] = n[o]), r), {})
      : n,
  );
}
function po(e, t) {
  return e === t
    ? !0
    : typeof e != typeof t
      ? !1
      : e && t && typeof e == "object" && typeof t == "object"
        ? !Object.keys(t).some((n) => !po(e[n], t[n]))
        : !1;
}
function yp(e, t) {
  if (e === t) return e;
  const n = Sc(e) && Sc(t);
  if (n || (zl(e) && zl(t))) {
    const r = n ? e : Object.keys(e),
      o = r.length,
      i = n ? t : Object.keys(t),
      s = i.length,
      l = n ? [] : {};
    let a = 0;
    for (let u = 0; u < s; u++) {
      const v = n ? u : i[u];
      ((!n && r.includes(v)) || n) && e[v] === void 0 && t[v] === void 0
        ? ((l[v] = void 0), a++)
        : ((l[v] = yp(e[v], t[v])), l[v] === e[v] && e[v] !== void 0 && a++);
    }
    return o === s && a === o ? e : l;
  }
  return t;
}
function Sc(e) {
  return Array.isArray(e) && e.length === Object.keys(e).length;
}
function zl(e) {
  if (!Ec(e)) return !1;
  const t = e.constructor;
  if (t === void 0) return !0;
  const n = t.prototype;
  return !(
    !Ec(n) ||
    !n.hasOwnProperty("isPrototypeOf") ||
    Object.getPrototypeOf(e) !== Object.prototype
  );
}
function Ec(e) {
  return Object.prototype.toString.call(e) === "[object Object]";
}
function Lg(e) {
  return new Promise((t) => {
    setTimeout(t, e);
  });
}
function Ag(e, t, n) {
  return typeof n.structuralSharing == "function"
    ? n.structuralSharing(e, t)
    : n.structuralSharing !== !1
      ? yp(e, t)
      : t;
}
function Fg(e, t, n = 0) {
  const r = [...e, t];
  return n && r.length > n ? r.slice(1) : r;
}
function zg(e, t, n = 0) {
  const r = [t, ...e];
  return n && r.length > n ? r.slice(0, -1) : r;
}
var Wa = Symbol();
function wp(e, t) {
  return !e.queryFn && t != null && t.initialPromise
    ? () => t.initialPromise
    : !e.queryFn || e.queryFn === Wa
      ? () => Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))
      : e.queryFn;
}
var gn,
  Bt,
  nr,
  Gc,
  $g =
    ((Gc = class extends es {
      constructor() {
        super();
        V(this, gn);
        V(this, Bt);
        V(this, nr);
        $(this, nr, (t) => {
          if (!ts && window.addEventListener) {
            const n = () => t();
            return (
              window.addEventListener("visibilitychange", n, !1),
              () => {
                window.removeEventListener("visibilitychange", n);
              }
            );
          }
        });
      }
      onSubscribe() {
        P(this, Bt) || this.setEventListener(P(this, nr));
      }
      onUnsubscribe() {
        var t;
        this.hasListeners() ||
          ((t = P(this, Bt)) == null || t.call(this), $(this, Bt, void 0));
      }
      setEventListener(t) {
        var n;
        ($(this, nr, t),
          (n = P(this, Bt)) == null || n.call(this),
          $(
            this,
            Bt,
            t((r) => {
              typeof r == "boolean" ? this.setFocused(r) : this.onFocus();
            }),
          ));
      }
      setFocused(t) {
        P(this, gn) !== t && ($(this, gn, t), this.onFocus());
      }
      onFocus() {
        const t = this.isFocused();
        this.listeners.forEach((n) => {
          n(t);
        });
      }
      isFocused() {
        var t;
        return typeof P(this, gn) == "boolean"
          ? P(this, gn)
          : ((t = globalThis.document) == null ? void 0 : t.visibilityState) !==
              "hidden";
      }
    }),
    (gn = new WeakMap()),
    (Bt = new WeakMap()),
    (nr = new WeakMap()),
    Gc),
  xp = new $g(),
  rr,
  Ht,
  or,
  Xc,
  Ug =
    ((Xc = class extends es {
      constructor() {
        super();
        V(this, rr, !0);
        V(this, Ht);
        V(this, or);
        $(this, or, (t) => {
          if (!ts && window.addEventListener) {
            const n = () => t(!0),
              r = () => t(!1);
            return (
              window.addEventListener("online", n, !1),
              window.addEventListener("offline", r, !1),
              () => {
                (window.removeEventListener("online", n),
                  window.removeEventListener("offline", r));
              }
            );
          }
        });
      }
      onSubscribe() {
        P(this, Ht) || this.setEventListener(P(this, or));
      }
      onUnsubscribe() {
        var t;
        this.hasListeners() ||
          ((t = P(this, Ht)) == null || t.call(this), $(this, Ht, void 0));
      }
      setEventListener(t) {
        var n;
        ($(this, or, t),
          (n = P(this, Ht)) == null || n.call(this),
          $(this, Ht, t(this.setOnline.bind(this))));
      }
      setOnline(t) {
        P(this, rr) !== t &&
          ($(this, rr, t),
          this.listeners.forEach((r) => {
            r(t);
          }));
      }
      isOnline() {
        return P(this, rr);
      }
    }),
    (rr = new WeakMap()),
    (Ht = new WeakMap()),
    (or = new WeakMap()),
    Xc),
  bi = new Ug();
function Wg() {
  let e, t;
  const n = new Promise((o, i) => {
    ((e = o), (t = i));
  });
  ((n.status = "pending"), n.catch(() => {}));
  function r(o) {
    (Object.assign(n, o), delete n.resolve, delete n.reject);
  }
  return (
    (n.resolve = (o) => {
      (r({ status: "fulfilled", value: o }), e(o));
    }),
    (n.reject = (o) => {
      (r({ status: "rejected", reason: o }), t(o));
    }),
    n
  );
}
function Vg(e) {
  return Math.min(1e3 * 2 ** e, 3e4);
}
function Sp(e) {
  return (e ?? "online") === "online" ? bi.isOnline() : !0;
}
var Ep = class extends Error {
  constructor(e) {
    (super("CancelledError"),
      (this.revert = e == null ? void 0 : e.revert),
      (this.silent = e == null ? void 0 : e.silent));
  }
};
function Ds(e) {
  return e instanceof Ep;
}
function Cp(e) {
  let t = !1,
    n = 0,
    r = !1,
    o;
  const i = Wg(),
    s = (g) => {
      var w;
      r || (m(new Ep(g)), (w = e.abort) == null || w.call(e));
    },
    l = () => {
      t = !0;
    },
    a = () => {
      t = !1;
    },
    u = () =>
      xp.isFocused() &&
      (e.networkMode === "always" || bi.isOnline()) &&
      e.canRun(),
    v = () => Sp(e.networkMode) && e.canRun(),
    f = (g) => {
      var w;
      r ||
        ((r = !0),
        (w = e.onSuccess) == null || w.call(e, g),
        o == null || o(),
        i.resolve(g));
    },
    m = (g) => {
      var w;
      r ||
        ((r = !0),
        (w = e.onError) == null || w.call(e, g),
        o == null || o(),
        i.reject(g));
    },
    x = () =>
      new Promise((g) => {
        var w;
        ((o = (d) => {
          (r || u()) && g(d);
        }),
          (w = e.onPause) == null || w.call(e));
      }).then(() => {
        var g;
        ((o = void 0), r || (g = e.onContinue) == null || g.call(e));
      }),
    E = () => {
      if (r) return;
      let g;
      const w = n === 0 ? e.initialPromise : void 0;
      try {
        g = w ?? e.fn();
      } catch (d) {
        g = Promise.reject(d);
      }
      Promise.resolve(g)
        .then(f)
        .catch((d) => {
          var N;
          if (r) return;
          const c = e.retry ?? (ts ? 0 : 3),
            p = e.retryDelay ?? Vg,
            S = typeof p == "function" ? p(n, d) : p,
            C =
              c === !0 ||
              (typeof c == "number" && n < c) ||
              (typeof c == "function" && c(n, d));
          if (t || !C) {
            m(d);
            return;
          }
          (n++,
            (N = e.onFail) == null || N.call(e, n, d),
            Lg(S)
              .then(() => (u() ? void 0 : x()))
              .then(() => {
                t ? m(d) : E();
              }));
        });
    };
  return {
    promise: i,
    cancel: s,
    continue: () => (o == null || o(), i),
    cancelRetry: l,
    continueRetry: a,
    canStart: v,
    start: () => (v() ? E() : x().then(E), i),
  };
}
function Bg() {
  let e = [],
    t = 0,
    n = (l) => {
      l();
    },
    r = (l) => {
      l();
    },
    o = (l) => setTimeout(l, 0);
  const i = (l) => {
      t
        ? e.push(l)
        : o(() => {
            n(l);
          });
    },
    s = () => {
      const l = e;
      ((e = []),
        l.length &&
          o(() => {
            r(() => {
              l.forEach((a) => {
                n(a);
              });
            });
          }));
    };
  return {
    batch: (l) => {
      let a;
      t++;
      try {
        a = l();
      } finally {
        (t--, t || s());
      }
      return a;
    },
    batchCalls:
      (l) =>
      (...a) => {
        i(() => {
          l(...a);
        });
      },
    schedule: i,
    setNotifyFunction: (l) => {
      n = l;
    },
    setBatchNotifyFunction: (l) => {
      r = l;
    },
    setScheduler: (l) => {
      o = l;
    },
  };
}
var Re = Bg(),
  yn,
  Yc,
  kp =
    ((Yc = class {
      constructor() {
        V(this, yn);
      }
      destroy() {
        this.clearGcTimeout();
      }
      scheduleGc() {
        (this.clearGcTimeout(),
          Dg(this.gcTime) &&
            $(
              this,
              yn,
              setTimeout(() => {
                this.optionalRemove();
              }, this.gcTime),
            ));
      }
      updateGcTime(e) {
        this.gcTime = Math.max(
          this.gcTime || 0,
          e ?? (ts ? 1 / 0 : 5 * 60 * 1e3),
        );
      }
      clearGcTimeout() {
        P(this, yn) && (clearTimeout(P(this, yn)), $(this, yn, void 0));
      }
    }),
    (yn = new WeakMap()),
    Yc),
  ir,
  sr,
  Qe,
  Se,
  mo,
  wn,
  rt,
  Ct,
  qc,
  Hg =
    ((qc = class extends kp {
      constructor(t) {
        super();
        V(this, rt);
        V(this, ir);
        V(this, sr);
        V(this, Qe);
        V(this, Se);
        V(this, mo);
        V(this, wn);
        ($(this, wn, !1),
          $(this, mo, t.defaultOptions),
          this.setOptions(t.options),
          (this.observers = []),
          $(this, Qe, t.cache),
          (this.queryKey = t.queryKey),
          (this.queryHash = t.queryHash),
          $(this, ir, Kg(this.options)),
          (this.state = t.state ?? P(this, ir)),
          this.scheduleGc());
      }
      get meta() {
        return this.options.meta;
      }
      get promise() {
        var t;
        return (t = P(this, Se)) == null ? void 0 : t.promise;
      }
      setOptions(t) {
        ((this.options = { ...P(this, mo), ...t }),
          this.updateGcTime(this.options.gcTime));
      }
      optionalRemove() {
        !this.observers.length &&
          this.state.fetchStatus === "idle" &&
          P(this, Qe).remove(this);
      }
      setData(t, n) {
        const r = Ag(this.state.data, t, this.options);
        return (
          ye(this, rt, Ct).call(this, {
            data: r,
            type: "success",
            dataUpdatedAt: n == null ? void 0 : n.updatedAt,
            manual: n == null ? void 0 : n.manual,
          }),
          r
        );
      }
      setState(t, n) {
        ye(this, rt, Ct).call(this, {
          type: "setState",
          state: t,
          setStateOptions: n,
        });
      }
      cancel(t) {
        var r, o;
        const n = (r = P(this, Se)) == null ? void 0 : r.promise;
        return (
          (o = P(this, Se)) == null || o.cancel(t),
          n ? n.then(nt).catch(nt) : Promise.resolve()
        );
      }
      destroy() {
        (super.destroy(), this.cancel({ silent: !0 }));
      }
      reset() {
        (this.destroy(), this.setState(P(this, ir)));
      }
      isActive() {
        return this.observers.some((t) => Ig(t.options.enabled, this) !== !1);
      }
      isDisabled() {
        return this.getObserversCount() > 0
          ? !this.isActive()
          : this.options.queryFn === Wa ||
              this.state.dataUpdateCount + this.state.errorUpdateCount === 0;
      }
      isStale() {
        return this.state.isInvalidated
          ? !0
          : this.getObserversCount() > 0
            ? this.observers.some((t) => t.getCurrentResult().isStale)
            : this.state.data === void 0;
      }
      isStaleByTime(t = 0) {
        return (
          this.state.isInvalidated ||
          this.state.data === void 0 ||
          !bg(this.state.dataUpdatedAt, t)
        );
      }
      onFocus() {
        var n;
        const t = this.observers.find((r) => r.shouldFetchOnWindowFocus());
        (t == null || t.refetch({ cancelRefetch: !1 }),
          (n = P(this, Se)) == null || n.continue());
      }
      onOnline() {
        var n;
        const t = this.observers.find((r) => r.shouldFetchOnReconnect());
        (t == null || t.refetch({ cancelRefetch: !1 }),
          (n = P(this, Se)) == null || n.continue());
      }
      addObserver(t) {
        this.observers.includes(t) ||
          (this.observers.push(t),
          this.clearGcTimeout(),
          P(this, Qe).notify({
            type: "observerAdded",
            query: this,
            observer: t,
          }));
      }
      removeObserver(t) {
        this.observers.includes(t) &&
          ((this.observers = this.observers.filter((n) => n !== t)),
          this.observers.length ||
            (P(this, Se) &&
              (P(this, wn)
                ? P(this, Se).cancel({ revert: !0 })
                : P(this, Se).cancelRetry()),
            this.scheduleGc()),
          P(this, Qe).notify({
            type: "observerRemoved",
            query: this,
            observer: t,
          }));
      }
      getObserversCount() {
        return this.observers.length;
      }
      invalidate() {
        this.state.isInvalidated ||
          ye(this, rt, Ct).call(this, { type: "invalidate" });
      }
      fetch(t, n) {
        var a, u, v;
        if (this.state.fetchStatus !== "idle") {
          if (this.state.data !== void 0 && n != null && n.cancelRefetch)
            this.cancel({ silent: !0 });
          else if (P(this, Se))
            return (P(this, Se).continueRetry(), P(this, Se).promise);
        }
        if ((t && this.setOptions(t), !this.options.queryFn)) {
          const f = this.observers.find((m) => m.options.queryFn);
          f && this.setOptions(f.options);
        }
        const r = new AbortController(),
          o = (f) => {
            Object.defineProperty(f, "signal", {
              enumerable: !0,
              get: () => ($(this, wn, !0), r.signal),
            });
          },
          i = () => {
            const f = wp(this.options, n),
              m = { queryKey: this.queryKey, meta: this.meta };
            return (
              o(m),
              $(this, wn, !1),
              this.options.persister ? this.options.persister(f, m, this) : f(m)
            );
          },
          s = {
            fetchOptions: n,
            options: this.options,
            queryKey: this.queryKey,
            state: this.state,
            fetchFn: i,
          };
        (o(s),
          (a = this.options.behavior) == null || a.onFetch(s, this),
          $(this, sr, this.state),
          (this.state.fetchStatus === "idle" ||
            this.state.fetchMeta !==
              ((u = s.fetchOptions) == null ? void 0 : u.meta)) &&
            ye(this, rt, Ct).call(this, {
              type: "fetch",
              meta: (v = s.fetchOptions) == null ? void 0 : v.meta,
            }));
        const l = (f) => {
          var m, x, E, g;
          ((Ds(f) && f.silent) ||
            ye(this, rt, Ct).call(this, { type: "error", error: f }),
            Ds(f) ||
              ((x = (m = P(this, Qe).config).onError) == null ||
                x.call(m, f, this),
              (g = (E = P(this, Qe).config).onSettled) == null ||
                g.call(E, this.state.data, f, this)),
            this.scheduleGc());
        };
        return (
          $(
            this,
            Se,
            Cp({
              initialPromise: n == null ? void 0 : n.initialPromise,
              fn: s.fetchFn,
              abort: r.abort.bind(r),
              onSuccess: (f) => {
                var m, x, E, g;
                if (f === void 0) {
                  l(new Error(`${this.queryHash} data is undefined`));
                  return;
                }
                try {
                  this.setData(f);
                } catch (w) {
                  l(w);
                  return;
                }
                ((x = (m = P(this, Qe).config).onSuccess) == null ||
                  x.call(m, f, this),
                  (g = (E = P(this, Qe).config).onSettled) == null ||
                    g.call(E, f, this.state.error, this),
                  this.scheduleGc());
              },
              onError: l,
              onFail: (f, m) => {
                ye(this, rt, Ct).call(this, {
                  type: "failed",
                  failureCount: f,
                  error: m,
                });
              },
              onPause: () => {
                ye(this, rt, Ct).call(this, { type: "pause" });
              },
              onContinue: () => {
                ye(this, rt, Ct).call(this, { type: "continue" });
              },
              retry: s.options.retry,
              retryDelay: s.options.retryDelay,
              networkMode: s.options.networkMode,
              canRun: () => !0,
            }),
          ),
          P(this, Se).start()
        );
      }
    }),
    (ir = new WeakMap()),
    (sr = new WeakMap()),
    (Qe = new WeakMap()),
    (Se = new WeakMap()),
    (mo = new WeakMap()),
    (wn = new WeakMap()),
    (rt = new WeakSet()),
    (Ct = function (t) {
      const n = (r) => {
        switch (t.type) {
          case "failed":
            return {
              ...r,
              fetchFailureCount: t.failureCount,
              fetchFailureReason: t.error,
            };
          case "pause":
            return { ...r, fetchStatus: "paused" };
          case "continue":
            return { ...r, fetchStatus: "fetching" };
          case "fetch":
            return {
              ...r,
              ...Qg(r.data, this.options),
              fetchMeta: t.meta ?? null,
            };
          case "success":
            return {
              ...r,
              data: t.data,
              dataUpdateCount: r.dataUpdateCount + 1,
              dataUpdatedAt: t.dataUpdatedAt ?? Date.now(),
              error: null,
              isInvalidated: !1,
              status: "success",
              ...(!t.manual && {
                fetchStatus: "idle",
                fetchFailureCount: 0,
                fetchFailureReason: null,
              }),
            };
          case "error":
            const o = t.error;
            return Ds(o) && o.revert && P(this, sr)
              ? { ...P(this, sr), fetchStatus: "idle" }
              : {
                  ...r,
                  error: o,
                  errorUpdateCount: r.errorUpdateCount + 1,
                  errorUpdatedAt: Date.now(),
                  fetchFailureCount: r.fetchFailureCount + 1,
                  fetchFailureReason: o,
                  fetchStatus: "idle",
                  status: "error",
                };
          case "invalidate":
            return { ...r, isInvalidated: !0 };
          case "setState":
            return { ...r, ...t.state };
        }
      };
      ((this.state = n(this.state)),
        Re.batch(() => {
          (this.observers.forEach((r) => {
            r.onQueryUpdate();
          }),
            P(this, Qe).notify({ query: this, type: "updated", action: t }));
        }));
    }),
    qc);
function Qg(e, t) {
  return {
    fetchFailureCount: 0,
    fetchFailureReason: null,
    fetchStatus: Sp(t.networkMode) ? "fetching" : "paused",
    ...(e === void 0 && { error: null, status: "pending" }),
  };
}
function Kg(e) {
  const t =
      typeof e.initialData == "function" ? e.initialData() : e.initialData,
    n = t !== void 0,
    r = n
      ? typeof e.initialDataUpdatedAt == "function"
        ? e.initialDataUpdatedAt()
        : e.initialDataUpdatedAt
      : 0;
  return {
    data: t,
    dataUpdateCount: 0,
    dataUpdatedAt: n ? (r ?? Date.now()) : 0,
    error: null,
    errorUpdateCount: 0,
    errorUpdatedAt: 0,
    fetchFailureCount: 0,
    fetchFailureReason: null,
    fetchMeta: null,
    isInvalidated: !1,
    status: n ? "success" : "pending",
    fetchStatus: "idle",
  };
}
var ht,
  Zc,
  Gg =
    ((Zc = class extends es {
      constructor(t = {}) {
        super();
        V(this, ht);
        ((this.config = t), $(this, ht, new Map()));
      }
      build(t, n, r) {
        const o = n.queryKey,
          i = n.queryHash ?? Ua(o, n);
        let s = this.get(i);
        return (
          s ||
            ((s = new Hg({
              cache: this,
              queryKey: o,
              queryHash: i,
              options: t.defaultQueryOptions(n),
              state: r,
              defaultOptions: t.getQueryDefaults(o),
            })),
            this.add(s)),
          s
        );
      }
      add(t) {
        P(this, ht).has(t.queryHash) ||
          (P(this, ht).set(t.queryHash, t),
          this.notify({ type: "added", query: t }));
      }
      remove(t) {
        const n = P(this, ht).get(t.queryHash);
        n &&
          (t.destroy(),
          n === t && P(this, ht).delete(t.queryHash),
          this.notify({ type: "removed", query: t }));
      }
      clear() {
        Re.batch(() => {
          this.getAll().forEach((t) => {
            this.remove(t);
          });
        });
      }
      get(t) {
        return P(this, ht).get(t);
      }
      getAll() {
        return [...P(this, ht).values()];
      }
      find(t) {
        const n = { exact: !0, ...t };
        return this.getAll().find((r) => wc(n, r));
      }
      findAll(t = {}) {
        const n = this.getAll();
        return Object.keys(t).length > 0 ? n.filter((r) => wc(t, r)) : n;
      }
      notify(t) {
        Re.batch(() => {
          this.listeners.forEach((n) => {
            n(t);
          });
        });
      }
      onFocus() {
        Re.batch(() => {
          this.getAll().forEach((t) => {
            t.onFocus();
          });
        });
      }
      onOnline() {
        Re.batch(() => {
          this.getAll().forEach((t) => {
            t.onOnline();
          });
        });
      }
    }),
    (ht = new WeakMap()),
    Zc),
  mt,
  Ne,
  xn,
  vt,
  Ft,
  Jc,
  Xg =
    ((Jc = class extends kp {
      constructor(t) {
        super();
        V(this, vt);
        V(this, mt);
        V(this, Ne);
        V(this, xn);
        ((this.mutationId = t.mutationId),
          $(this, Ne, t.mutationCache),
          $(this, mt, []),
          (this.state = t.state || Yg()),
          this.setOptions(t.options),
          this.scheduleGc());
      }
      setOptions(t) {
        ((this.options = t), this.updateGcTime(this.options.gcTime));
      }
      get meta() {
        return this.options.meta;
      }
      addObserver(t) {
        P(this, mt).includes(t) ||
          (P(this, mt).push(t),
          this.clearGcTimeout(),
          P(this, Ne).notify({
            type: "observerAdded",
            mutation: this,
            observer: t,
          }));
      }
      removeObserver(t) {
        ($(
          this,
          mt,
          P(this, mt).filter((n) => n !== t),
        ),
          this.scheduleGc(),
          P(this, Ne).notify({
            type: "observerRemoved",
            mutation: this,
            observer: t,
          }));
      }
      optionalRemove() {
        P(this, mt).length ||
          (this.state.status === "pending"
            ? this.scheduleGc()
            : P(this, Ne).remove(this));
      }
      continue() {
        var t;
        return (
          ((t = P(this, xn)) == null ? void 0 : t.continue()) ??
          this.execute(this.state.variables)
        );
      }
      async execute(t) {
        var o, i, s, l, a, u, v, f, m, x, E, g, w, d, c, p, S, C, N, k;
        $(
          this,
          xn,
          Cp({
            fn: () =>
              this.options.mutationFn
                ? this.options.mutationFn(t)
                : Promise.reject(new Error("No mutationFn found")),
            onFail: (T, b) => {
              ye(this, vt, Ft).call(this, {
                type: "failed",
                failureCount: T,
                error: b,
              });
            },
            onPause: () => {
              ye(this, vt, Ft).call(this, { type: "pause" });
            },
            onContinue: () => {
              ye(this, vt, Ft).call(this, { type: "continue" });
            },
            retry: this.options.retry ?? 0,
            retryDelay: this.options.retryDelay,
            networkMode: this.options.networkMode,
            canRun: () => P(this, Ne).canRun(this),
          }),
        );
        const n = this.state.status === "pending",
          r = !P(this, xn).canStart();
        try {
          if (!n) {
            (ye(this, vt, Ft).call(this, {
              type: "pending",
              variables: t,
              isPaused: r,
            }),
              await ((i = (o = P(this, Ne).config).onMutate) == null
                ? void 0
                : i.call(o, t, this)));
            const b = await ((l = (s = this.options).onMutate) == null
              ? void 0
              : l.call(s, t));
            b !== this.state.context &&
              ye(this, vt, Ft).call(this, {
                type: "pending",
                context: b,
                variables: t,
                isPaused: r,
              });
          }
          const T = await P(this, xn).start();
          return (
            await ((u = (a = P(this, Ne).config).onSuccess) == null
              ? void 0
              : u.call(a, T, t, this.state.context, this)),
            await ((f = (v = this.options).onSuccess) == null
              ? void 0
              : f.call(v, T, t, this.state.context)),
            await ((x = (m = P(this, Ne).config).onSettled) == null
              ? void 0
              : x.call(
                  m,
                  T,
                  null,
                  this.state.variables,
                  this.state.context,
                  this,
                )),
            await ((g = (E = this.options).onSettled) == null
              ? void 0
              : g.call(E, T, null, t, this.state.context)),
            ye(this, vt, Ft).call(this, { type: "success", data: T }),
            T
          );
        } catch (T) {
          try {
            throw (
              await ((d = (w = P(this, Ne).config).onError) == null
                ? void 0
                : d.call(w, T, t, this.state.context, this)),
              await ((p = (c = this.options).onError) == null
                ? void 0
                : p.call(c, T, t, this.state.context)),
              await ((C = (S = P(this, Ne).config).onSettled) == null
                ? void 0
                : C.call(
                    S,
                    void 0,
                    T,
                    this.state.variables,
                    this.state.context,
                    this,
                  )),
              await ((k = (N = this.options).onSettled) == null
                ? void 0
                : k.call(N, void 0, T, t, this.state.context)),
              T
            );
          } finally {
            ye(this, vt, Ft).call(this, { type: "error", error: T });
          }
        } finally {
          P(this, Ne).runNext(this);
        }
      }
    }),
    (mt = new WeakMap()),
    (Ne = new WeakMap()),
    (xn = new WeakMap()),
    (vt = new WeakSet()),
    (Ft = function (t) {
      const n = (r) => {
        switch (t.type) {
          case "failed":
            return {
              ...r,
              failureCount: t.failureCount,
              failureReason: t.error,
            };
          case "pause":
            return { ...r, isPaused: !0 };
          case "continue":
            return { ...r, isPaused: !1 };
          case "pending":
            return {
              ...r,
              context: t.context,
              data: void 0,
              failureCount: 0,
              failureReason: null,
              error: null,
              isPaused: t.isPaused,
              status: "pending",
              variables: t.variables,
              submittedAt: Date.now(),
            };
          case "success":
            return {
              ...r,
              data: t.data,
              failureCount: 0,
              failureReason: null,
              error: null,
              status: "success",
              isPaused: !1,
            };
          case "error":
            return {
              ...r,
              data: void 0,
              error: t.error,
              failureCount: r.failureCount + 1,
              failureReason: t.error,
              isPaused: !1,
              status: "error",
            };
        }
      };
      ((this.state = n(this.state)),
        Re.batch(() => {
          (P(this, mt).forEach((r) => {
            r.onMutationUpdate(t);
          }),
            P(this, Ne).notify({ mutation: this, type: "updated", action: t }));
        }));
    }),
    Jc);
function Yg() {
  return {
    context: void 0,
    data: void 0,
    error: null,
    failureCount: 0,
    failureReason: null,
    isPaused: !1,
    status: "idle",
    variables: void 0,
    submittedAt: 0,
  };
}
var Fe,
  vo,
  ed,
  qg =
    ((ed = class extends es {
      constructor(t = {}) {
        super();
        V(this, Fe);
        V(this, vo);
        ((this.config = t), $(this, Fe, new Map()), $(this, vo, Date.now()));
      }
      build(t, n, r) {
        const o = new Xg({
          mutationCache: this,
          mutationId: ++Co(this, vo)._,
          options: t.defaultMutationOptions(n),
          state: r,
        });
        return (this.add(o), o);
      }
      add(t) {
        const n = Uo(t),
          r = P(this, Fe).get(n) ?? [];
        (r.push(t),
          P(this, Fe).set(n, r),
          this.notify({ type: "added", mutation: t }));
      }
      remove(t) {
        var r;
        const n = Uo(t);
        if (P(this, Fe).has(n)) {
          const o =
            (r = P(this, Fe).get(n)) == null
              ? void 0
              : r.filter((i) => i !== t);
          o && (o.length === 0 ? P(this, Fe).delete(n) : P(this, Fe).set(n, o));
        }
        this.notify({ type: "removed", mutation: t });
      }
      canRun(t) {
        var r;
        const n =
          (r = P(this, Fe).get(Uo(t))) == null
            ? void 0
            : r.find((o) => o.state.status === "pending");
        return !n || n === t;
      }
      runNext(t) {
        var r;
        const n =
          (r = P(this, Fe).get(Uo(t))) == null
            ? void 0
            : r.find((o) => o !== t && o.state.isPaused);
        return (n == null ? void 0 : n.continue()) ?? Promise.resolve();
      }
      clear() {
        Re.batch(() => {
          this.getAll().forEach((t) => {
            this.remove(t);
          });
        });
      }
      getAll() {
        return [...P(this, Fe).values()].flat();
      }
      find(t) {
        const n = { exact: !0, ...t };
        return this.getAll().find((r) => xc(n, r));
      }
      findAll(t = {}) {
        return this.getAll().filter((n) => xc(t, n));
      }
      notify(t) {
        Re.batch(() => {
          this.listeners.forEach((n) => {
            n(t);
          });
        });
      }
      resumePausedMutations() {
        const t = this.getAll().filter((n) => n.state.isPaused);
        return Re.batch(() =>
          Promise.all(t.map((n) => n.continue().catch(nt))),
        );
      }
    }),
    (Fe = new WeakMap()),
    (vo = new WeakMap()),
    ed);
function Uo(e) {
  var t;
  return (
    ((t = e.options.scope) == null ? void 0 : t.id) ?? String(e.mutationId)
  );
}
function Cc(e) {
  return {
    onFetch: (t, n) => {
      var v, f, m, x, E;
      const r = t.options,
        o =
          (m =
            (f = (v = t.fetchOptions) == null ? void 0 : v.meta) == null
              ? void 0
              : f.fetchMore) == null
            ? void 0
            : m.direction,
        i = ((x = t.state.data) == null ? void 0 : x.pages) || [],
        s = ((E = t.state.data) == null ? void 0 : E.pageParams) || [];
      let l = { pages: [], pageParams: [] },
        a = 0;
      const u = async () => {
        let g = !1;
        const w = (p) => {
            Object.defineProperty(p, "signal", {
              enumerable: !0,
              get: () => (
                t.signal.aborted
                  ? (g = !0)
                  : t.signal.addEventListener("abort", () => {
                      g = !0;
                    }),
                t.signal
              ),
            });
          },
          d = wp(t.options, t.fetchOptions),
          c = async (p, S, C) => {
            if (g) return Promise.reject();
            if (S == null && p.pages.length) return Promise.resolve(p);
            const N = {
              queryKey: t.queryKey,
              pageParam: S,
              direction: C ? "backward" : "forward",
              meta: t.options.meta,
            };
            w(N);
            const k = await d(N),
              { maxPages: T } = t.options,
              b = C ? zg : Fg;
            return {
              pages: b(p.pages, k, T),
              pageParams: b(p.pageParams, S, T),
            };
          };
        if (o && i.length) {
          const p = o === "backward",
            S = p ? Zg : kc,
            C = { pages: i, pageParams: s },
            N = S(r, C);
          l = await c(C, N, p);
        } else {
          const p = e ?? i.length;
          do {
            const S = a === 0 ? (s[0] ?? r.initialPageParam) : kc(r, l);
            if (a > 0 && S == null) break;
            ((l = await c(l, S)), a++);
          } while (a < p);
        }
        return l;
      };
      t.options.persister
        ? (t.fetchFn = () => {
            var g, w;
            return (w = (g = t.options).persister) == null
              ? void 0
              : w.call(
                  g,
                  u,
                  {
                    queryKey: t.queryKey,
                    meta: t.options.meta,
                    signal: t.signal,
                  },
                  n,
                );
          })
        : (t.fetchFn = u);
    },
  };
}
function kc(e, { pages: t, pageParams: n }) {
  const r = t.length - 1;
  return t.length > 0 ? e.getNextPageParam(t[r], t, n[r], n) : void 0;
}
function Zg(e, { pages: t, pageParams: n }) {
  var r;
  return t.length > 0
    ? (r = e.getPreviousPageParam) == null
      ? void 0
      : r.call(e, t[0], t, n[0], n)
    : void 0;
}
var te,
  Qt,
  Kt,
  lr,
  ar,
  Gt,
  ur,
  cr,
  td,
  Jg =
    ((td = class {
      constructor(e = {}) {
        V(this, te);
        V(this, Qt);
        V(this, Kt);
        V(this, lr);
        V(this, ar);
        V(this, Gt);
        V(this, ur);
        V(this, cr);
        ($(this, te, e.queryCache || new Gg()),
          $(this, Qt, e.mutationCache || new qg()),
          $(this, Kt, e.defaultOptions || {}),
          $(this, lr, new Map()),
          $(this, ar, new Map()),
          $(this, Gt, 0));
      }
      mount() {
        (Co(this, Gt)._++,
          P(this, Gt) === 1 &&
            ($(
              this,
              ur,
              xp.subscribe(async (e) => {
                e &&
                  (await this.resumePausedMutations(), P(this, te).onFocus());
              }),
            ),
            $(
              this,
              cr,
              bi.subscribe(async (e) => {
                e &&
                  (await this.resumePausedMutations(), P(this, te).onOnline());
              }),
            )));
      }
      unmount() {
        var e, t;
        (Co(this, Gt)._--,
          P(this, Gt) === 0 &&
            ((e = P(this, ur)) == null || e.call(this),
            $(this, ur, void 0),
            (t = P(this, cr)) == null || t.call(this),
            $(this, cr, void 0)));
      }
      isFetching(e) {
        return P(this, te).findAll({ ...e, fetchStatus: "fetching" }).length;
      }
      isMutating(e) {
        return P(this, Qt).findAll({ ...e, status: "pending" }).length;
      }
      getQueryData(e) {
        var n;
        const t = this.defaultQueryOptions({ queryKey: e });
        return (n = P(this, te).get(t.queryHash)) == null
          ? void 0
          : n.state.data;
      }
      ensureQueryData(e) {
        const t = this.getQueryData(e.queryKey);
        if (t === void 0) return this.fetchQuery(e);
        {
          const n = this.defaultQueryOptions(e),
            r = P(this, te).build(this, n);
          return (
            e.revalidateIfStale &&
              r.isStaleByTime(yc(n.staleTime, r)) &&
              this.prefetchQuery(n),
            Promise.resolve(t)
          );
        }
      }
      getQueriesData(e) {
        return P(this, te)
          .findAll(e)
          .map(({ queryKey: t, state: n }) => {
            const r = n.data;
            return [t, r];
          });
      }
      setQueryData(e, t, n) {
        const r = this.defaultQueryOptions({ queryKey: e }),
          o = P(this, te).get(r.queryHash),
          i = o == null ? void 0 : o.state.data,
          s = jg(t, i);
        if (s !== void 0)
          return P(this, te)
            .build(this, r)
            .setData(s, { ...n, manual: !0 });
      }
      setQueriesData(e, t, n) {
        return Re.batch(() =>
          P(this, te)
            .findAll(e)
            .map(({ queryKey: r }) => [r, this.setQueryData(r, t, n)]),
        );
      }
      getQueryState(e) {
        var n;
        const t = this.defaultQueryOptions({ queryKey: e });
        return (n = P(this, te).get(t.queryHash)) == null ? void 0 : n.state;
      }
      removeQueries(e) {
        const t = P(this, te);
        Re.batch(() => {
          t.findAll(e).forEach((n) => {
            t.remove(n);
          });
        });
      }
      resetQueries(e, t) {
        const n = P(this, te),
          r = { type: "active", ...e };
        return Re.batch(
          () => (
            n.findAll(e).forEach((o) => {
              o.reset();
            }),
            this.refetchQueries(r, t)
          ),
        );
      }
      cancelQueries(e = {}, t = {}) {
        const n = { revert: !0, ...t },
          r = Re.batch(() =>
            P(this, te)
              .findAll(e)
              .map((o) => o.cancel(n)),
          );
        return Promise.all(r).then(nt).catch(nt);
      }
      invalidateQueries(e = {}, t = {}) {
        return Re.batch(() => {
          if (
            (P(this, te)
              .findAll(e)
              .forEach((r) => {
                r.invalidate();
              }),
            e.refetchType === "none")
          )
            return Promise.resolve();
          const n = { ...e, type: e.refetchType ?? e.type ?? "active" };
          return this.refetchQueries(n, t);
        });
      }
      refetchQueries(e = {}, t) {
        const n = {
            ...t,
            cancelRefetch: (t == null ? void 0 : t.cancelRefetch) ?? !0,
          },
          r = Re.batch(() =>
            P(this, te)
              .findAll(e)
              .filter((o) => !o.isDisabled())
              .map((o) => {
                let i = o.fetch(void 0, n);
                return (
                  n.throwOnError || (i = i.catch(nt)),
                  o.state.fetchStatus === "paused" ? Promise.resolve() : i
                );
              }),
          );
        return Promise.all(r).then(nt);
      }
      fetchQuery(e) {
        const t = this.defaultQueryOptions(e);
        t.retry === void 0 && (t.retry = !1);
        const n = P(this, te).build(this, t);
        return n.isStaleByTime(yc(t.staleTime, n))
          ? n.fetch(t)
          : Promise.resolve(n.state.data);
      }
      prefetchQuery(e) {
        return this.fetchQuery(e).then(nt).catch(nt);
      }
      fetchInfiniteQuery(e) {
        return ((e.behavior = Cc(e.pages)), this.fetchQuery(e));
      }
      prefetchInfiniteQuery(e) {
        return this.fetchInfiniteQuery(e).then(nt).catch(nt);
      }
      ensureInfiniteQueryData(e) {
        return ((e.behavior = Cc(e.pages)), this.ensureQueryData(e));
      }
      resumePausedMutations() {
        return bi.isOnline()
          ? P(this, Qt).resumePausedMutations()
          : Promise.resolve();
      }
      getQueryCache() {
        return P(this, te);
      }
      getMutationCache() {
        return P(this, Qt);
      }
      getDefaultOptions() {
        return P(this, Kt);
      }
      setDefaultOptions(e) {
        $(this, Kt, e);
      }
      setQueryDefaults(e, t) {
        P(this, lr).set(fo(e), { queryKey: e, defaultOptions: t });
      }
      getQueryDefaults(e) {
        const t = [...P(this, lr).values()];
        let n = {};
        return (
          t.forEach((r) => {
            po(e, r.queryKey) && (n = { ...n, ...r.defaultOptions });
          }),
          n
        );
      }
      setMutationDefaults(e, t) {
        P(this, ar).set(fo(e), { mutationKey: e, defaultOptions: t });
      }
      getMutationDefaults(e) {
        const t = [...P(this, ar).values()];
        let n = {};
        return (
          t.forEach((r) => {
            po(e, r.mutationKey) && (n = { ...n, ...r.defaultOptions });
          }),
          n
        );
      }
      defaultQueryOptions(e) {
        if (e._defaulted) return e;
        const t = {
          ...P(this, Kt).queries,
          ...this.getQueryDefaults(e.queryKey),
          ...e,
          _defaulted: !0,
        };
        return (
          t.queryHash || (t.queryHash = Ua(t.queryKey, t)),
          t.refetchOnReconnect === void 0 &&
            (t.refetchOnReconnect = t.networkMode !== "always"),
          t.throwOnError === void 0 && (t.throwOnError = !!t.suspense),
          !t.networkMode && t.persister && (t.networkMode = "offlineFirst"),
          t.enabled !== !0 && t.queryFn === Wa && (t.enabled = !1),
          t
        );
      }
      defaultMutationOptions(e) {
        return e != null && e._defaulted
          ? e
          : {
              ...P(this, Kt).mutations,
              ...((e == null ? void 0 : e.mutationKey) &&
                this.getMutationDefaults(e.mutationKey)),
              ...e,
              _defaulted: !0,
            };
      }
      clear() {
        (P(this, te).clear(), P(this, Qt).clear());
      }
    }),
    (te = new WeakMap()),
    (Qt = new WeakMap()),
    (Kt = new WeakMap()),
    (lr = new WeakMap()),
    (ar = new WeakMap()),
    (Gt = new WeakMap()),
    (ur = new WeakMap()),
    (cr = new WeakMap()),
    td),
  ey = h.createContext(void 0),
  ty = ({ client: e, children: t }) => (
    h.useEffect(
      () => (
        e.mount(),
        () => {
          e.unmount();
        }
      ),
      [e],
    ),
    y.jsx(ey.Provider, { value: e, children: t })
  );
const ny = new Jg({
    defaultOptions: {
      queries: {
        queryFn: async ({ queryKey: e }) => {
          const t = await fetch(e[0], { credentials: "include" });
          if (!t.ok)
            throw t.status >= 500
              ? new Error(`${t.status}: ${t.statusText}`)
              : new Error(`${t.status}: ${await t.text()}`);
          return t.json();
        },
        refetchInterval: !1,
        refetchOnWindowFocus: !1,
        staleTime: 1 / 0,
        retry: !1,
      },
      mutations: { retry: !1 },
    },
  }),
  ry = 1,
  oy = 1e6;
let bs = 0;
function iy() {
  return ((bs = (bs + 1) % Number.MAX_SAFE_INTEGER), bs.toString());
}
const Is = new Map(),
  Pc = (e) => {
    if (Is.has(e)) return;
    const t = setTimeout(() => {
      (Is.delete(e), Kr({ type: "REMOVE_TOAST", toastId: e }));
    }, oy);
    Is.set(e, t);
  },
  sy = (e, t) => {
    switch (t.type) {
      case "ADD_TOAST":
        return { ...e, toasts: [t.toast, ...e.toasts].slice(0, ry) };
      case "UPDATE_TOAST":
        return {
          ...e,
          toasts: e.toasts.map((n) =>
            n.id === t.toast.id ? { ...n, ...t.toast } : n,
          ),
        };
      case "DISMISS_TOAST": {
        const { toastId: n } = t;
        return (
          n
            ? Pc(n)
            : e.toasts.forEach((r) => {
                Pc(r.id);
              }),
          {
            ...e,
            toasts: e.toasts.map((r) =>
              r.id === n || n === void 0 ? { ...r, open: !1 } : r,
            ),
          }
        );
      }
      case "REMOVE_TOAST":
        return t.toastId === void 0
          ? { ...e, toasts: [] }
          : { ...e, toasts: e.toasts.filter((n) => n.id !== t.toastId) };
    }
  },
  si = [];
let li = { toasts: [] };
function Kr(e) {
  ((li = sy(li, e)),
    si.forEach((t) => {
      t(li);
    }));
}
function ly({ ...e }) {
  const t = iy(),
    n = (o) => Kr({ type: "UPDATE_TOAST", toast: { ...o, id: t } }),
    r = () => Kr({ type: "DISMISS_TOAST", toastId: t });
  return (
    Kr({
      type: "ADD_TOAST",
      toast: {
        ...e,
        id: t,
        open: !0,
        onOpenChange: (o) => {
          o || r();
        },
      },
    }),
    { id: t, dismiss: r, update: n }
  );
}
function ay() {
  const [e, t] = h.useState(li);
  return (
    h.useEffect(
      () => (
        si.push(t),
        () => {
          const n = si.indexOf(t);
          n > -1 && si.splice(n, 1);
        }
      ),
      [e],
    ),
    {
      ...e,
      toast: ly,
      dismiss: (n) => Kr({ type: "DISMISS_TOAST", toastId: n }),
    }
  );
}
function pe(e, t, { checkForDefaultPrevented: n = !0 } = {}) {
  return function (o) {
    if ((e == null || e(o), n === !1 || !o.defaultPrevented))
      return t == null ? void 0 : t(o);
  };
}
function uy(e, t) {
  typeof e == "function" ? e(t) : e != null && (e.current = t);
}
function Pp(...e) {
  return (t) => e.forEach((n) => uy(n, t));
}
function ut(...e) {
  return h.useCallback(Pp(...e), e);
}
function cy(e, t = []) {
  let n = [];
  function r(i, s) {
    const l = h.createContext(s),
      a = n.length;
    n = [...n, s];
    function u(f) {
      const { scope: m, children: x, ...E } = f,
        g = (m == null ? void 0 : m[e][a]) || l,
        w = h.useMemo(() => E, Object.values(E));
      return y.jsx(g.Provider, { value: w, children: x });
    }
    function v(f, m) {
      const x = (m == null ? void 0 : m[e][a]) || l,
        E = h.useContext(x);
      if (E) return E;
      if (s !== void 0) return s;
      throw new Error(`\`${f}\` must be used within \`${i}\``);
    }
    return ((u.displayName = i + "Provider"), [u, v]);
  }
  const o = () => {
    const i = n.map((s) => h.createContext(s));
    return function (l) {
      const a = (l == null ? void 0 : l[e]) || i;
      return h.useMemo(() => ({ [`__scope${e}`]: { ...l, [e]: a } }), [l, a]);
    };
  };
  return ((o.scopeName = e), [r, dy(o, ...t)]);
}
function dy(...e) {
  const t = e[0];
  if (e.length === 1) return t;
  const n = () => {
    const r = e.map((o) => ({ useScope: o(), scopeName: o.scopeName }));
    return function (i) {
      const s = r.reduce((l, { useScope: a, scopeName: u }) => {
        const f = a(i)[`__scope${u}`];
        return { ...l, ...f };
      }, {});
      return h.useMemo(() => ({ [`__scope${t.scopeName}`]: s }), [s]);
    };
  };
  return ((n.scopeName = t.scopeName), n);
}
var yr = h.forwardRef((e, t) => {
  const { children: n, ...r } = e,
    o = h.Children.toArray(n),
    i = o.find(py);
  if (i) {
    const s = i.props.children,
      l = o.map((a) =>
        a === i
          ? h.Children.count(s) > 1
            ? h.Children.only(null)
            : h.isValidElement(s)
              ? s.props.children
              : null
          : a,
      );
    return y.jsx($l, {
      ...r,
      ref: t,
      children: h.isValidElement(s) ? h.cloneElement(s, void 0, l) : null,
    });
  }
  return y.jsx($l, { ...r, ref: t, children: n });
});
yr.displayName = "Slot";
var $l = h.forwardRef((e, t) => {
  const { children: n, ...r } = e;
  if (h.isValidElement(n)) {
    const o = my(n);
    return h.cloneElement(n, { ...hy(r, n.props), ref: t ? Pp(t, o) : o });
  }
  return h.Children.count(n) > 1 ? h.Children.only(null) : null;
});
$l.displayName = "SlotClone";
var fy = ({ children: e }) => y.jsx(y.Fragment, { children: e });
function py(e) {
  return h.isValidElement(e) && e.type === fy;
}
function hy(e, t) {
  const n = { ...t };
  for (const r in t) {
    const o = e[r],
      i = t[r];
    /^on[A-Z]/.test(r)
      ? o && i
        ? (n[r] = (...l) => {
            (i(...l), o(...l));
          })
        : o && (n[r] = o)
      : r === "style"
        ? (n[r] = { ...o, ...i })
        : r === "className" && (n[r] = [o, i].filter(Boolean).join(" "));
  }
  return { ...e, ...n };
}
function my(e) {
  var r, o;
  let t =
      (r = Object.getOwnPropertyDescriptor(e.props, "ref")) == null
        ? void 0
        : r.get,
    n = t && "isReactWarning" in t && t.isReactWarning;
  return n
    ? e.ref
    : ((t =
        (o = Object.getOwnPropertyDescriptor(e, "ref")) == null
          ? void 0
          : o.get),
      (n = t && "isReactWarning" in t && t.isReactWarning),
      n ? e.props.ref : e.props.ref || e.ref);
}
function vy(e) {
  const t = e + "CollectionProvider",
    [n, r] = cy(t),
    [o, i] = n(t, { collectionRef: { current: null }, itemMap: new Map() }),
    s = (x) => {
      const { scope: E, children: g } = x,
        w = At.useRef(null),
        d = At.useRef(new Map()).current;
      return y.jsx(o, { scope: E, itemMap: d, collectionRef: w, children: g });
    };
  s.displayName = t;
  const l = e + "CollectionSlot",
    a = At.forwardRef((x, E) => {
      const { scope: g, children: w } = x,
        d = i(l, g),
        c = ut(E, d.collectionRef);
      return y.jsx(yr, { ref: c, children: w });
    });
  a.displayName = l;
  const u = e + "CollectionItemSlot",
    v = "data-radix-collection-item",
    f = At.forwardRef((x, E) => {
      const { scope: g, children: w, ...d } = x,
        c = At.useRef(null),
        p = ut(E, c),
        S = i(u, g);
      return (
        At.useEffect(
          () => (
            S.itemMap.set(c, { ref: c, ...d }),
            () => void S.itemMap.delete(c)
          ),
        ),
        y.jsx(yr, { [v]: "", ref: p, children: w })
      );
    });
  f.displayName = u;
  function m(x) {
    const E = i(e + "CollectionConsumer", x);
    return At.useCallback(() => {
      const w = E.collectionRef.current;
      if (!w) return [];
      const d = Array.from(w.querySelectorAll(`[${v}]`));
      return Array.from(E.itemMap.values()).sort(
        (S, C) => d.indexOf(S.ref.current) - d.indexOf(C.ref.current),
      );
    }, [E.collectionRef, E.itemMap]);
  }
  return [{ Provider: s, Slot: a, ItemSlot: f }, m, r];
}
function gy(e, t) {
  const n = h.createContext(t),
    r = (i) => {
      const { children: s, ...l } = i,
        a = h.useMemo(() => l, Object.values(l));
      return y.jsx(n.Provider, { value: a, children: s });
    };
  r.displayName = e + "Provider";
  function o(i) {
    const s = h.useContext(n);
    if (s) return s;
    if (t !== void 0) return t;
    throw new Error(`\`${i}\` must be used within \`${e}\``);
  }
  return [r, o];
}
function Np(e, t = []) {
  let n = [];
  function r(i, s) {
    const l = h.createContext(s),
      a = n.length;
    n = [...n, s];
    const u = (f) => {
      var d;
      const { scope: m, children: x, ...E } = f,
        g = ((d = m == null ? void 0 : m[e]) == null ? void 0 : d[a]) || l,
        w = h.useMemo(() => E, Object.values(E));
      return y.jsx(g.Provider, { value: w, children: x });
    };
    u.displayName = i + "Provider";
    function v(f, m) {
      var g;
      const x = ((g = m == null ? void 0 : m[e]) == null ? void 0 : g[a]) || l,
        E = h.useContext(x);
      if (E) return E;
      if (s !== void 0) return s;
      throw new Error(`\`${f}\` must be used within \`${i}\``);
    }
    return [u, v];
  }
  const o = () => {
    const i = n.map((s) => h.createContext(s));
    return function (l) {
      const a = (l == null ? void 0 : l[e]) || i;
      return h.useMemo(() => ({ [`__scope${e}`]: { ...l, [e]: a } }), [l, a]);
    };
  };
  return ((o.scopeName = e), [r, yy(o, ...t)]);
}
function yy(...e) {
  const t = e[0];
  if (e.length === 1) return t;
  const n = () => {
    const r = e.map((o) => ({ useScope: o(), scopeName: o.scopeName }));
    return function (i) {
      const s = r.reduce((l, { useScope: a, scopeName: u }) => {
        const f = a(i)[`__scope${u}`];
        return { ...l, ...f };
      }, {});
      return h.useMemo(() => ({ [`__scope${t.scopeName}`]: s }), [s]);
    };
  };
  return ((n.scopeName = t.scopeName), n);
}
var wy = [
    "a",
    "button",
    "div",
    "form",
    "h2",
    "h3",
    "img",
    "input",
    "label",
    "li",
    "nav",
    "ol",
    "p",
    "span",
    "svg",
    "ul",
  ],
  ke = wy.reduce((e, t) => {
    const n = h.forwardRef((r, o) => {
      const { asChild: i, ...s } = r,
        l = i ? yr : t;
      return (
        typeof window < "u" && (window[Symbol.for("radix-ui")] = !0),
        y.jsx(l, { ...s, ref: o })
      );
    });
    return ((n.displayName = `Primitive.${t}`), { ...e, [t]: n });
  }, {});
function Tp(e, t) {
  e && Ji.flushSync(() => e.dispatchEvent(t));
}
function ct(e) {
  const t = h.useRef(e);
  return (
    h.useEffect(() => {
      t.current = e;
    }),
    h.useMemo(
      () =>
        (...n) => {
          var r;
          return (r = t.current) == null ? void 0 : r.call(t, ...n);
        },
      [],
    )
  );
}
function xy(e, t = globalThis == null ? void 0 : globalThis.document) {
  const n = ct(e);
  h.useEffect(() => {
    const r = (o) => {
      o.key === "Escape" && n(o);
    };
    return (
      t.addEventListener("keydown", r, { capture: !0 }),
      () => t.removeEventListener("keydown", r, { capture: !0 })
    );
  }, [n, t]);
}
var Sy = "DismissableLayer",
  Ul = "dismissableLayer.update",
  Ey = "dismissableLayer.pointerDownOutside",
  Cy = "dismissableLayer.focusOutside",
  Nc,
  Rp = h.createContext({
    layers: new Set(),
    layersWithOutsidePointerEventsDisabled: new Set(),
    branches: new Set(),
  }),
  Va = h.forwardRef((e, t) => {
    const {
        disableOutsidePointerEvents: n = !1,
        onEscapeKeyDown: r,
        onPointerDownOutside: o,
        onFocusOutside: i,
        onInteractOutside: s,
        onDismiss: l,
        ...a
      } = e,
      u = h.useContext(Rp),
      [v, f] = h.useState(null),
      m =
        (v == null ? void 0 : v.ownerDocument) ??
        (globalThis == null ? void 0 : globalThis.document),
      [, x] = h.useState({}),
      E = ut(t, (k) => f(k)),
      g = Array.from(u.layers),
      [w] = [...u.layersWithOutsidePointerEventsDisabled].slice(-1),
      d = g.indexOf(w),
      c = v ? g.indexOf(v) : -1,
      p = u.layersWithOutsidePointerEventsDisabled.size > 0,
      S = c >= d,
      C = Py((k) => {
        const T = k.target,
          b = [...u.branches].some((D) => D.contains(T));
        !S ||
          b ||
          (o == null || o(k),
          s == null || s(k),
          k.defaultPrevented || l == null || l());
      }, m),
      N = Ny((k) => {
        const T = k.target;
        [...u.branches].some((D) => D.contains(T)) ||
          (i == null || i(k),
          s == null || s(k),
          k.defaultPrevented || l == null || l());
      }, m);
    return (
      xy((k) => {
        c === u.layers.size - 1 &&
          (r == null || r(k),
          !k.defaultPrevented && l && (k.preventDefault(), l()));
      }, m),
      h.useEffect(() => {
        if (v)
          return (
            n &&
              (u.layersWithOutsidePointerEventsDisabled.size === 0 &&
                ((Nc = m.body.style.pointerEvents),
                (m.body.style.pointerEvents = "none")),
              u.layersWithOutsidePointerEventsDisabled.add(v)),
            u.layers.add(v),
            Tc(),
            () => {
              n &&
                u.layersWithOutsidePointerEventsDisabled.size === 1 &&
                (m.body.style.pointerEvents = Nc);
            }
          );
      }, [v, m, n, u]),
      h.useEffect(
        () => () => {
          v &&
            (u.layers.delete(v),
            u.layersWithOutsidePointerEventsDisabled.delete(v),
            Tc());
        },
        [v, u],
      ),
      h.useEffect(() => {
        const k = () => x({});
        return (
          document.addEventListener(Ul, k),
          () => document.removeEventListener(Ul, k)
        );
      }, []),
      y.jsx(ke.div, {
        ...a,
        ref: E,
        style: {
          pointerEvents: p ? (S ? "auto" : "none") : void 0,
          ...e.style,
        },
        onFocusCapture: pe(e.onFocusCapture, N.onFocusCapture),
        onBlurCapture: pe(e.onBlurCapture, N.onBlurCapture),
        onPointerDownCapture: pe(
          e.onPointerDownCapture,
          C.onPointerDownCapture,
        ),
      })
    );
  });
Va.displayName = Sy;
var ky = "DismissableLayerBranch",
  _p = h.forwardRef((e, t) => {
    const n = h.useContext(Rp),
      r = h.useRef(null),
      o = ut(t, r);
    return (
      h.useEffect(() => {
        const i = r.current;
        if (i)
          return (
            n.branches.add(i),
            () => {
              n.branches.delete(i);
            }
          );
      }, [n.branches]),
      y.jsx(ke.div, { ...e, ref: o })
    );
  });
_p.displayName = ky;
function Py(e, t = globalThis == null ? void 0 : globalThis.document) {
  const n = ct(e),
    r = h.useRef(!1),
    o = h.useRef(() => {});
  return (
    h.useEffect(() => {
      const i = (l) => {
          if (l.target && !r.current) {
            let a = function () {
              Mp(Ey, n, u, { discrete: !0 });
            };
            const u = { originalEvent: l };
            l.pointerType === "touch"
              ? (t.removeEventListener("click", o.current),
                (o.current = a),
                t.addEventListener("click", o.current, { once: !0 }))
              : a();
          } else t.removeEventListener("click", o.current);
          r.current = !1;
        },
        s = window.setTimeout(() => {
          t.addEventListener("pointerdown", i);
        }, 0);
      return () => {
        (window.clearTimeout(s),
          t.removeEventListener("pointerdown", i),
          t.removeEventListener("click", o.current));
      };
    }, [t, n]),
    { onPointerDownCapture: () => (r.current = !0) }
  );
}
function Ny(e, t = globalThis == null ? void 0 : globalThis.document) {
  const n = ct(e),
    r = h.useRef(!1);
  return (
    h.useEffect(() => {
      const o = (i) => {
        i.target &&
          !r.current &&
          Mp(Cy, n, { originalEvent: i }, { discrete: !1 });
      };
      return (
        t.addEventListener("focusin", o),
        () => t.removeEventListener("focusin", o)
      );
    }, [t, n]),
    {
      onFocusCapture: () => (r.current = !0),
      onBlurCapture: () => (r.current = !1),
    }
  );
}
function Tc() {
  const e = new CustomEvent(Ul);
  document.dispatchEvent(e);
}
function Mp(e, t, n, { discrete: r }) {
  const o = n.originalEvent.target,
    i = new CustomEvent(e, { bubbles: !1, cancelable: !0, detail: n });
  (t && o.addEventListener(e, t, { once: !0 }),
    r ? Tp(o, i) : o.dispatchEvent(i));
}
var Ty = Va,
  Ry = _p,
  ho = globalThis != null && globalThis.document ? h.useLayoutEffect : () => {},
  _y = "Portal",
  Ba = h.forwardRef((e, t) => {
    var l;
    const { container: n, ...r } = e,
      [o, i] = h.useState(!1);
    ho(() => i(!0), []);
    const s =
      n ||
      (o &&
        ((l = globalThis == null ? void 0 : globalThis.document) == null
          ? void 0
          : l.body));
    return s ? Og.createPortal(y.jsx(ke.div, { ...r, ref: t }), s) : null;
  });
Ba.displayName = _y;
function My(e, t) {
  return h.useReducer((n, r) => t[n][r] ?? n, e);
}
var Eo = (e) => {
  const { present: t, children: n } = e,
    r = Oy(t),
    o =
      typeof n == "function" ? n({ present: r.isPresent }) : h.Children.only(n),
    i = ut(r.ref, jy(o));
  return typeof n == "function" || r.isPresent
    ? h.cloneElement(o, { ref: i })
    : null;
};
Eo.displayName = "Presence";
function Oy(e) {
  const [t, n] = h.useState(),
    r = h.useRef({}),
    o = h.useRef(e),
    i = h.useRef("none"),
    s = e ? "mounted" : "unmounted",
    [l, a] = My(s, {
      mounted: { UNMOUNT: "unmounted", ANIMATION_OUT: "unmountSuspended" },
      unmountSuspended: { MOUNT: "mounted", ANIMATION_END: "unmounted" },
      unmounted: { MOUNT: "mounted" },
    });
  return (
    h.useEffect(() => {
      const u = Wo(r.current);
      i.current = l === "mounted" ? u : "none";
    }, [l]),
    ho(() => {
      const u = r.current,
        v = o.current;
      if (v !== e) {
        const m = i.current,
          x = Wo(u);
        (e
          ? a("MOUNT")
          : x === "none" || (u == null ? void 0 : u.display) === "none"
            ? a("UNMOUNT")
            : a(v && m !== x ? "ANIMATION_OUT" : "UNMOUNT"),
          (o.current = e));
      }
    }, [e, a]),
    ho(() => {
      if (t) {
        let u;
        const v = t.ownerDocument.defaultView ?? window,
          f = (x) => {
            const g = Wo(r.current).includes(x.animationName);
            if (x.target === t && g && (a("ANIMATION_END"), !o.current)) {
              const w = t.style.animationFillMode;
              ((t.style.animationFillMode = "forwards"),
                (u = v.setTimeout(() => {
                  t.style.animationFillMode === "forwards" &&
                    (t.style.animationFillMode = w);
                })));
            }
          },
          m = (x) => {
            x.target === t && (i.current = Wo(r.current));
          };
        return (
          t.addEventListener("animationstart", m),
          t.addEventListener("animationcancel", f),
          t.addEventListener("animationend", f),
          () => {
            (v.clearTimeout(u),
              t.removeEventListener("animationstart", m),
              t.removeEventListener("animationcancel", f),
              t.removeEventListener("animationend", f));
          }
        );
      } else a("ANIMATION_END");
    }, [t, a]),
    {
      isPresent: ["mounted", "unmountSuspended"].includes(l),
      ref: h.useCallback((u) => {
        (u && (r.current = getComputedStyle(u)), n(u));
      }, []),
    }
  );
}
function Wo(e) {
  return (e == null ? void 0 : e.animationName) || "none";
}
function jy(e) {
  var r, o;
  let t =
      (r = Object.getOwnPropertyDescriptor(e.props, "ref")) == null
        ? void 0
        : r.get,
    n = t && "isReactWarning" in t && t.isReactWarning;
  return n
    ? e.ref
    : ((t =
        (o = Object.getOwnPropertyDescriptor(e, "ref")) == null
          ? void 0
          : o.get),
      (n = t && "isReactWarning" in t && t.isReactWarning),
      n ? e.props.ref : e.props.ref || e.ref);
}
function Op({ prop: e, defaultProp: t, onChange: n = () => {} }) {
  const [r, o] = Dy({ defaultProp: t, onChange: n }),
    i = e !== void 0,
    s = i ? e : r,
    l = ct(n),
    a = h.useCallback(
      (u) => {
        if (i) {
          const f = typeof u == "function" ? u(e) : u;
          f !== e && l(f);
        } else o(u);
      },
      [i, e, o, l],
    );
  return [s, a];
}
function Dy({ defaultProp: e, onChange: t }) {
  const n = h.useState(e),
    [r] = n,
    o = h.useRef(r),
    i = ct(t);
  return (
    h.useEffect(() => {
      o.current !== r && (i(r), (o.current = r));
    }, [r, o, i]),
    n
  );
}
var by = "VisuallyHidden",
  Ha = h.forwardRef((e, t) =>
    y.jsx(ke.span, {
      ...e,
      ref: t,
      style: {
        position: "absolute",
        border: 0,
        width: 1,
        height: 1,
        padding: 0,
        margin: -1,
        overflow: "hidden",
        clip: "rect(0, 0, 0, 0)",
        whiteSpace: "nowrap",
        wordWrap: "normal",
        ...e.style,
      },
    }),
  );
Ha.displayName = by;
var Qa = "ToastProvider",
  [Ka, Iy, Ly] = vy("Toast"),
  [jp, qw] = Np("Toast", [Ly]),
  [Ay, ns] = jp(Qa),
  Dp = (e) => {
    const {
        __scopeToast: t,
        label: n = "Notification",
        duration: r = 5e3,
        swipeDirection: o = "right",
        swipeThreshold: i = 50,
        children: s,
      } = e,
      [l, a] = h.useState(null),
      [u, v] = h.useState(0),
      f = h.useRef(!1),
      m = h.useRef(!1);
    return (
      n.trim() ||
        console.error(
          `Invalid prop \`label\` supplied to \`${Qa}\`. Expected non-empty \`string\`.`,
        ),
      y.jsx(Ka.Provider, {
        scope: t,
        children: y.jsx(Ay, {
          scope: t,
          label: n,
          duration: r,
          swipeDirection: o,
          swipeThreshold: i,
          toastCount: u,
          viewport: l,
          onViewportChange: a,
          onToastAdd: h.useCallback(() => v((x) => x + 1), []),
          onToastRemove: h.useCallback(() => v((x) => x - 1), []),
          isFocusedToastEscapeKeyDownRef: f,
          isClosePausedRef: m,
          children: s,
        }),
      })
    );
  };
Dp.displayName = Qa;
var bp = "ToastViewport",
  Fy = ["F8"],
  Wl = "toast.viewportPause",
  Vl = "toast.viewportResume",
  Ip = h.forwardRef((e, t) => {
    const {
        __scopeToast: n,
        hotkey: r = Fy,
        label: o = "Notifications ({hotkey})",
        ...i
      } = e,
      s = ns(bp, n),
      l = Iy(n),
      a = h.useRef(null),
      u = h.useRef(null),
      v = h.useRef(null),
      f = h.useRef(null),
      m = ut(t, f, s.onViewportChange),
      x = r.join("+").replace(/Key/g, "").replace(/Digit/g, ""),
      E = s.toastCount > 0;
    (h.useEffect(() => {
      const w = (d) => {
        var p;
        r.length !== 0 &&
          r.every((S) => d[S] || d.code === S) &&
          ((p = f.current) == null || p.focus());
      };
      return (
        document.addEventListener("keydown", w),
        () => document.removeEventListener("keydown", w)
      );
    }, [r]),
      h.useEffect(() => {
        const w = a.current,
          d = f.current;
        if (E && w && d) {
          const c = () => {
              if (!s.isClosePausedRef.current) {
                const N = new CustomEvent(Wl);
                (d.dispatchEvent(N), (s.isClosePausedRef.current = !0));
              }
            },
            p = () => {
              if (s.isClosePausedRef.current) {
                const N = new CustomEvent(Vl);
                (d.dispatchEvent(N), (s.isClosePausedRef.current = !1));
              }
            },
            S = (N) => {
              !w.contains(N.relatedTarget) && p();
            },
            C = () => {
              w.contains(document.activeElement) || p();
            };
          return (
            w.addEventListener("focusin", c),
            w.addEventListener("focusout", S),
            w.addEventListener("pointermove", c),
            w.addEventListener("pointerleave", C),
            window.addEventListener("blur", c),
            window.addEventListener("focus", p),
            () => {
              (w.removeEventListener("focusin", c),
                w.removeEventListener("focusout", S),
                w.removeEventListener("pointermove", c),
                w.removeEventListener("pointerleave", C),
                window.removeEventListener("blur", c),
                window.removeEventListener("focus", p));
            }
          );
        }
      }, [E, s.isClosePausedRef]));
    const g = h.useCallback(
      ({ tabbingDirection: w }) => {
        const c = l().map((p) => {
          const S = p.ref.current,
            C = [S, ...qy(S)];
          return w === "forwards" ? C : C.reverse();
        });
        return (w === "forwards" ? c.reverse() : c).flat();
      },
      [l],
    );
    return (
      h.useEffect(() => {
        const w = f.current;
        if (w) {
          const d = (c) => {
            var C, N, k;
            const p = c.altKey || c.ctrlKey || c.metaKey;
            if (c.key === "Tab" && !p) {
              const T = document.activeElement,
                b = c.shiftKey;
              if (c.target === w && b) {
                (C = u.current) == null || C.focus();
                return;
              }
              const z = g({ tabbingDirection: b ? "backwards" : "forwards" }),
                Pe = z.findIndex((L) => L === T);
              Ls(z.slice(Pe + 1))
                ? c.preventDefault()
                : b
                  ? (N = u.current) == null || N.focus()
                  : (k = v.current) == null || k.focus();
            }
          };
          return (
            w.addEventListener("keydown", d),
            () => w.removeEventListener("keydown", d)
          );
        }
      }, [l, g]),
      y.jsxs(Ry, {
        ref: a,
        role: "region",
        "aria-label": o.replace("{hotkey}", x),
        tabIndex: -1,
        style: { pointerEvents: E ? void 0 : "none" },
        children: [
          E &&
            y.jsx(Bl, {
              ref: u,
              onFocusFromOutsideViewport: () => {
                const w = g({ tabbingDirection: "forwards" });
                Ls(w);
              },
            }),
          y.jsx(Ka.Slot, {
            scope: n,
            children: y.jsx(ke.ol, { tabIndex: -1, ...i, ref: m }),
          }),
          E &&
            y.jsx(Bl, {
              ref: v,
              onFocusFromOutsideViewport: () => {
                const w = g({ tabbingDirection: "backwards" });
                Ls(w);
              },
            }),
        ],
      })
    );
  });
Ip.displayName = bp;
var Lp = "ToastFocusProxy",
  Bl = h.forwardRef((e, t) => {
    const { __scopeToast: n, onFocusFromOutsideViewport: r, ...o } = e,
      i = ns(Lp, n);
    return y.jsx(Ha, {
      "aria-hidden": !0,
      tabIndex: 0,
      ...o,
      ref: t,
      style: { position: "fixed" },
      onFocus: (s) => {
        var u;
        const l = s.relatedTarget;
        !((u = i.viewport) != null && u.contains(l)) && r();
      },
    });
  });
Bl.displayName = Lp;
var rs = "Toast",
  zy = "toast.swipeStart",
  $y = "toast.swipeMove",
  Uy = "toast.swipeCancel",
  Wy = "toast.swipeEnd",
  Ap = h.forwardRef((e, t) => {
    const { forceMount: n, open: r, defaultOpen: o, onOpenChange: i, ...s } = e,
      [l = !0, a] = Op({ prop: r, defaultProp: o, onChange: i });
    return y.jsx(Eo, {
      present: n || l,
      children: y.jsx(Hy, {
        open: l,
        ...s,
        ref: t,
        onClose: () => a(!1),
        onPause: ct(e.onPause),
        onResume: ct(e.onResume),
        onSwipeStart: pe(e.onSwipeStart, (u) => {
          u.currentTarget.setAttribute("data-swipe", "start");
        }),
        onSwipeMove: pe(e.onSwipeMove, (u) => {
          const { x: v, y: f } = u.detail.delta;
          (u.currentTarget.setAttribute("data-swipe", "move"),
            u.currentTarget.style.setProperty(
              "--radix-toast-swipe-move-x",
              `${v}px`,
            ),
            u.currentTarget.style.setProperty(
              "--radix-toast-swipe-move-y",
              `${f}px`,
            ));
        }),
        onSwipeCancel: pe(e.onSwipeCancel, (u) => {
          (u.currentTarget.setAttribute("data-swipe", "cancel"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y"));
        }),
        onSwipeEnd: pe(e.onSwipeEnd, (u) => {
          const { x: v, y: f } = u.detail.delta;
          (u.currentTarget.setAttribute("data-swipe", "end"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),
            u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),
            u.currentTarget.style.setProperty(
              "--radix-toast-swipe-end-x",
              `${v}px`,
            ),
            u.currentTarget.style.setProperty(
              "--radix-toast-swipe-end-y",
              `${f}px`,
            ),
            a(!1));
        }),
      }),
    });
  });
Ap.displayName = rs;
var [Vy, By] = jp(rs, { onClose() {} }),
  Hy = h.forwardRef((e, t) => {
    const {
        __scopeToast: n,
        type: r = "foreground",
        duration: o,
        open: i,
        onClose: s,
        onEscapeKeyDown: l,
        onPause: a,
        onResume: u,
        onSwipeStart: v,
        onSwipeMove: f,
        onSwipeCancel: m,
        onSwipeEnd: x,
        ...E
      } = e,
      g = ns(rs, n),
      [w, d] = h.useState(null),
      c = ut(t, (L) => d(L)),
      p = h.useRef(null),
      S = h.useRef(null),
      C = o || g.duration,
      N = h.useRef(0),
      k = h.useRef(C),
      T = h.useRef(0),
      { onToastAdd: b, onToastRemove: D } = g,
      H = ct(() => {
        var ie;
        ((w == null ? void 0 : w.contains(document.activeElement)) &&
          ((ie = g.viewport) == null || ie.focus()),
          s());
      }),
      z = h.useCallback(
        (L) => {
          !L ||
            L === 1 / 0 ||
            (window.clearTimeout(T.current),
            (N.current = new Date().getTime()),
            (T.current = window.setTimeout(H, L)));
        },
        [H],
      );
    (h.useEffect(() => {
      const L = g.viewport;
      if (L) {
        const ie = () => {
            (z(k.current), u == null || u());
          },
          le = () => {
            const je = new Date().getTime() - N.current;
            ((k.current = k.current - je),
              window.clearTimeout(T.current),
              a == null || a());
          };
        return (
          L.addEventListener(Wl, le),
          L.addEventListener(Vl, ie),
          () => {
            (L.removeEventListener(Wl, le), L.removeEventListener(Vl, ie));
          }
        );
      }
    }, [g.viewport, C, a, u, z]),
      h.useEffect(() => {
        i && !g.isClosePausedRef.current && z(C);
      }, [i, C, g.isClosePausedRef, z]),
      h.useEffect(() => (b(), () => D()), [b, D]));
    const Pe = h.useMemo(() => (w ? Bp(w) : null), [w]);
    return g.viewport
      ? y.jsxs(y.Fragment, {
          children: [
            Pe &&
              y.jsx(Qy, {
                __scopeToast: n,
                role: "status",
                "aria-live": r === "foreground" ? "assertive" : "polite",
                "aria-atomic": !0,
                children: Pe,
              }),
            y.jsx(Vy, {
              scope: n,
              onClose: H,
              children: Ji.createPortal(
                y.jsx(Ka.ItemSlot, {
                  scope: n,
                  children: y.jsx(Ty, {
                    asChild: !0,
                    onEscapeKeyDown: pe(l, () => {
                      (g.isFocusedToastEscapeKeyDownRef.current || H(),
                        (g.isFocusedToastEscapeKeyDownRef.current = !1));
                    }),
                    children: y.jsx(ke.li, {
                      role: "status",
                      "aria-live": "off",
                      "aria-atomic": !0,
                      tabIndex: 0,
                      "data-state": i ? "open" : "closed",
                      "data-swipe-direction": g.swipeDirection,
                      ...E,
                      ref: c,
                      style: {
                        userSelect: "none",
                        touchAction: "none",
                        ...e.style,
                      },
                      onKeyDown: pe(e.onKeyDown, (L) => {
                        L.key === "Escape" &&
                          (l == null || l(L.nativeEvent),
                          L.nativeEvent.defaultPrevented ||
                            ((g.isFocusedToastEscapeKeyDownRef.current = !0),
                            H()));
                      }),
                      onPointerDown: pe(e.onPointerDown, (L) => {
                        L.button === 0 &&
                          (p.current = { x: L.clientX, y: L.clientY });
                      }),
                      onPointerMove: pe(e.onPointerMove, (L) => {
                        if (!p.current) return;
                        const ie = L.clientX - p.current.x,
                          le = L.clientY - p.current.y,
                          je = !!S.current,
                          M = ["left", "right"].includes(g.swipeDirection),
                          j = ["left", "up"].includes(g.swipeDirection)
                            ? Math.min
                            : Math.max,
                          A = M ? j(0, ie) : 0,
                          _ = M ? 0 : j(0, le),
                          I = L.pointerType === "touch" ? 10 : 2,
                          ee = { x: A, y: _ },
                          re = { originalEvent: L, delta: ee };
                        je
                          ? ((S.current = ee), Vo($y, f, re, { discrete: !1 }))
                          : Rc(ee, g.swipeDirection, I)
                            ? ((S.current = ee),
                              Vo(zy, v, re, { discrete: !1 }),
                              L.target.setPointerCapture(L.pointerId))
                            : (Math.abs(ie) > I || Math.abs(le) > I) &&
                              (p.current = null);
                      }),
                      onPointerUp: pe(e.onPointerUp, (L) => {
                        const ie = S.current,
                          le = L.target;
                        if (
                          (le.hasPointerCapture(L.pointerId) &&
                            le.releasePointerCapture(L.pointerId),
                          (S.current = null),
                          (p.current = null),
                          ie)
                        ) {
                          const je = L.currentTarget,
                            M = { originalEvent: L, delta: ie };
                          (Rc(ie, g.swipeDirection, g.swipeThreshold)
                            ? Vo(Wy, x, M, { discrete: !0 })
                            : Vo(Uy, m, M, { discrete: !0 }),
                            je.addEventListener(
                              "click",
                              (j) => j.preventDefault(),
                              { once: !0 },
                            ));
                        }
                      }),
                    }),
                  }),
                }),
                g.viewport,
              ),
            }),
          ],
        })
      : null;
  }),
  Qy = (e) => {
    const { __scopeToast: t, children: n, ...r } = e,
      o = ns(rs, t),
      [i, s] = h.useState(!1),
      [l, a] = h.useState(!1);
    return (
      Xy(() => s(!0)),
      h.useEffect(() => {
        const u = window.setTimeout(() => a(!0), 1e3);
        return () => window.clearTimeout(u);
      }, []),
      l
        ? null
        : y.jsx(Ba, {
            asChild: !0,
            children: y.jsx(Ha, {
              ...r,
              children:
                i && y.jsxs(y.Fragment, { children: [o.label, " ", n] }),
            }),
          })
    );
  },
  Ky = "ToastTitle",
  Fp = h.forwardRef((e, t) => {
    const { __scopeToast: n, ...r } = e;
    return y.jsx(ke.div, { ...r, ref: t });
  });
Fp.displayName = Ky;
var Gy = "ToastDescription",
  zp = h.forwardRef((e, t) => {
    const { __scopeToast: n, ...r } = e;
    return y.jsx(ke.div, { ...r, ref: t });
  });
zp.displayName = Gy;
var $p = "ToastAction",
  Up = h.forwardRef((e, t) => {
    const { altText: n, ...r } = e;
    return n.trim()
      ? y.jsx(Vp, {
          altText: n,
          asChild: !0,
          children: y.jsx(Ga, { ...r, ref: t }),
        })
      : (console.error(
          `Invalid prop \`altText\` supplied to \`${$p}\`. Expected non-empty \`string\`.`,
        ),
        null);
  });
Up.displayName = $p;
var Wp = "ToastClose",
  Ga = h.forwardRef((e, t) => {
    const { __scopeToast: n, ...r } = e,
      o = By(Wp, n);
    return y.jsx(Vp, {
      asChild: !0,
      children: y.jsx(ke.button, {
        type: "button",
        ...r,
        ref: t,
        onClick: pe(e.onClick, o.onClose),
      }),
    });
  });
Ga.displayName = Wp;
var Vp = h.forwardRef((e, t) => {
  const { __scopeToast: n, altText: r, ...o } = e;
  return y.jsx(ke.div, {
    "data-radix-toast-announce-exclude": "",
    "data-radix-toast-announce-alt": r || void 0,
    ...o,
    ref: t,
  });
});
function Bp(e) {
  const t = [];
  return (
    Array.from(e.childNodes).forEach((r) => {
      if (
        (r.nodeType === r.TEXT_NODE && r.textContent && t.push(r.textContent),
        Yy(r))
      ) {
        const o = r.ariaHidden || r.hidden || r.style.display === "none",
          i = r.dataset.radixToastAnnounceExclude === "";
        if (!o)
          if (i) {
            const s = r.dataset.radixToastAnnounceAlt;
            s && t.push(s);
          } else t.push(...Bp(r));
      }
    }),
    t
  );
}
function Vo(e, t, n, { discrete: r }) {
  const o = n.originalEvent.currentTarget,
    i = new CustomEvent(e, { bubbles: !0, cancelable: !0, detail: n });
  (t && o.addEventListener(e, t, { once: !0 }),
    r ? Tp(o, i) : o.dispatchEvent(i));
}
var Rc = (e, t, n = 0) => {
  const r = Math.abs(e.x),
    o = Math.abs(e.y),
    i = r > o;
  return t === "left" || t === "right" ? i && r > n : !i && o > n;
};
function Xy(e = () => {}) {
  const t = ct(e);
  ho(() => {
    let n = 0,
      r = 0;
    return (
      (n = window.requestAnimationFrame(
        () => (r = window.requestAnimationFrame(t)),
      )),
      () => {
        (window.cancelAnimationFrame(n), window.cancelAnimationFrame(r));
      }
    );
  }, [t]);
}
function Yy(e) {
  return e.nodeType === e.ELEMENT_NODE;
}
function qy(e) {
  const t = [],
    n = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
      acceptNode: (r) => {
        const o = r.tagName === "INPUT" && r.type === "hidden";
        return r.disabled || r.hidden || o
          ? NodeFilter.FILTER_SKIP
          : r.tabIndex >= 0
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP;
      },
    });
  for (; n.nextNode(); ) t.push(n.currentNode);
  return t;
}
function Ls(e) {
  const t = document.activeElement;
  return e.some((n) =>
    n === t ? !0 : (n.focus(), document.activeElement !== t),
  );
}
var Zy = Dp,
  Hp = Ip,
  Qp = Ap,
  Kp = Fp,
  Gp = zp,
  Xp = Up,
  Yp = Ga;
function qp(e) {
  var t,
    n,
    r = "";
  if (typeof e == "string" || typeof e == "number") r += e;
  else if (typeof e == "object")
    if (Array.isArray(e))
      for (t = 0; t < e.length; t++)
        e[t] && (n = qp(e[t])) && (r && (r += " "), (r += n));
    else for (t in e) e[t] && (r && (r += " "), (r += t));
  return r;
}
function Jy() {
  for (var e, t, n = 0, r = ""; n < arguments.length; )
    (e = arguments[n++]) && (t = qp(e)) && (r && (r += " "), (r += t));
  return r;
}
const _c = (e) => (typeof e == "boolean" ? "".concat(e) : e === 0 ? "0" : e),
  Mc = Jy,
  Zp = (e, t) => (n) => {
    var r;
    if ((t == null ? void 0 : t.variants) == null)
      return Mc(
        e,
        n == null ? void 0 : n.class,
        n == null ? void 0 : n.className,
      );
    const { variants: o, defaultVariants: i } = t,
      s = Object.keys(o).map((u) => {
        const v = n == null ? void 0 : n[u],
          f = i == null ? void 0 : i[u];
        if (v === null) return null;
        const m = _c(v) || _c(f);
        return o[u][m];
      }),
      l =
        n &&
        Object.entries(n).reduce((u, v) => {
          let [f, m] = v;
          return (m === void 0 || (u[f] = m), u);
        }, {}),
      a =
        t == null || (r = t.compoundVariants) === null || r === void 0
          ? void 0
          : r.reduce((u, v) => {
              let { class: f, className: m, ...x } = v;
              return Object.entries(x).every((E) => {
                let [g, w] = E;
                return Array.isArray(w)
                  ? w.includes({ ...i, ...l }[g])
                  : { ...i, ...l }[g] === w;
              })
                ? [...u, f, m]
                : u;
            }, []);
    return Mc(
      e,
      s,
      a,
      n == null ? void 0 : n.class,
      n == null ? void 0 : n.className,
    );
  };
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const e0 = (e) => e.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase(),
  Jp = (...e) => e.filter((t, n, r) => !!t && r.indexOf(t) === n).join(" ");
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ var t0 = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  strokeWidth: 2,
  strokeLinecap: "round",
  strokeLinejoin: "round",
};
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const n0 = h.forwardRef(
  (
    {
      color: e = "currentColor",
      size: t = 24,
      strokeWidth: n = 2,
      absoluteStrokeWidth: r,
      className: o = "",
      children: i,
      iconNode: s,
      ...l
    },
    a,
  ) =>
    h.createElement(
      "svg",
      {
        ref: a,
        ...t0,
        width: t,
        height: t,
        stroke: e,
        strokeWidth: r ? (Number(n) * 24) / Number(t) : n,
        className: Jp("lucide", o),
        ...l,
      },
      [
        ...s.map(([u, v]) => h.createElement(u, v)),
        ...(Array.isArray(i) ? i : [i]),
      ],
    ),
);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const dn = (e, t) => {
  const n = h.forwardRef(({ className: r, ...o }, i) =>
    h.createElement(n0, {
      ref: i,
      iconNode: t,
      className: Jp(`lucide-${e0(e)}`, r),
      ...o,
    }),
  );
  return ((n.displayName = `${e}`), n);
};
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const r0 = dn("Check", [["path", { d: "M20 6 9 17l-5-5", key: "1gmf2c" }]]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const o0 = dn("Link", [
  [
    "path",
    {
      d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",
      key: "1cjeqo",
    },
  ],
  [
    "path",
    {
      d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",
      key: "19qd67",
    },
  ],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const i0 = dn("Monitor", [
  [
    "rect",
    { width: "20", height: "14", x: "2", y: "3", rx: "2", key: "48i651" },
  ],
  ["line", { x1: "8", x2: "16", y1: "21", y2: "21", key: "1svkeh" }],
  ["line", { x1: "12", x2: "12", y1: "17", y2: "21", key: "vw1qmm" }],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const s0 = dn("Settings2", [
  ["path", { d: "M20 7h-9", key: "3s1dr2" }],
  ["path", { d: "M14 17H5", key: "gfn3mx" }],
  ["circle", { cx: "17", cy: "17", r: "3", key: "18b49y" }],
  ["circle", { cx: "7", cy: "7", r: "3", key: "dfmy0x" }],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const l0 = dn("SquareDashed", [
  ["path", { d: "M5 3a2 2 0 0 0-2 2", key: "y57alp" }],
  ["path", { d: "M19 3a2 2 0 0 1 2 2", key: "18rm91" }],
  ["path", { d: "M21 19a2 2 0 0 1-2 2", key: "1j7049" }],
  ["path", { d: "M5 21a2 2 0 0 1-2-2", key: "sbafld" }],
  ["path", { d: "M9 3h1", key: "1yesri" }],
  ["path", { d: "M9 21h1", key: "15o7lz" }],
  ["path", { d: "M14 3h1", key: "1ec4yj" }],
  ["path", { d: "M14 21h1", key: "v9vybs" }],
  ["path", { d: "M3 9v1", key: "1r0deq" }],
  ["path", { d: "M21 9v1", key: "mxsmne" }],
  ["path", { d: "M3 14v1", key: "vnatye" }],
  ["path", { d: "M21 14v1", key: "169vum" }],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const a0 = dn("StickyNote", [
  [
    "path",
    {
      d: "M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",
      key: "qazsjp",
    },
  ],
  ["path", { d: "M15 3v4a2 2 0 0 0 2 2h4", key: "40519r" }],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const u0 = dn("Trash2", [
  ["path", { d: "M3 6h18", key: "d0wm0j" }],
  ["path", { d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6", key: "4alrt4" }],
  ["path", { d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2", key: "v07s0e" }],
  ["line", { x1: "10", x2: "10", y1: "11", y2: "17", key: "1uufr5" }],
  ["line", { x1: "14", x2: "14", y1: "11", y2: "17", key: "xtxkd" }],
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ const eh = dn("X", [
  ["path", { d: "M18 6 6 18", key: "1bl5f8" }],
  ["path", { d: "m6 6 12 12", key: "d8bk6v" }],
]);
function th(e) {
  var t,
    n,
    r = "";
  if (typeof e == "string" || typeof e == "number") r += e;
  else if (typeof e == "object")
    if (Array.isArray(e)) {
      var o = e.length;
      for (t = 0; t < o; t++)
        e[t] && (n = th(e[t])) && (r && (r += " "), (r += n));
    } else for (n in e) e[n] && (r && (r += " "), (r += n));
  return r;
}
function c0() {
  for (var e, t, n = 0, r = "", o = arguments.length; n < o; n++)
    (e = arguments[n]) && (t = th(e)) && (r && (r += " "), (r += t));
  return r;
}
const Xa = "-",
  d0 = (e) => {
    const t = p0(e),
      { conflictingClassGroups: n, conflictingClassGroupModifiers: r } = e;
    return {
      getClassGroupId: (s) => {
        const l = s.split(Xa);
        return (l[0] === "" && l.length !== 1 && l.shift(), nh(l, t) || f0(s));
      },
      getConflictingClassGroupIds: (s, l) => {
        const a = n[s] || [];
        return l && r[s] ? [...a, ...r[s]] : a;
      },
    };
  },
  nh = (e, t) => {
    var s;
    if (e.length === 0) return t.classGroupId;
    const n = e[0],
      r = t.nextPart.get(n),
      o = r ? nh(e.slice(1), r) : void 0;
    if (o) return o;
    if (t.validators.length === 0) return;
    const i = e.join(Xa);
    return (s = t.validators.find(({ validator: l }) => l(i))) == null
      ? void 0
      : s.classGroupId;
  },
  Oc = /^\[(.+)\]$/,
  f0 = (e) => {
    if (Oc.test(e)) {
      const t = Oc.exec(e)[1],
        n = t == null ? void 0 : t.substring(0, t.indexOf(":"));
      if (n) return "arbitrary.." + n;
    }
  },
  p0 = (e) => {
    const { theme: t, prefix: n } = e,
      r = { nextPart: new Map(), validators: [] };
    return (
      m0(Object.entries(e.classGroups), n).forEach(([i, s]) => {
        Hl(s, r, i, t);
      }),
      r
    );
  },
  Hl = (e, t, n, r) => {
    e.forEach((o) => {
      if (typeof o == "string") {
        const i = o === "" ? t : jc(t, o);
        i.classGroupId = n;
        return;
      }
      if (typeof o == "function") {
        if (h0(o)) {
          Hl(o(r), t, n, r);
          return;
        }
        t.validators.push({ validator: o, classGroupId: n });
        return;
      }
      Object.entries(o).forEach(([i, s]) => {
        Hl(s, jc(t, i), n, r);
      });
    });
  },
  jc = (e, t) => {
    let n = e;
    return (
      t.split(Xa).forEach((r) => {
        (n.nextPart.has(r) ||
          n.nextPart.set(r, { nextPart: new Map(), validators: [] }),
          (n = n.nextPart.get(r)));
      }),
      n
    );
  },
  h0 = (e) => e.isThemeGetter,
  m0 = (e, t) =>
    t
      ? e.map(([n, r]) => {
          const o = r.map((i) =>
            typeof i == "string"
              ? t + i
              : typeof i == "object"
                ? Object.fromEntries(
                    Object.entries(i).map(([s, l]) => [t + s, l]),
                  )
                : i,
          );
          return [n, o];
        })
      : e,
  v0 = (e) => {
    if (e < 1) return { get: () => {}, set: () => {} };
    let t = 0,
      n = new Map(),
      r = new Map();
    const o = (i, s) => {
      (n.set(i, s), t++, t > e && ((t = 0), (r = n), (n = new Map())));
    };
    return {
      get(i) {
        let s = n.get(i);
        if (s !== void 0) return s;
        if ((s = r.get(i)) !== void 0) return (o(i, s), s);
      },
      set(i, s) {
        n.has(i) ? n.set(i, s) : o(i, s);
      },
    };
  },
  rh = "!",
  g0 = (e) => {
    const { separator: t, experimentalParseClassName: n } = e,
      r = t.length === 1,
      o = t[0],
      i = t.length,
      s = (l) => {
        const a = [];
        let u = 0,
          v = 0,
          f;
        for (let w = 0; w < l.length; w++) {
          let d = l[w];
          if (u === 0) {
            if (d === o && (r || l.slice(w, w + i) === t)) {
              (a.push(l.slice(v, w)), (v = w + i));
              continue;
            }
            if (d === "/") {
              f = w;
              continue;
            }
          }
          d === "[" ? u++ : d === "]" && u--;
        }
        const m = a.length === 0 ? l : l.substring(v),
          x = m.startsWith(rh),
          E = x ? m.substring(1) : m,
          g = f && f > v ? f - v : void 0;
        return {
          modifiers: a,
          hasImportantModifier: x,
          baseClassName: E,
          maybePostfixModifierPosition: g,
        };
      };
    return n ? (l) => n({ className: l, parseClassName: s }) : s;
  },
  y0 = (e) => {
    if (e.length <= 1) return e;
    const t = [];
    let n = [];
    return (
      e.forEach((r) => {
        r[0] === "[" ? (t.push(...n.sort(), r), (n = [])) : n.push(r);
      }),
      t.push(...n.sort()),
      t
    );
  },
  w0 = (e) => ({ cache: v0(e.cacheSize), parseClassName: g0(e), ...d0(e) }),
  x0 = /\s+/,
  S0 = (e, t) => {
    const {
        parseClassName: n,
        getClassGroupId: r,
        getConflictingClassGroupIds: o,
      } = t,
      i = [],
      s = e.trim().split(x0);
    let l = "";
    for (let a = s.length - 1; a >= 0; a -= 1) {
      const u = s[a],
        {
          modifiers: v,
          hasImportantModifier: f,
          baseClassName: m,
          maybePostfixModifierPosition: x,
        } = n(u);
      let E = !!x,
        g = r(E ? m.substring(0, x) : m);
      if (!g) {
        if (!E) {
          l = u + (l.length > 0 ? " " + l : l);
          continue;
        }
        if (((g = r(m)), !g)) {
          l = u + (l.length > 0 ? " " + l : l);
          continue;
        }
        E = !1;
      }
      const w = y0(v).join(":"),
        d = f ? w + rh : w,
        c = d + g;
      if (i.includes(c)) continue;
      i.push(c);
      const p = o(g, E);
      for (let S = 0; S < p.length; ++S) {
        const C = p[S];
        i.push(d + C);
      }
      l = u + (l.length > 0 ? " " + l : l);
    }
    return l;
  };
function E0() {
  let e = 0,
    t,
    n,
    r = "";
  for (; e < arguments.length; )
    (t = arguments[e++]) && (n = oh(t)) && (r && (r += " "), (r += n));
  return r;
}
const oh = (e) => {
  if (typeof e == "string") return e;
  let t,
    n = "";
  for (let r = 0; r < e.length; r++)
    e[r] && (t = oh(e[r])) && (n && (n += " "), (n += t));
  return n;
};
function C0(e, ...t) {
  let n,
    r,
    o,
    i = s;
  function s(a) {
    const u = t.reduce((v, f) => f(v), e());
    return ((n = w0(u)), (r = n.cache.get), (o = n.cache.set), (i = l), l(a));
  }
  function l(a) {
    const u = r(a);
    if (u) return u;
    const v = S0(a, n);
    return (o(a, v), v);
  }
  return function () {
    return i(E0.apply(null, arguments));
  };
}
const K = (e) => {
    const t = (n) => n[e] || [];
    return ((t.isThemeGetter = !0), t);
  },
  ih = /^\[(?:([a-z-]+):)?(.+)\]$/i,
  k0 = /^\d+\/\d+$/,
  P0 = new Set(["px", "full", "screen"]),
  N0 = /^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,
  T0 =
    /\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,
  R0 = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,
  _0 = /^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,
  M0 =
    /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,
  Et = (e) => er(e) || P0.has(e) || k0.test(e),
  It = (e) => Cr(e, "length", F0),
  er = (e) => !!e && !Number.isNaN(Number(e)),
  As = (e) => Cr(e, "number", er),
  jr = (e) => !!e && Number.isInteger(Number(e)),
  O0 = (e) => e.endsWith("%") && er(e.slice(0, -1)),
  F = (e) => ih.test(e),
  Lt = (e) => N0.test(e),
  j0 = new Set(["length", "size", "percentage"]),
  D0 = (e) => Cr(e, j0, sh),
  b0 = (e) => Cr(e, "position", sh),
  I0 = new Set(["image", "url"]),
  L0 = (e) => Cr(e, I0, $0),
  A0 = (e) => Cr(e, "", z0),
  Dr = () => !0,
  Cr = (e, t, n) => {
    const r = ih.exec(e);
    return r
      ? r[1]
        ? typeof t == "string"
          ? r[1] === t
          : t.has(r[1])
        : n(r[2])
      : !1;
  },
  F0 = (e) => T0.test(e) && !R0.test(e),
  sh = () => !1,
  z0 = (e) => _0.test(e),
  $0 = (e) => M0.test(e),
  U0 = () => {
    const e = K("colors"),
      t = K("spacing"),
      n = K("blur"),
      r = K("brightness"),
      o = K("borderColor"),
      i = K("borderRadius"),
      s = K("borderSpacing"),
      l = K("borderWidth"),
      a = K("contrast"),
      u = K("grayscale"),
      v = K("hueRotate"),
      f = K("invert"),
      m = K("gap"),
      x = K("gradientColorStops"),
      E = K("gradientColorStopPositions"),
      g = K("inset"),
      w = K("margin"),
      d = K("opacity"),
      c = K("padding"),
      p = K("saturate"),
      S = K("scale"),
      C = K("sepia"),
      N = K("skew"),
      k = K("space"),
      T = K("translate"),
      b = () => ["auto", "contain", "none"],
      D = () => ["auto", "hidden", "clip", "visible", "scroll"],
      H = () => ["auto", F, t],
      z = () => [F, t],
      Pe = () => ["", Et, It],
      L = () => ["auto", er, F],
      ie = () => [
        "bottom",
        "center",
        "left",
        "left-bottom",
        "left-top",
        "right",
        "right-bottom",
        "right-top",
        "top",
      ],
      le = () => ["solid", "dashed", "dotted", "double", "none"],
      je = () => [
        "normal",
        "multiply",
        "screen",
        "overlay",
        "darken",
        "lighten",
        "color-dodge",
        "color-burn",
        "hard-light",
        "soft-light",
        "difference",
        "exclusion",
        "hue",
        "saturation",
        "color",
        "luminosity",
      ],
      M = () => [
        "start",
        "end",
        "center",
        "between",
        "around",
        "evenly",
        "stretch",
      ],
      j = () => ["", "0", F],
      A = () => [
        "auto",
        "avoid",
        "all",
        "avoid-page",
        "page",
        "left",
        "right",
        "column",
      ],
      _ = () => [er, F];
    return {
      cacheSize: 500,
      separator: ":",
      theme: {
        colors: [Dr],
        spacing: [Et, It],
        blur: ["none", "", Lt, F],
        brightness: _(),
        borderColor: [e],
        borderRadius: ["none", "", "full", Lt, F],
        borderSpacing: z(),
        borderWidth: Pe(),
        contrast: _(),
        grayscale: j(),
        hueRotate: _(),
        invert: j(),
        gap: z(),
        gradientColorStops: [e],
        gradientColorStopPositions: [O0, It],
        inset: H(),
        margin: H(),
        opacity: _(),
        padding: z(),
        saturate: _(),
        scale: _(),
        sepia: j(),
        skew: _(),
        space: z(),
        translate: z(),
      },
      classGroups: {
        aspect: [{ aspect: ["auto", "square", "video", F] }],
        container: ["container"],
        columns: [{ columns: [Lt] }],
        "break-after": [{ "break-after": A() }],
        "break-before": [{ "break-before": A() }],
        "break-inside": [
          { "break-inside": ["auto", "avoid", "avoid-page", "avoid-column"] },
        ],
        "box-decoration": [{ "box-decoration": ["slice", "clone"] }],
        box: [{ box: ["border", "content"] }],
        display: [
          "block",
          "inline-block",
          "inline",
          "flex",
          "inline-flex",
          "table",
          "inline-table",
          "table-caption",
          "table-cell",
          "table-column",
          "table-column-group",
          "table-footer-group",
          "table-header-group",
          "table-row-group",
          "table-row",
          "flow-root",
          "grid",
          "inline-grid",
          "contents",
          "list-item",
          "hidden",
        ],
        float: [{ float: ["right", "left", "none", "start", "end"] }],
        clear: [{ clear: ["left", "right", "both", "none", "start", "end"] }],
        isolation: ["isolate", "isolation-auto"],
        "object-fit": [
          { object: ["contain", "cover", "fill", "none", "scale-down"] },
        ],
        "object-position": [{ object: [...ie(), F] }],
        overflow: [{ overflow: D() }],
        "overflow-x": [{ "overflow-x": D() }],
        "overflow-y": [{ "overflow-y": D() }],
        overscroll: [{ overscroll: b() }],
        "overscroll-x": [{ "overscroll-x": b() }],
        "overscroll-y": [{ "overscroll-y": b() }],
        position: ["static", "fixed", "absolute", "relative", "sticky"],
        inset: [{ inset: [g] }],
        "inset-x": [{ "inset-x": [g] }],
        "inset-y": [{ "inset-y": [g] }],
        start: [{ start: [g] }],
        end: [{ end: [g] }],
        top: [{ top: [g] }],
        right: [{ right: [g] }],
        bottom: [{ bottom: [g] }],
        left: [{ left: [g] }],
        visibility: ["visible", "invisible", "collapse"],
        z: [{ z: ["auto", jr, F] }],
        basis: [{ basis: H() }],
        "flex-direction": [
          { flex: ["row", "row-reverse", "col", "col-reverse"] },
        ],
        "flex-wrap": [{ flex: ["wrap", "wrap-reverse", "nowrap"] }],
        flex: [{ flex: ["1", "auto", "initial", "none", F] }],
        grow: [{ grow: j() }],
        shrink: [{ shrink: j() }],
        order: [{ order: ["first", "last", "none", jr, F] }],
        "grid-cols": [{ "grid-cols": [Dr] }],
        "col-start-end": [{ col: ["auto", { span: ["full", jr, F] }, F] }],
        "col-start": [{ "col-start": L() }],
        "col-end": [{ "col-end": L() }],
        "grid-rows": [{ "grid-rows": [Dr] }],
        "row-start-end": [{ row: ["auto", { span: [jr, F] }, F] }],
        "row-start": [{ "row-start": L() }],
        "row-end": [{ "row-end": L() }],
        "grid-flow": [
          { "grid-flow": ["row", "col", "dense", "row-dense", "col-dense"] },
        ],
        "auto-cols": [{ "auto-cols": ["auto", "min", "max", "fr", F] }],
        "auto-rows": [{ "auto-rows": ["auto", "min", "max", "fr", F] }],
        gap: [{ gap: [m] }],
        "gap-x": [{ "gap-x": [m] }],
        "gap-y": [{ "gap-y": [m] }],
        "justify-content": [{ justify: ["normal", ...M()] }],
        "justify-items": [
          { "justify-items": ["start", "end", "center", "stretch"] },
        ],
        "justify-self": [
          { "justify-self": ["auto", "start", "end", "center", "stretch"] },
        ],
        "align-content": [{ content: ["normal", ...M(), "baseline"] }],
        "align-items": [
          { items: ["start", "end", "center", "baseline", "stretch"] },
        ],
        "align-self": [
          { self: ["auto", "start", "end", "center", "stretch", "baseline"] },
        ],
        "place-content": [{ "place-content": [...M(), "baseline"] }],
        "place-items": [
          { "place-items": ["start", "end", "center", "baseline", "stretch"] },
        ],
        "place-self": [
          { "place-self": ["auto", "start", "end", "center", "stretch"] },
        ],
        p: [{ p: [c] }],
        px: [{ px: [c] }],
        py: [{ py: [c] }],
        ps: [{ ps: [c] }],
        pe: [{ pe: [c] }],
        pt: [{ pt: [c] }],
        pr: [{ pr: [c] }],
        pb: [{ pb: [c] }],
        pl: [{ pl: [c] }],
        m: [{ m: [w] }],
        mx: [{ mx: [w] }],
        my: [{ my: [w] }],
        ms: [{ ms: [w] }],
        me: [{ me: [w] }],
        mt: [{ mt: [w] }],
        mr: [{ mr: [w] }],
        mb: [{ mb: [w] }],
        ml: [{ ml: [w] }],
        "space-x": [{ "space-x": [k] }],
        "space-x-reverse": ["space-x-reverse"],
        "space-y": [{ "space-y": [k] }],
        "space-y-reverse": ["space-y-reverse"],
        w: [{ w: ["auto", "min", "max", "fit", "svw", "lvw", "dvw", F, t] }],
        "min-w": [{ "min-w": [F, t, "min", "max", "fit"] }],
        "max-w": [
          {
            "max-w": [
              F,
              t,
              "none",
              "full",
              "min",
              "max",
              "fit",
              "prose",
              { screen: [Lt] },
              Lt,
            ],
          },
        ],
        h: [{ h: [F, t, "auto", "min", "max", "fit", "svh", "lvh", "dvh"] }],
        "min-h": [
          { "min-h": [F, t, "min", "max", "fit", "svh", "lvh", "dvh"] },
        ],
        "max-h": [
          { "max-h": [F, t, "min", "max", "fit", "svh", "lvh", "dvh"] },
        ],
        size: [{ size: [F, t, "auto", "min", "max", "fit"] }],
        "font-size": [{ text: ["base", Lt, It] }],
        "font-smoothing": ["antialiased", "subpixel-antialiased"],
        "font-style": ["italic", "not-italic"],
        "font-weight": [
          {
            font: [
              "thin",
              "extralight",
              "light",
              "normal",
              "medium",
              "semibold",
              "bold",
              "extrabold",
              "black",
              As,
            ],
          },
        ],
        "font-family": [{ font: [Dr] }],
        "fvn-normal": ["normal-nums"],
        "fvn-ordinal": ["ordinal"],
        "fvn-slashed-zero": ["slashed-zero"],
        "fvn-figure": ["lining-nums", "oldstyle-nums"],
        "fvn-spacing": ["proportional-nums", "tabular-nums"],
        "fvn-fraction": ["diagonal-fractions", "stacked-fractons"],
        tracking: [
          {
            tracking: [
              "tighter",
              "tight",
              "normal",
              "wide",
              "wider",
              "widest",
              F,
            ],
          },
        ],
        "line-clamp": [{ "line-clamp": ["none", er, As] }],
        leading: [
          {
            leading: [
              "none",
              "tight",
              "snug",
              "normal",
              "relaxed",
              "loose",
              Et,
              F,
            ],
          },
        ],
        "list-image": [{ "list-image": ["none", F] }],
        "list-style-type": [{ list: ["none", "disc", "decimal", F] }],
        "list-style-position": [{ list: ["inside", "outside"] }],
        "placeholder-color": [{ placeholder: [e] }],
        "placeholder-opacity": [{ "placeholder-opacity": [d] }],
        "text-alignment": [
          { text: ["left", "center", "right", "justify", "start", "end"] },
        ],
        "text-color": [{ text: [e] }],
        "text-opacity": [{ "text-opacity": [d] }],
        "text-decoration": [
          "underline",
          "overline",
          "line-through",
          "no-underline",
        ],
        "text-decoration-style": [{ decoration: [...le(), "wavy"] }],
        "text-decoration-thickness": [
          { decoration: ["auto", "from-font", Et, It] },
        ],
        "underline-offset": [{ "underline-offset": ["auto", Et, F] }],
        "text-decoration-color": [{ decoration: [e] }],
        "text-transform": [
          "uppercase",
          "lowercase",
          "capitalize",
          "normal-case",
        ],
        "text-overflow": ["truncate", "text-ellipsis", "text-clip"],
        "text-wrap": [{ text: ["wrap", "nowrap", "balance", "pretty"] }],
        indent: [{ indent: z() }],
        "vertical-align": [
          {
            align: [
              "baseline",
              "top",
              "middle",
              "bottom",
              "text-top",
              "text-bottom",
              "sub",
              "super",
              F,
            ],
          },
        ],
        whitespace: [
          {
            whitespace: [
              "normal",
              "nowrap",
              "pre",
              "pre-line",
              "pre-wrap",
              "break-spaces",
            ],
          },
        ],
        break: [{ break: ["normal", "words", "all", "keep"] }],
        hyphens: [{ hyphens: ["none", "manual", "auto"] }],
        content: [{ content: ["none", F] }],
        "bg-attachment": [{ bg: ["fixed", "local", "scroll"] }],
        "bg-clip": [{ "bg-clip": ["border", "padding", "content", "text"] }],
        "bg-opacity": [{ "bg-opacity": [d] }],
        "bg-origin": [{ "bg-origin": ["border", "padding", "content"] }],
        "bg-position": [{ bg: [...ie(), b0] }],
        "bg-repeat": [
          { bg: ["no-repeat", { repeat: ["", "x", "y", "round", "space"] }] },
        ],
        "bg-size": [{ bg: ["auto", "cover", "contain", D0] }],
        "bg-image": [
          {
            bg: [
              "none",
              { "gradient-to": ["t", "tr", "r", "br", "b", "bl", "l", "tl"] },
              L0,
            ],
          },
        ],
        "bg-color": [{ bg: [e] }],
        "gradient-from-pos": [{ from: [E] }],
        "gradient-via-pos": [{ via: [E] }],
        "gradient-to-pos": [{ to: [E] }],
        "gradient-from": [{ from: [x] }],
        "gradient-via": [{ via: [x] }],
        "gradient-to": [{ to: [x] }],
        rounded: [{ rounded: [i] }],
        "rounded-s": [{ "rounded-s": [i] }],
        "rounded-e": [{ "rounded-e": [i] }],
        "rounded-t": [{ "rounded-t": [i] }],
        "rounded-r": [{ "rounded-r": [i] }],
        "rounded-b": [{ "rounded-b": [i] }],
        "rounded-l": [{ "rounded-l": [i] }],
        "rounded-ss": [{ "rounded-ss": [i] }],
        "rounded-se": [{ "rounded-se": [i] }],
        "rounded-ee": [{ "rounded-ee": [i] }],
        "rounded-es": [{ "rounded-es": [i] }],
        "rounded-tl": [{ "rounded-tl": [i] }],
        "rounded-tr": [{ "rounded-tr": [i] }],
        "rounded-br": [{ "rounded-br": [i] }],
        "rounded-bl": [{ "rounded-bl": [i] }],
        "border-w": [{ border: [l] }],
        "border-w-x": [{ "border-x": [l] }],
        "border-w-y": [{ "border-y": [l] }],
        "border-w-s": [{ "border-s": [l] }],
        "border-w-e": [{ "border-e": [l] }],
        "border-w-t": [{ "border-t": [l] }],
        "border-w-r": [{ "border-r": [l] }],
        "border-w-b": [{ "border-b": [l] }],
        "border-w-l": [{ "border-l": [l] }],
        "border-opacity": [{ "border-opacity": [d] }],
        "border-style": [{ border: [...le(), "hidden"] }],
        "divide-x": [{ "divide-x": [l] }],
        "divide-x-reverse": ["divide-x-reverse"],
        "divide-y": [{ "divide-y": [l] }],
        "divide-y-reverse": ["divide-y-reverse"],
        "divide-opacity": [{ "divide-opacity": [d] }],
        "divide-style": [{ divide: le() }],
        "border-color": [{ border: [o] }],
        "border-color-x": [{ "border-x": [o] }],
        "border-color-y": [{ "border-y": [o] }],
        "border-color-s": [{ "border-s": [o] }],
        "border-color-e": [{ "border-e": [o] }],
        "border-color-t": [{ "border-t": [o] }],
        "border-color-r": [{ "border-r": [o] }],
        "border-color-b": [{ "border-b": [o] }],
        "border-color-l": [{ "border-l": [o] }],
        "divide-color": [{ divide: [o] }],
        "outline-style": [{ outline: ["", ...le()] }],
        "outline-offset": [{ "outline-offset": [Et, F] }],
        "outline-w": [{ outline: [Et, It] }],
        "outline-color": [{ outline: [e] }],
        "ring-w": [{ ring: Pe() }],
        "ring-w-inset": ["ring-inset"],
        "ring-color": [{ ring: [e] }],
        "ring-opacity": [{ "ring-opacity": [d] }],
        "ring-offset-w": [{ "ring-offset": [Et, It] }],
        "ring-offset-color": [{ "ring-offset": [e] }],
        shadow: [{ shadow: ["", "inner", "none", Lt, A0] }],
        "shadow-color": [{ shadow: [Dr] }],
        opacity: [{ opacity: [d] }],
        "mix-blend": [
          { "mix-blend": [...je(), "plus-lighter", "plus-darker"] },
        ],
        "bg-blend": [{ "bg-blend": je() }],
        filter: [{ filter: ["", "none"] }],
        blur: [{ blur: [n] }],
        brightness: [{ brightness: [r] }],
        contrast: [{ contrast: [a] }],
        "drop-shadow": [{ "drop-shadow": ["", "none", Lt, F] }],
        grayscale: [{ grayscale: [u] }],
        "hue-rotate": [{ "hue-rotate": [v] }],
        invert: [{ invert: [f] }],
        saturate: [{ saturate: [p] }],
        sepia: [{ sepia: [C] }],
        "backdrop-filter": [{ "backdrop-filter": ["", "none"] }],
        "backdrop-blur": [{ "backdrop-blur": [n] }],
        "backdrop-brightness": [{ "backdrop-brightness": [r] }],
        "backdrop-contrast": [{ "backdrop-contrast": [a] }],
        "backdrop-grayscale": [{ "backdrop-grayscale": [u] }],
        "backdrop-hue-rotate": [{ "backdrop-hue-rotate": [v] }],
        "backdrop-invert": [{ "backdrop-invert": [f] }],
        "backdrop-opacity": [{ "backdrop-opacity": [d] }],
        "backdrop-saturate": [{ "backdrop-saturate": [p] }],
        "backdrop-sepia": [{ "backdrop-sepia": [C] }],
        "border-collapse": [{ border: ["collapse", "separate"] }],
        "border-spacing": [{ "border-spacing": [s] }],
        "border-spacing-x": [{ "border-spacing-x": [s] }],
        "border-spacing-y": [{ "border-spacing-y": [s] }],
        "table-layout": [{ table: ["auto", "fixed"] }],
        caption: [{ caption: ["top", "bottom"] }],
        transition: [
          {
            transition: [
              "none",
              "all",
              "",
              "colors",
              "opacity",
              "shadow",
              "transform",
              F,
            ],
          },
        ],
        duration: [{ duration: _() }],
        ease: [{ ease: ["linear", "in", "out", "in-out", F] }],
        delay: [{ delay: _() }],
        animate: [{ animate: ["none", "spin", "ping", "pulse", "bounce", F] }],
        transform: [{ transform: ["", "gpu", "none"] }],
        scale: [{ scale: [S] }],
        "scale-x": [{ "scale-x": [S] }],
        "scale-y": [{ "scale-y": [S] }],
        rotate: [{ rotate: [jr, F] }],
        "translate-x": [{ "translate-x": [T] }],
        "translate-y": [{ "translate-y": [T] }],
        "skew-x": [{ "skew-x": [N] }],
        "skew-y": [{ "skew-y": [N] }],
        "transform-origin": [
          {
            origin: [
              "center",
              "top",
              "top-right",
              "right",
              "bottom-right",
              "bottom",
              "bottom-left",
              "left",
              "top-left",
              F,
            ],
          },
        ],
        accent: [{ accent: ["auto", e] }],
        appearance: [{ appearance: ["none", "auto"] }],
        cursor: [
          {
            cursor: [
              "auto",
              "default",
              "pointer",
              "wait",
              "text",
              "move",
              "help",
              "not-allowed",
              "none",
              "context-menu",
              "progress",
              "cell",
              "crosshair",
              "vertical-text",
              "alias",
              "copy",
              "no-drop",
              "grab",
              "grabbing",
              "all-scroll",
              "col-resize",
              "row-resize",
              "n-resize",
              "e-resize",
              "s-resize",
              "w-resize",
              "ne-resize",
              "nw-resize",
              "se-resize",
              "sw-resize",
              "ew-resize",
              "ns-resize",
              "nesw-resize",
              "nwse-resize",
              "zoom-in",
              "zoom-out",
              F,
            ],
          },
        ],
        "caret-color": [{ caret: [e] }],
        "pointer-events": [{ "pointer-events": ["none", "auto"] }],
        resize: [{ resize: ["none", "y", "x", ""] }],
        "scroll-behavior": [{ scroll: ["auto", "smooth"] }],
        "scroll-m": [{ "scroll-m": z() }],
        "scroll-mx": [{ "scroll-mx": z() }],
        "scroll-my": [{ "scroll-my": z() }],
        "scroll-ms": [{ "scroll-ms": z() }],
        "scroll-me": [{ "scroll-me": z() }],
        "scroll-mt": [{ "scroll-mt": z() }],
        "scroll-mr": [{ "scroll-mr": z() }],
        "scroll-mb": [{ "scroll-mb": z() }],
        "scroll-ml": [{ "scroll-ml": z() }],
        "scroll-p": [{ "scroll-p": z() }],
        "scroll-px": [{ "scroll-px": z() }],
        "scroll-py": [{ "scroll-py": z() }],
        "scroll-ps": [{ "scroll-ps": z() }],
        "scroll-pe": [{ "scroll-pe": z() }],
        "scroll-pt": [{ "scroll-pt": z() }],
        "scroll-pr": [{ "scroll-pr": z() }],
        "scroll-pb": [{ "scroll-pb": z() }],
        "scroll-pl": [{ "scroll-pl": z() }],
        "snap-align": [{ snap: ["start", "end", "center", "align-none"] }],
        "snap-stop": [{ snap: ["normal", "always"] }],
        "snap-type": [{ snap: ["none", "x", "y", "both"] }],
        "snap-strictness": [{ snap: ["mandatory", "proximity"] }],
        touch: [{ touch: ["auto", "none", "manipulation"] }],
        "touch-x": [{ "touch-pan": ["x", "left", "right"] }],
        "touch-y": [{ "touch-pan": ["y", "up", "down"] }],
        "touch-pz": ["touch-pinch-zoom"],
        select: [{ select: ["none", "text", "all", "auto"] }],
        "will-change": [
          { "will-change": ["auto", "scroll", "contents", "transform", F] },
        ],
        fill: [{ fill: [e, "none"] }],
        "stroke-w": [{ stroke: [Et, It, As] }],
        stroke: [{ stroke: [e, "none"] }],
        sr: ["sr-only", "not-sr-only"],
        "forced-color-adjust": [{ "forced-color-adjust": ["auto", "none"] }],
      },
      conflictingClassGroups: {
        overflow: ["overflow-x", "overflow-y"],
        overscroll: ["overscroll-x", "overscroll-y"],
        inset: [
          "inset-x",
          "inset-y",
          "start",
          "end",
          "top",
          "right",
          "bottom",
          "left",
        ],
        "inset-x": ["right", "left"],
        "inset-y": ["top", "bottom"],
        flex: ["basis", "grow", "shrink"],
        gap: ["gap-x", "gap-y"],
        p: ["px", "py", "ps", "pe", "pt", "pr", "pb", "pl"],
        px: ["pr", "pl"],
        py: ["pt", "pb"],
        m: ["mx", "my", "ms", "me", "mt", "mr", "mb", "ml"],
        mx: ["mr", "ml"],
        my: ["mt", "mb"],
        size: ["w", "h"],
        "font-size": ["leading"],
        "fvn-normal": [
          "fvn-ordinal",
          "fvn-slashed-zero",
          "fvn-figure",
          "fvn-spacing",
          "fvn-fraction",
        ],
        "fvn-ordinal": ["fvn-normal"],
        "fvn-slashed-zero": ["fvn-normal"],
        "fvn-figure": ["fvn-normal"],
        "fvn-spacing": ["fvn-normal"],
        "fvn-fraction": ["fvn-normal"],
        "line-clamp": ["display", "overflow"],
        rounded: [
          "rounded-s",
          "rounded-e",
          "rounded-t",
          "rounded-r",
          "rounded-b",
          "rounded-l",
          "rounded-ss",
          "rounded-se",
          "rounded-ee",
          "rounded-es",
          "rounded-tl",
          "rounded-tr",
          "rounded-br",
          "rounded-bl",
        ],
        "rounded-s": ["rounded-ss", "rounded-es"],
        "rounded-e": ["rounded-se", "rounded-ee"],
        "rounded-t": ["rounded-tl", "rounded-tr"],
        "rounded-r": ["rounded-tr", "rounded-br"],
        "rounded-b": ["rounded-br", "rounded-bl"],
        "rounded-l": ["rounded-tl", "rounded-bl"],
        "border-spacing": ["border-spacing-x", "border-spacing-y"],
        "border-w": [
          "border-w-s",
          "border-w-e",
          "border-w-t",
          "border-w-r",
          "border-w-b",
          "border-w-l",
        ],
        "border-w-x": ["border-w-r", "border-w-l"],
        "border-w-y": ["border-w-t", "border-w-b"],
        "border-color": [
          "border-color-s",
          "border-color-e",
          "border-color-t",
          "border-color-r",
          "border-color-b",
          "border-color-l",
        ],
        "border-color-x": ["border-color-r", "border-color-l"],
        "border-color-y": ["border-color-t", "border-color-b"],
        "scroll-m": [
          "scroll-mx",
          "scroll-my",
          "scroll-ms",
          "scroll-me",
          "scroll-mt",
          "scroll-mr",
          "scroll-mb",
          "scroll-ml",
        ],
        "scroll-mx": ["scroll-mr", "scroll-ml"],
        "scroll-my": ["scroll-mt", "scroll-mb"],
        "scroll-p": [
          "scroll-px",
          "scroll-py",
          "scroll-ps",
          "scroll-pe",
          "scroll-pt",
          "scroll-pr",
          "scroll-pb",
          "scroll-pl",
        ],
        "scroll-px": ["scroll-pr", "scroll-pl"],
        "scroll-py": ["scroll-pt", "scroll-pb"],
        touch: ["touch-x", "touch-y", "touch-pz"],
        "touch-x": ["touch"],
        "touch-y": ["touch"],
        "touch-pz": ["touch"],
      },
      conflictingClassGroupModifiers: { "font-size": ["leading"] },
    };
  },
  W0 = C0(U0);
function ce(...e) {
  return W0(c0(e));
}
const V0 = Zy,
  lh = h.forwardRef(({ className: e, ...t }, n) =>
    y.jsx(Hp, {
      ref: n,
      className: ce(
        "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
        e,
      ),
      ...t,
    }),
  );
lh.displayName = Hp.displayName;
const B0 = Zp(
    "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
    {
      variants: {
        variant: {
          default: "border bg-background text-foreground",
          destructive:
            "destructive group border-destructive bg-destructive text-destructive-foreground",
        },
      },
      defaultVariants: { variant: "default" },
    },
  ),
  ah = h.forwardRef(({ className: e, variant: t, ...n }, r) =>
    y.jsx(Qp, { ref: r, className: ce(B0({ variant: t }), e), ...n }),
  );
ah.displayName = Qp.displayName;
const H0 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(Xp, {
    ref: n,
    className: ce(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      e,
    ),
    ...t,
  }),
);
H0.displayName = Xp.displayName;
const uh = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(Yp, {
    ref: n,
    className: ce(
      "absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      e,
    ),
    "toast-close": "",
    ...t,
    children: y.jsx(eh, { className: "h-4 w-4" }),
  }),
);
uh.displayName = Yp.displayName;
const ch = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(Kp, { ref: n, className: ce("text-sm font-semibold", e), ...t }),
);
ch.displayName = Kp.displayName;
const dh = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(Gp, { ref: n, className: ce("text-sm opacity-90", e), ...t }),
);
dh.displayName = Gp.displayName;
function Q0() {
  const { toasts: e } = ay();
  return y.jsxs(V0, {
    children: [
      e.map(function ({ id: t, title: n, description: r, action: o, ...i }) {
        return y.jsxs(
          ah,
          {
            ...i,
            children: [
              y.jsxs("div", {
                className: "grid gap-1",
                children: [
                  n && y.jsx(ch, { children: n }),
                  r && y.jsx(dh, { children: r }),
                ],
              }),
              o,
              y.jsx(uh, {}),
            ],
          },
          t,
        );
      }),
      y.jsx(lh, {}),
    ],
  });
}
function K0(e, t) {
  if (e instanceof RegExp) return { keys: !1, pattern: e };
  var n,
    r,
    o,
    i,
    s = [],
    l = "",
    a = e.split("/");
  for (a[0] || a.shift(); (o = a.shift()); )
    ((n = o[0]),
      n === "*"
        ? (s.push(n), (l += o[1] === "?" ? "(?:/(.*))?" : "/(.*)"))
        : n === ":"
          ? ((r = o.indexOf("?", 1)),
            (i = o.indexOf(".", 1)),
            s.push(o.substring(1, ~r ? r : ~i ? i : o.length)),
            (l += ~r && !~i ? "(?:/([^/]+?))?" : "/([^/]+?)"),
            ~i && (l += (~r ? "?" : "") + "\\" + o.substring(i)))
          : (l += "/" + o));
  return {
    keys: s,
    pattern: new RegExp("^" + l + (t ? "(?=$|/)" : "/?$"), "i"),
  };
}
var fh = { exports: {} },
  ph = {};
/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var wr = h;
function G0(e, t) {
  return (e === t && (e !== 0 || 1 / e === 1 / t)) || (e !== e && t !== t);
}
var X0 = typeof Object.is == "function" ? Object.is : G0,
  Y0 = wr.useState,
  q0 = wr.useEffect,
  Z0 = wr.useLayoutEffect,
  J0 = wr.useDebugValue;
function e1(e, t) {
  var n = t(),
    r = Y0({ inst: { value: n, getSnapshot: t } }),
    o = r[0].inst,
    i = r[1];
  return (
    Z0(
      function () {
        ((o.value = n), (o.getSnapshot = t), Fs(o) && i({ inst: o }));
      },
      [e, n, t],
    ),
    q0(
      function () {
        return (
          Fs(o) && i({ inst: o }),
          e(function () {
            Fs(o) && i({ inst: o });
          })
        );
      },
      [e],
    ),
    J0(n),
    n
  );
}
function Fs(e) {
  var t = e.getSnapshot;
  e = e.value;
  try {
    var n = t();
    return !X0(e, n);
  } catch {
    return !0;
  }
}
function t1(e, t) {
  return t();
}
var n1 =
  typeof window > "u" ||
  typeof window.document > "u" ||
  typeof window.document.createElement > "u"
    ? t1
    : e1;
ph.useSyncExternalStore =
  wr.useSyncExternalStore !== void 0 ? wr.useSyncExternalStore : n1;
fh.exports = ph;
var r1 = fh.exports;
const o1 = pd.useInsertionEffect,
  i1 =
    typeof window < "u" &&
    typeof window.document < "u" &&
    typeof window.document.createElement < "u",
  s1 = i1 ? h.useLayoutEffect : h.useEffect,
  l1 = o1 || s1,
  hh = (e) => {
    const t = h.useRef([e, (...n) => t[0](...n)]).current;
    return (
      l1(() => {
        t[0] = e;
      }),
      t[1]
    );
  },
  a1 = "popstate",
  Ya = "pushState",
  qa = "replaceState",
  u1 = "hashchange",
  Dc = [a1, Ya, qa, u1],
  c1 = (e) => {
    for (const t of Dc) addEventListener(t, e);
    return () => {
      for (const t of Dc) removeEventListener(t, e);
    };
  },
  mh = (e, t) => r1.useSyncExternalStore(c1, e, t),
  d1 = () => location.search,
  f1 = ({ ssrSearch: e = "" } = {}) => mh(d1, () => e),
  bc = () => location.pathname,
  p1 = ({ ssrPath: e } = {}) => mh(bc, e ? () => e : bc),
  h1 = (e, { replace: t = !1, state: n = null } = {}) =>
    history[t ? qa : Ya](n, "", e),
  m1 = (e = {}) => [p1(e), h1],
  Ic = Symbol.for("wouter_v3");
if (typeof history < "u" && typeof window[Ic] > "u") {
  for (const e of [Ya, qa]) {
    const t = history[e];
    history[e] = function () {
      const n = t.apply(this, arguments),
        r = new Event(e);
      return ((r.arguments = arguments), dispatchEvent(r), n);
    };
  }
  Object.defineProperty(window, Ic, { value: !0 });
}
const v1 = (e, t) =>
    t.toLowerCase().indexOf(e.toLowerCase())
      ? "~" + t
      : t.slice(e.length) || "/",
  vh = (e = "") => (e === "/" ? "" : e),
  g1 = (e, t) => (e[0] === "~" ? e.slice(1) : vh(t) + e),
  y1 = (e = "", t) => v1(Lc(vh(e)), Lc(t)),
  Lc = (e) => {
    try {
      return decodeURI(e);
    } catch {
      return e;
    }
  },
  gh = {
    hook: m1,
    searchHook: f1,
    parser: K0,
    base: "",
    ssrPath: void 0,
    ssrSearch: void 0,
    hrefs: (e) => e,
  },
  yh = h.createContext(gh),
  os = () => h.useContext(yh),
  wh = {},
  xh = h.createContext(wh),
  w1 = () => h.useContext(xh),
  Za = (e) => {
    const [t, n] = e.hook(e);
    return [y1(e.base, t), hh((r, o) => n(g1(r, e.base), o))];
  },
  Sh = (e, t, n, r) => {
    const { pattern: o, keys: i } =
        t instanceof RegExp ? { keys: !1, pattern: t } : e(t || "*", r),
      s = o.exec(n) || [],
      [l, ...a] = s;
    return l !== void 0
      ? [
          !0,
          (() => {
            const u =
              i !== !1
                ? Object.fromEntries(i.map((f, m) => [f, a[m]]))
                : s.groups;
            let v = { ...a };
            return (u && Object.assign(v, u), v);
          })(),
          ...(r ? [l] : []),
        ]
      : [!1, null];
  },
  x1 = ({ children: e, ...t }) => {
    var v, f;
    const n = os(),
      r = t.hook ? gh : n;
    let o = r;
    const [i, s] = ((v = t.ssrPath) == null ? void 0 : v.split("?")) ?? [];
    (s && ((t.ssrSearch = s), (t.ssrPath = i)),
      (t.hrefs = t.hrefs ?? ((f = t.hook) == null ? void 0 : f.hrefs)));
    let l = h.useRef({}),
      a = l.current,
      u = a;
    for (let m in r) {
      const x = m === "base" ? r[m] + (t[m] || "") : t[m] || r[m];
      (a === u && x !== u[m] && (l.current = u = { ...u }),
        (u[m] = x),
        x !== r[m] && (o = u));
    }
    return h.createElement(yh.Provider, { value: o, children: e });
  },
  Ac = ({ children: e, component: t }, n) =>
    t ? h.createElement(t, { params: n }) : typeof e == "function" ? e(n) : e,
  S1 = (e) => {
    let t = h.useRef(wh),
      n = t.current;
    for (const r in e) e[r] !== n[r] && (n = e);
    return (Object.keys(e).length === 0 && (n = e), (t.current = n));
  },
  E1 = ({ path: e, nest: t, match: n, ...r }) => {
    const o = os(),
      [i] = Za(o),
      [s, l, a] = n ?? Sh(o.parser, e, i, t),
      u = S1({ ...w1(), ...l });
    if (!s) return null;
    const v = a ? h.createElement(x1, { base: a }, Ac(r, u)) : Ac(r, u);
    return h.createElement(xh.Provider, { value: u, children: v });
  };
h.forwardRef((e, t) => {
  const n = os(),
    [r, o] = Za(n),
    {
      to: i = "",
      href: s = i,
      onClick: l,
      asChild: a,
      children: u,
      className: v,
      replace: f,
      state: m,
      ...x
    } = e,
    E = hh((w) => {
      w.ctrlKey ||
        w.metaKey ||
        w.altKey ||
        w.shiftKey ||
        w.button !== 0 ||
        (l == null || l(w),
        w.defaultPrevented || (w.preventDefault(), o(s, e)));
    }),
    g = n.hrefs(s[0] === "~" ? s.slice(1) : n.base + s, n);
  return a && h.isValidElement(u)
    ? h.cloneElement(u, { onClick: E, href: g })
    : h.createElement("a", {
        ...x,
        onClick: E,
        href: g,
        className: v != null && v.call ? v(r === s) : v,
        children: u,
        ref: t,
      });
});
const Eh = (e) =>
    Array.isArray(e)
      ? e.flatMap((t) => Eh(t && t.type === h.Fragment ? t.props.children : t))
      : [e],
  C1 = ({ children: e, location: t }) => {
    const n = os(),
      [r] = Za(n);
    for (const o of Eh(e)) {
      let i = 0;
      if (
        h.isValidElement(o) &&
        (i = Sh(n.parser, o.props.path, t || r, o.props.nest))[0]
      )
        return h.cloneElement(o, { match: i });
    }
    return null;
  };
function Ch(e = 20) {
  const t = h.useCallback(
      (r) => ({ x: Math.round(r.x / e) * e, y: Math.round(r.y / e) * e }),
      [e],
    ),
    n = h.useCallback(
      (r, o, i) => {
        ((r.strokeStyle = "rgba(255, 255, 255, 0.1)"), (r.lineWidth = 1));
        for (let s = 0; s <= o; s += e)
          (r.beginPath(), r.moveTo(s, 0), r.lineTo(s, i), r.stroke());
        for (let s = 0; s <= i; s += e)
          (r.beginPath(), r.moveTo(0, s), r.lineTo(o, s), r.stroke());
      },
      [e],
    );
  return { snapToGrid: t, drawGrid: n };
}
function k1({ width: e, height: t, gridSize: n = 20 }) {
  const r = h.useRef(null),
    { drawGrid: o } = Ch(n);
  return (
    h.useEffect(() => {
      const i = r.current;
      if (!i) return;
      const s = i.getContext("2d");
      s && (s.clearRect(0, 0, e, t), o(s, e, t));
    }, [e, t, o]),
    y.jsx("canvas", {
      ref: r,
      width: e,
      height: t,
      className: "absolute top-0 left-0 pointer-events-none",
      style: { zIndex: 0 },
    })
  );
}
const Ja = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("div", {
    ref: n,
    className: ce(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      e,
    ),
    ...t,
  }),
);
Ja.displayName = "Card";
const P1 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("div", {
    ref: n,
    className: ce("flex flex-col space-y-1.5 p-6", e),
    ...t,
  }),
);
P1.displayName = "CardHeader";
const N1 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("h3", {
    ref: n,
    className: ce("text-2xl font-semibold leading-none tracking-tight", e),
    ...t,
  }),
);
N1.displayName = "CardTitle";
const T1 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("p", {
    ref: n,
    className: ce("text-sm text-muted-foreground", e),
    ...t,
  }),
);
T1.displayName = "CardDescription";
const R1 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("div", { ref: n, className: ce("p-6 pt-0", e), ...t }),
);
R1.displayName = "CardContent";
const _1 = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx("div", {
    ref: n,
    className: ce("flex items-center p-6 pt-0", e),
    ...t,
  }),
);
_1.displayName = "CardFooter";
const M1 = h.memo(({ server: e, onUpdate: t, isSelected: n }) => {
    const r = (s) => {
        t(e.id, { cores: s });
      },
      o = (s) => {
        t(e.id, { ram: s });
      },
      i = (s) => {
        s.stopPropagation();
        const l = s.clientX,
          a = s.clientY,
          u = e.width || 256,
          v = e.height || 160,
          f = (x) => {
            const E = x.clientX - l,
              g = x.clientY - a,
              w = Math.max(256, Math.round((u + E) / 20) * 20),
              d = Math.max(160, Math.round((v + g) / 20) * 20);
            t(e.id, { width: w, height: d });
          },
          m = () => {
            (window.removeEventListener("mousemove", f),
              window.removeEventListener("mouseup", m));
          };
        (window.addEventListener("mousemove", f),
          window.addEventListener("mouseup", m));
      };
    return y.jsxs(Ja, {
      className:
        "bg-card select-none cursor-move flex flex-col transform transition-transform duration-150 hover:scale-[1.01]",
      style: {
        position: "absolute",
        left: e.position.x,
        top: e.position.y,
        width: e.width || 256,
        height: e.height || 160,
        padding: "1rem",
        fontFamily: "monospace",
        backgroundColor: e.color || "#1a1a1a",
        borderColor: "#000000",
        borderWidth: "1px",
        boxShadow: n
          ? "0 0 30px 15px rgba(0, 0, 0, 0.3), 0 0 15px 5px rgba(0, 0, 0, 0.4)"
          : "none",
      },
      children: [
        y.jsxs("div", {
          className: "space-y-2",
          children: [
            y.jsx("div", {
              className: "flex flex-wrap gap-0.5 max-w-[256px]",
              children: Array.from({ length: 8 }).map((s, l) =>
                y.jsx(
                  "div",
                  {
                    className: `w-3 h-3 rounded-sm transform transition-all duration-150 
                ${l < e.cores ? "bg-blue-500" : "bg-gray-700"} 
                cursor-pointer hover:bg-blue-400 hover:scale-110 active:scale-95`,
                    onClick: (a) => {
                      (a.stopPropagation(), r(l + 1));
                    },
                  },
                  l,
                ),
              ),
            }),
            y.jsx("div", {
              className: "flex flex-wrap gap-0.5 max-w-[256px]",
              children: Array.from({ length: 32 }).map((s, l) =>
                y.jsx(
                  "div",
                  {
                    className: `w-3 h-3 rounded-sm transform transition-all duration-150
                ${e.ram > l ? "bg-green-500" : "bg-gray-700"}
                cursor-pointer hover:bg-green-400 hover:scale-110 active:scale-95`,
                    onClick: (a) => {
                      (a.stopPropagation(), o(l + 1));
                    },
                  },
                  l,
                ),
              ),
            }),
          ],
        }),
        y.jsx("textarea", {
          value: e.notes,
          onChange: (s) => t(e.id, { notes: s.target.value }),
          className:
            "flex-1 w-full mt-2 rounded-md font-mono text-sm resize-none focus:outline-none focus:ring-1 focus:ring-black transition-all duration-150",
          style: {
            backgroundColor: "rgb(20, 20, 20)",
            borderColor: "rgba(55, 65, 81, 0.8)",
            padding: "0.5rem",
            lineHeight: "1.0",
            outline: "none",
          },
          placeholder: "Notes...",
          onMouseDown: (s) => s.stopPropagation(),
        }),
        n &&
          y.jsx("div", {
            className: `absolute bottom-0 right-0 w-4 h-4 bg-gray-600 rounded-tl cursor-se-resize 
            hover:bg-gray-500 transform transition-all duration-150 hover:scale-125`,
            style: { transform: "translate(50%, 50%)" },
            onMouseDown: i,
          }),
      ],
    });
  }),
  kh = h.memo(({ note: e, onUpdate: t, isSelected: n }) => {
    const r = {
        neutral: { bg: "#4A5568", active: "#718096" },
        low: { bg: "#ECC94B", active: "#F6E05E" },
        medium: { bg: "#ED8936", active: "#F6AD55" },
        high: { bg: "#E53E3E", active: "#FC8181" },
      },
      o = ["neutral", "low", "medium", "high"],
      i = (s) => {
        s.stopPropagation();
        const l = s.clientX,
          a = s.clientY,
          u = e.width || 256,
          v = e.height || 160,
          f = (x) => {
            const E = x.clientX - l,
              g = x.clientY - a,
              w = Math.max(256, Math.round((u + E) / 20) * 20),
              d = Math.max(160, Math.round((v + g) / 20) * 20);
            t(e.id, { width: w, height: d });
          },
          m = () => {
            (window.removeEventListener("mousemove", f),
              window.removeEventListener("mouseup", m));
          };
        (window.addEventListener("mousemove", f),
          window.addEventListener("mouseup", m));
      };
    return y.jsxs(Ja, {
      className:
        "bg-card select-none cursor-move flex flex-col transform transition-transform duration-150 hover:scale-[1.01]",
      style: {
        position: "absolute",
        left: e.position.x,
        top: e.position.y,
        width: e.width || 256,
        height: e.height || 160,
        padding: "1rem",
        fontFamily: "monospace",
        backgroundColor: e.color || "#1a1a1a",
        borderColor: "#000000",
        borderWidth: "1px",
        boxShadow: n
          ? "0 0 30px 15px rgba(0, 0, 0, 0.3), 0 0 15px 5px rgba(0, 0, 0, 0.4)"
          : "none",
      },
      children: [
        y.jsx("div", {
          className: "flex justify-end gap-1 mb-2",
          children: o.map((s) =>
            y.jsx(
              "div",
              {
                className:
                  "w-4 h-4 rounded cursor-pointer transition-all duration-150 hover:scale-110 active:scale-95",
                style: {
                  backgroundColor: e.priority === s ? r[s].active : r[s].bg,
                  opacity: e.priority === s ? 1 : 0.15,
                },
                onClick: (l) => {
                  (l.stopPropagation(), t(e.id, { priority: s }));
                },
              },
              s,
            ),
          ),
        }),
        y.jsx("textarea", {
          value: e.notes,
          onChange: (s) => t(e.id, { notes: s.target.value }),
          className:
            "flex-1 w-full rounded-md font-mono text-sm resize-none focus:outline-none focus:ring-1 focus:ring-black transition-all duration-150",
          style: {
            backgroundColor: "rgb(20, 20, 20)",
            borderColor: "rgba(55, 65, 81, 0.8)",
            padding: "0.5rem",
            lineHeight: "1.0",
            outline: "none",
          },
          placeholder: "Notes...",
          onMouseDown: (s) => s.stopPropagation(),
        }),
        n &&
          y.jsx("div", {
            className:
              "absolute bottom-0 right-0 w-4 h-4 bg-gray-600 rounded-tl cursor-se-resize hover:bg-gray-500 transform transition-all duration-150 hover:scale-125",
            style: { transform: "translate(50%, 50%)" },
            onMouseDown: i,
          }),
      ],
    });
  });
kh.displayName = "NotesObject";
const Ii = h.forwardRef(({ className: e, type: t, ...n }, r) =>
  y.jsx("input", {
    type: t,
    className: ce(
      "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
      e,
    ),
    ref: r,
    ...n,
  }),
);
Ii.displayName = "Input";
const O1 = h.memo(
    ({ group: e, onUpdate: t, isSelected: n, onMouseDown: r }) => {
      const o = (s) => {
          if (s.startsWith("#")) {
            const l = parseInt(s.slice(1, 3), 16),
              a = parseInt(s.slice(3, 5), 16),
              u = parseInt(s.slice(5, 7), 16);
            return `rgba(${l}, ${a}, ${u}, 0.3)`;
          }
          return s.replace(/[^,]+(?=\))/, "0.3");
        },
        i = (s) => {
          s.stopPropagation();
          const l = s.clientX,
            a = s.clientY,
            u = e.width,
            v = e.height,
            f = (x) => {
              const E = x.clientX - l,
                g = x.clientY - a,
                w = Math.max(200, Math.round((u + E) / 20) * 20),
                d = Math.max(200, Math.round((v + g) / 20) * 20);
              t(e.id, { width: w, height: d });
            },
            m = () => {
              (window.removeEventListener("mousemove", f),
                window.removeEventListener("mouseup", m));
            };
          (window.addEventListener("mousemove", f),
            window.addEventListener("mouseup", m));
        };
      return y.jsxs("div", {
        className: "absolute select-none",
        style: { position: "absolute", left: e.position.x, top: e.position.y },
        children: [
          y.jsx("div", {
            className: "absolute -top-8 left-0",
            children: y.jsx(Ii, {
              value: e.name,
              onChange: (s) => t(e.id, { name: s.target.value }),
              className:
                "h-6 px-2 py-1 text-sm font-mono border-gray-700 hover:border-gray-600 focus:border-gray-600",
              style: {
                outline: "none",
                boxShadow: "none",
                backgroundColor: "rgba(31, 41, 55, 0.5)",
              },
              onMouseDown: (s) => s.stopPropagation(),
            }),
          }),
          y.jsx("div", {
            className:
              "rounded-lg border-[3px] border-dotted cursor-move transform transition-all duration-150 hover:scale-[1.005]",
            onMouseDown: r,
            style: {
              width: e.width,
              height: e.height,
              backgroundColor: e.color ? o(e.color) : "rgba(74, 85, 104, 0.3)",
              borderColor: e.color || "rgb(74, 85, 104)",
              boxShadow: n
                ? "0 0 30px 15px rgba(0, 0, 0, 0.3), 0 0 15px 5px rgba(0, 0, 0, 0.4)"
                : "none",
            },
            children:
              n &&
              y.jsx("div", {
                className:
                  "absolute bottom-0 right-0 w-4 h-4 bg-gray-600 rounded-tl cursor-se-resize hover:bg-gray-500 transform transition-all duration-150 hover:scale-125",
                style: { transform: "translate(50%, 50%)" },
                onMouseDown: i,
              }),
          }),
        ],
      });
    },
  ),
  Ph = h.memo(({ connection: e, servers: t, isSelected: n, onUpdate: r }) => {
    const o = t.find((c) => c.id === e.sourceId),
      i = t.find((c) => c.id === e.targetId);
    if (!o || !i) return null;
    const s = o.position.x + (o.width || 256) / 2,
      l = o.position.y + (o.height || 160) / 2,
      a = i.position.x + (i.width || 256) / 2,
      u = i.position.y + (i.height || 160) / 2,
      v = a - s,
      f = u - l,
      m = Math.atan2(f, v),
      x = 10,
      E = s + Math.cos(m) * x,
      g = l + Math.sin(m) * x,
      w = a - Math.cos(m) * x,
      d = u - Math.sin(m) * x;
    return y.jsxs("svg", {
      className: "absolute top-0 left-0 w-full h-full pointer-events-none",
      style: { zIndex: 1 },
      children: [
        y.jsx("line", {
          x1: E,
          y1: g,
          x2: w,
          y2: d,
          stroke: e.color || "#4A5568",
          strokeWidth: 2,
          strokeDasharray: n ? "5,5" : "none",
          className: "transition-all duration-150",
        }),
        y.jsx("polygon", {
          points: `${w},${d} ${w - 10 * Math.cos(m - Math.PI / 6)},${d - 10 * Math.sin(m - Math.PI / 6)} ${w - 10 * Math.cos(m + Math.PI / 6)},${d - 10 * Math.sin(m + Math.PI / 6)}`,
          fill: e.color || "#4A5568",
          className: "transition-all duration-150",
        }),
      ],
    });
  });
Ph.displayName = "Connection";
const j1 = Zp(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
    {
      variants: {
        variant: {
          default: "bg-primary text-primary-foreground hover:bg-primary/90",
          destructive:
            "bg-destructive text-destructive-foreground hover:bg-destructive/90",
          outline:
            "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
          secondary:
            "bg-secondary text-secondary-foreground hover:bg-secondary/80",
          ghost: "hover:bg-accent hover:text-accent-foreground",
          link: "text-primary underline-offset-4 hover:underline",
        },
        size: {
          default: "h-10 px-4 py-2",
          sm: "h-9 rounded-md px-3",
          lg: "h-11 rounded-md px-8",
          icon: "h-10 w-10",
        },
      },
      defaultVariants: { variant: "default", size: "default" },
    },
  ),
  Ke = h.forwardRef(
    ({ className: e, variant: t, size: n, asChild: r = !1, ...o }, i) => {
      const s = r ? yr : "button";
      return y.jsx(s, {
        className: ce(j1({ variant: t, size: n, className: e })),
        ref: i,
        ...o,
      });
    },
  );
Ke.displayName = "Button";
var D1 = pd.useId || (() => {}),
  b1 = 0;
function zs(e) {
  const [t, n] = h.useState(D1());
  return (
    ho(() => {
      e || n((r) => r ?? String(b1++));
    }, [e]),
    e || (t ? `radix-${t}` : "")
  );
}
var $s = "focusScope.autoFocusOnMount",
  Us = "focusScope.autoFocusOnUnmount",
  Fc = { bubbles: !1, cancelable: !0 },
  I1 = "FocusScope",
  Nh = h.forwardRef((e, t) => {
    const {
        loop: n = !1,
        trapped: r = !1,
        onMountAutoFocus: o,
        onUnmountAutoFocus: i,
        ...s
      } = e,
      [l, a] = h.useState(null),
      u = ct(o),
      v = ct(i),
      f = h.useRef(null),
      m = ut(t, (g) => a(g)),
      x = h.useRef({
        paused: !1,
        pause() {
          this.paused = !0;
        },
        resume() {
          this.paused = !1;
        },
      }).current;
    (h.useEffect(() => {
      if (r) {
        let g = function (p) {
            if (x.paused || !l) return;
            const S = p.target;
            l.contains(S) ? (f.current = S) : zt(f.current, { select: !0 });
          },
          w = function (p) {
            if (x.paused || !l) return;
            const S = p.relatedTarget;
            S !== null && (l.contains(S) || zt(f.current, { select: !0 }));
          },
          d = function (p) {
            if (document.activeElement === document.body)
              for (const C of p) C.removedNodes.length > 0 && zt(l);
          };
        (document.addEventListener("focusin", g),
          document.addEventListener("focusout", w));
        const c = new MutationObserver(d);
        return (
          l && c.observe(l, { childList: !0, subtree: !0 }),
          () => {
            (document.removeEventListener("focusin", g),
              document.removeEventListener("focusout", w),
              c.disconnect());
          }
        );
      }
    }, [r, l, x.paused]),
      h.useEffect(() => {
        if (l) {
          $c.add(x);
          const g = document.activeElement;
          if (!l.contains(g)) {
            const d = new CustomEvent($s, Fc);
            (l.addEventListener($s, u),
              l.dispatchEvent(d),
              d.defaultPrevented ||
                (L1(U1(Th(l)), { select: !0 }),
                document.activeElement === g && zt(l)));
          }
          return () => {
            (l.removeEventListener($s, u),
              setTimeout(() => {
                const d = new CustomEvent(Us, Fc);
                (l.addEventListener(Us, v),
                  l.dispatchEvent(d),
                  d.defaultPrevented || zt(g ?? document.body, { select: !0 }),
                  l.removeEventListener(Us, v),
                  $c.remove(x));
              }, 0));
          };
        }
      }, [l, u, v, x]));
    const E = h.useCallback(
      (g) => {
        if ((!n && !r) || x.paused) return;
        const w = g.key === "Tab" && !g.altKey && !g.ctrlKey && !g.metaKey,
          d = document.activeElement;
        if (w && d) {
          const c = g.currentTarget,
            [p, S] = A1(c);
          p && S
            ? !g.shiftKey && d === S
              ? (g.preventDefault(), n && zt(p, { select: !0 }))
              : g.shiftKey &&
                d === p &&
                (g.preventDefault(), n && zt(S, { select: !0 }))
            : d === c && g.preventDefault();
        }
      },
      [n, r, x.paused],
    );
    return y.jsx(ke.div, { tabIndex: -1, ...s, ref: m, onKeyDown: E });
  });
Nh.displayName = I1;
function L1(e, { select: t = !1 } = {}) {
  const n = document.activeElement;
  for (const r of e)
    if ((zt(r, { select: t }), document.activeElement !== n)) return;
}
function A1(e) {
  const t = Th(e),
    n = zc(t, e),
    r = zc(t.reverse(), e);
  return [n, r];
}
function Th(e) {
  const t = [],
    n = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
      acceptNode: (r) => {
        const o = r.tagName === "INPUT" && r.type === "hidden";
        return r.disabled || r.hidden || o
          ? NodeFilter.FILTER_SKIP
          : r.tabIndex >= 0
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP;
      },
    });
  for (; n.nextNode(); ) t.push(n.currentNode);
  return t;
}
function zc(e, t) {
  for (const n of e) if (!F1(n, { upTo: t })) return n;
}
function F1(e, { upTo: t }) {
  if (getComputedStyle(e).visibility === "hidden") return !0;
  for (; e; ) {
    if (t !== void 0 && e === t) return !1;
    if (getComputedStyle(e).display === "none") return !0;
    e = e.parentElement;
  }
  return !1;
}
function z1(e) {
  return e instanceof HTMLInputElement && "select" in e;
}
function zt(e, { select: t = !1 } = {}) {
  if (e && e.focus) {
    const n = document.activeElement;
    (e.focus({ preventScroll: !0 }), e !== n && z1(e) && t && e.select());
  }
}
var $c = $1();
function $1() {
  let e = [];
  return {
    add(t) {
      const n = e[0];
      (t !== n && (n == null || n.pause()), (e = Uc(e, t)), e.unshift(t));
    },
    remove(t) {
      var n;
      ((e = Uc(e, t)), (n = e[0]) == null || n.resume());
    },
  };
}
function Uc(e, t) {
  const n = [...e],
    r = n.indexOf(t);
  return (r !== -1 && n.splice(r, 1), n);
}
function U1(e) {
  return e.filter((t) => t.tagName !== "A");
}
var Ws = 0;
function W1() {
  h.useEffect(() => {
    const e = document.querySelectorAll("[data-radix-focus-guard]");
    return (
      document.body.insertAdjacentElement("afterbegin", e[0] ?? Wc()),
      document.body.insertAdjacentElement("beforeend", e[1] ?? Wc()),
      Ws++,
      () => {
        (Ws === 1 &&
          document
            .querySelectorAll("[data-radix-focus-guard]")
            .forEach((t) => t.remove()),
          Ws--);
      }
    );
  }, []);
}
function Wc() {
  const e = document.createElement("span");
  return (
    e.setAttribute("data-radix-focus-guard", ""),
    (e.tabIndex = 0),
    (e.style.outline = "none"),
    (e.style.opacity = "0"),
    (e.style.position = "fixed"),
    (e.style.pointerEvents = "none"),
    e
  );
}
var yt = function () {
  return (
    (yt =
      Object.assign ||
      function (t) {
        for (var n, r = 1, o = arguments.length; r < o; r++) {
          n = arguments[r];
          for (var i in n)
            Object.prototype.hasOwnProperty.call(n, i) && (t[i] = n[i]);
        }
        return t;
      }),
    yt.apply(this, arguments)
  );
};
function Rh(e, t) {
  var n = {};
  for (var r in e)
    Object.prototype.hasOwnProperty.call(e, r) &&
      t.indexOf(r) < 0 &&
      (n[r] = e[r]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var o = 0, r = Object.getOwnPropertySymbols(e); o < r.length; o++)
      t.indexOf(r[o]) < 0 &&
        Object.prototype.propertyIsEnumerable.call(e, r[o]) &&
        (n[r[o]] = e[r[o]]);
  return n;
}
function V1(e, t, n) {
  if (n || arguments.length === 2)
    for (var r = 0, o = t.length, i; r < o; r++)
      (i || !(r in t)) &&
        (i || (i = Array.prototype.slice.call(t, 0, r)), (i[r] = t[r]));
  return e.concat(i || Array.prototype.slice.call(t));
}
var ai = "right-scroll-bar-position",
  ui = "width-before-scroll-bar",
  B1 = "with-scroll-bars-hidden",
  H1 = "--removed-body-scroll-bar-size";
function Vs(e, t) {
  return (typeof e == "function" ? e(t) : e && (e.current = t), e);
}
function Q1(e, t) {
  var n = h.useState(function () {
    return {
      value: e,
      callback: t,
      facade: {
        get current() {
          return n.value;
        },
        set current(r) {
          var o = n.value;
          o !== r && ((n.value = r), n.callback(r, o));
        },
      },
    };
  })[0];
  return ((n.callback = t), n.facade);
}
var K1 = typeof window < "u" ? h.useLayoutEffect : h.useEffect,
  Vc = new WeakMap();
function G1(e, t) {
  var n = Q1(null, function (r) {
    return e.forEach(function (o) {
      return Vs(o, r);
    });
  });
  return (
    K1(
      function () {
        var r = Vc.get(n);
        if (r) {
          var o = new Set(r),
            i = new Set(e),
            s = n.current;
          (o.forEach(function (l) {
            i.has(l) || Vs(l, null);
          }),
            i.forEach(function (l) {
              o.has(l) || Vs(l, s);
            }));
        }
        Vc.set(n, e);
      },
      [e],
    ),
    n
  );
}
function X1(e) {
  return e;
}
function Y1(e, t) {
  t === void 0 && (t = X1);
  var n = [],
    r = !1,
    o = {
      read: function () {
        if (r)
          throw new Error(
            "Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.",
          );
        return n.length ? n[n.length - 1] : e;
      },
      useMedium: function (i) {
        var s = t(i, r);
        return (
          n.push(s),
          function () {
            n = n.filter(function (l) {
              return l !== s;
            });
          }
        );
      },
      assignSyncMedium: function (i) {
        for (r = !0; n.length; ) {
          var s = n;
          ((n = []), s.forEach(i));
        }
        n = {
          push: function (l) {
            return i(l);
          },
          filter: function () {
            return n;
          },
        };
      },
      assignMedium: function (i) {
        r = !0;
        var s = [];
        if (n.length) {
          var l = n;
          ((n = []), l.forEach(i), (s = n));
        }
        var a = function () {
            var v = s;
            ((s = []), v.forEach(i));
          },
          u = function () {
            return Promise.resolve().then(a);
          };
        (u(),
          (n = {
            push: function (v) {
              (s.push(v), u());
            },
            filter: function (v) {
              return ((s = s.filter(v)), n);
            },
          }));
      },
    };
  return o;
}
function q1(e) {
  e === void 0 && (e = {});
  var t = Y1(null);
  return ((t.options = yt({ async: !0, ssr: !1 }, e)), t);
}
var _h = function (e) {
  var t = e.sideCar,
    n = Rh(e, ["sideCar"]);
  if (!t)
    throw new Error(
      "Sidecar: please provide `sideCar` property to import the right car",
    );
  var r = t.read();
  if (!r) throw new Error("Sidecar medium not found");
  return h.createElement(r, yt({}, n));
};
_h.isSideCarExport = !0;
function Z1(e, t) {
  return (e.useMedium(t), _h);
}
var Mh = q1(),
  Bs = function () {},
  is = h.forwardRef(function (e, t) {
    var n = h.useRef(null),
      r = h.useState({
        onScrollCapture: Bs,
        onWheelCapture: Bs,
        onTouchMoveCapture: Bs,
      }),
      o = r[0],
      i = r[1],
      s = e.forwardProps,
      l = e.children,
      a = e.className,
      u = e.removeScrollBar,
      v = e.enabled,
      f = e.shards,
      m = e.sideCar,
      x = e.noIsolation,
      E = e.inert,
      g = e.allowPinchZoom,
      w = e.as,
      d = w === void 0 ? "div" : w,
      c = e.gapMode,
      p = Rh(e, [
        "forwardProps",
        "children",
        "className",
        "removeScrollBar",
        "enabled",
        "shards",
        "sideCar",
        "noIsolation",
        "inert",
        "allowPinchZoom",
        "as",
        "gapMode",
      ]),
      S = m,
      C = G1([n, t]),
      N = yt(yt({}, p), o);
    return h.createElement(
      h.Fragment,
      null,
      v &&
        h.createElement(S, {
          sideCar: Mh,
          removeScrollBar: u,
          shards: f,
          noIsolation: x,
          inert: E,
          setCallbacks: i,
          allowPinchZoom: !!g,
          lockRef: n,
          gapMode: c,
        }),
      s
        ? h.cloneElement(h.Children.only(l), yt(yt({}, N), { ref: C }))
        : h.createElement(d, yt({}, N, { className: a, ref: C }), l),
    );
  });
is.defaultProps = { enabled: !0, removeScrollBar: !0, inert: !1 };
is.classNames = { fullWidth: ui, zeroRight: ai };
var J1 = function () {
  if (typeof __webpack_nonce__ < "u") return __webpack_nonce__;
};
function ew() {
  if (!document) return null;
  var e = document.createElement("style");
  e.type = "text/css";
  var t = J1();
  return (t && e.setAttribute("nonce", t), e);
}
function tw(e, t) {
  e.styleSheet
    ? (e.styleSheet.cssText = t)
    : e.appendChild(document.createTextNode(t));
}
function nw(e) {
  var t = document.head || document.getElementsByTagName("head")[0];
  t.appendChild(e);
}
var rw = function () {
    var e = 0,
      t = null;
    return {
      add: function (n) {
        (e == 0 && (t = ew()) && (tw(t, n), nw(t)), e++);
      },
      remove: function () {
        (e--,
          !e && t && (t.parentNode && t.parentNode.removeChild(t), (t = null)));
      },
    };
  },
  ow = function () {
    var e = rw();
    return function (t, n) {
      h.useEffect(
        function () {
          return (
            e.add(t),
            function () {
              e.remove();
            }
          );
        },
        [t && n],
      );
    };
  },
  Oh = function () {
    var e = ow(),
      t = function (n) {
        var r = n.styles,
          o = n.dynamic;
        return (e(r, o), null);
      };
    return t;
  },
  iw = { left: 0, top: 0, right: 0, gap: 0 },
  Hs = function (e) {
    return parseInt(e || "", 10) || 0;
  },
  sw = function (e) {
    var t = window.getComputedStyle(document.body),
      n = t[e === "padding" ? "paddingLeft" : "marginLeft"],
      r = t[e === "padding" ? "paddingTop" : "marginTop"],
      o = t[e === "padding" ? "paddingRight" : "marginRight"];
    return [Hs(n), Hs(r), Hs(o)];
  },
  lw = function (e) {
    if ((e === void 0 && (e = "margin"), typeof window > "u")) return iw;
    var t = sw(e),
      n = document.documentElement.clientWidth,
      r = window.innerWidth;
    return {
      left: t[0],
      top: t[1],
      right: t[2],
      gap: Math.max(0, r - n + t[2] - t[0]),
    };
  },
  aw = Oh(),
  tr = "data-scroll-locked",
  uw = function (e, t, n, r) {
    var o = e.left,
      i = e.top,
      s = e.right,
      l = e.gap;
    return (
      n === void 0 && (n = "margin"),
      `
  .`
        .concat(
          B1,
          ` {
   overflow: hidden `,
        )
        .concat(
          r,
          `;
   padding-right: `,
        )
        .concat(l, "px ")
        .concat(
          r,
          `;
  }
  body[`,
        )
        .concat(
          tr,
          `] {
    overflow: hidden `,
        )
        .concat(
          r,
          `;
    overscroll-behavior: contain;
    `,
        )
        .concat(
          [
            t && "position: relative ".concat(r, ";"),
            n === "margin" &&
              `
    padding-left: `
                .concat(
                  o,
                  `px;
    padding-top: `,
                )
                .concat(
                  i,
                  `px;
    padding-right: `,
                )
                .concat(
                  s,
                  `px;
    margin-left:0;
    margin-top:0;
    margin-right: `,
                )
                .concat(l, "px ")
                .concat(
                  r,
                  `;
    `,
                ),
            n === "padding" &&
              "padding-right: ".concat(l, "px ").concat(r, ";"),
          ]
            .filter(Boolean)
            .join(""),
          `
  }
  
  .`,
        )
        .concat(
          ai,
          ` {
    right: `,
        )
        .concat(l, "px ")
        .concat(
          r,
          `;
  }
  
  .`,
        )
        .concat(
          ui,
          ` {
    margin-right: `,
        )
        .concat(l, "px ")
        .concat(
          r,
          `;
  }
  
  .`,
        )
        .concat(ai, " .")
        .concat(
          ai,
          ` {
    right: 0 `,
        )
        .concat(
          r,
          `;
  }
  
  .`,
        )
        .concat(ui, " .")
        .concat(
          ui,
          ` {
    margin-right: 0 `,
        )
        .concat(
          r,
          `;
  }
  
  body[`,
        )
        .concat(
          tr,
          `] {
    `,
        )
        .concat(H1, ": ")
        .concat(
          l,
          `px;
  }
`,
        )
    );
  },
  Bc = function () {
    var e = parseInt(document.body.getAttribute(tr) || "0", 10);
    return isFinite(e) ? e : 0;
  },
  cw = function () {
    h.useEffect(function () {
      return (
        document.body.setAttribute(tr, (Bc() + 1).toString()),
        function () {
          var e = Bc() - 1;
          e <= 0
            ? document.body.removeAttribute(tr)
            : document.body.setAttribute(tr, e.toString());
        }
      );
    }, []);
  },
  dw = function (e) {
    var t = e.noRelative,
      n = e.noImportant,
      r = e.gapMode,
      o = r === void 0 ? "margin" : r;
    cw();
    var i = h.useMemo(
      function () {
        return lw(o);
      },
      [o],
    );
    return h.createElement(aw, { styles: uw(i, !t, o, n ? "" : "!important") });
  },
  Ql = !1;
if (typeof window < "u")
  try {
    var Bo = Object.defineProperty({}, "passive", {
      get: function () {
        return ((Ql = !0), !0);
      },
    });
    (window.addEventListener("test", Bo, Bo),
      window.removeEventListener("test", Bo, Bo));
  } catch {
    Ql = !1;
  }
var jn = Ql ? { passive: !1 } : !1,
  fw = function (e) {
    return e.tagName === "TEXTAREA";
  },
  jh = function (e, t) {
    if (!(e instanceof Element)) return !1;
    var n = window.getComputedStyle(e);
    return (
      n[t] !== "hidden" &&
      !(n.overflowY === n.overflowX && !fw(e) && n[t] === "visible")
    );
  },
  pw = function (e) {
    return jh(e, "overflowY");
  },
  hw = function (e) {
    return jh(e, "overflowX");
  },
  Hc = function (e, t) {
    var n = t.ownerDocument,
      r = t;
    do {
      typeof ShadowRoot < "u" && r instanceof ShadowRoot && (r = r.host);
      var o = Dh(e, r);
      if (o) {
        var i = bh(e, r),
          s = i[1],
          l = i[2];
        if (s > l) return !0;
      }
      r = r.parentNode;
    } while (r && r !== n.body);
    return !1;
  },
  mw = function (e) {
    var t = e.scrollTop,
      n = e.scrollHeight,
      r = e.clientHeight;
    return [t, n, r];
  },
  vw = function (e) {
    var t = e.scrollLeft,
      n = e.scrollWidth,
      r = e.clientWidth;
    return [t, n, r];
  },
  Dh = function (e, t) {
    return e === "v" ? pw(t) : hw(t);
  },
  bh = function (e, t) {
    return e === "v" ? mw(t) : vw(t);
  },
  gw = function (e, t) {
    return e === "h" && t === "rtl" ? -1 : 1;
  },
  yw = function (e, t, n, r, o) {
    var i = gw(e, window.getComputedStyle(t).direction),
      s = i * r,
      l = n.target,
      a = t.contains(l),
      u = !1,
      v = s > 0,
      f = 0,
      m = 0;
    do {
      var x = bh(e, l),
        E = x[0],
        g = x[1],
        w = x[2],
        d = g - w - i * E;
      ((E || d) && Dh(e, l) && ((f += d), (m += E)),
        l instanceof ShadowRoot ? (l = l.host) : (l = l.parentNode));
    } while ((!a && l !== document.body) || (a && (t.contains(l) || t === l)));
    return (
      ((v && (Math.abs(f) < 1 || !o)) || (!v && (Math.abs(m) < 1 || !o))) &&
        (u = !0),
      u
    );
  },
  Ho = function (e) {
    return "changedTouches" in e
      ? [e.changedTouches[0].clientX, e.changedTouches[0].clientY]
      : [0, 0];
  },
  Qc = function (e) {
    return [e.deltaX, e.deltaY];
  },
  Kc = function (e) {
    return e && "current" in e ? e.current : e;
  },
  ww = function (e, t) {
    return e[0] === t[0] && e[1] === t[1];
  },
  xw = function (e) {
    return `
  .block-interactivity-`
      .concat(
        e,
        ` {pointer-events: none;}
  .allow-interactivity-`,
      )
      .concat(
        e,
        ` {pointer-events: all;}
`,
      );
  },
  Sw = 0,
  Dn = [];
function Ew(e) {
  var t = h.useRef([]),
    n = h.useRef([0, 0]),
    r = h.useRef(),
    o = h.useState(Sw++)[0],
    i = h.useState(Oh)[0],
    s = h.useRef(e);
  (h.useEffect(
    function () {
      s.current = e;
    },
    [e],
  ),
    h.useEffect(
      function () {
        if (e.inert) {
          document.body.classList.add("block-interactivity-".concat(o));
          var g = V1([e.lockRef.current], (e.shards || []).map(Kc), !0).filter(
            Boolean,
          );
          return (
            g.forEach(function (w) {
              return w.classList.add("allow-interactivity-".concat(o));
            }),
            function () {
              (document.body.classList.remove("block-interactivity-".concat(o)),
                g.forEach(function (w) {
                  return w.classList.remove("allow-interactivity-".concat(o));
                }));
            }
          );
        }
      },
      [e.inert, e.lockRef.current, e.shards],
    ));
  var l = h.useCallback(function (g, w) {
      if (
        ("touches" in g && g.touches.length === 2) ||
        (g.type === "wheel" && g.ctrlKey)
      )
        return !s.current.allowPinchZoom;
      var d = Ho(g),
        c = n.current,
        p = "deltaX" in g ? g.deltaX : c[0] - d[0],
        S = "deltaY" in g ? g.deltaY : c[1] - d[1],
        C,
        N = g.target,
        k = Math.abs(p) > Math.abs(S) ? "h" : "v";
      if ("touches" in g && k === "h" && N.type === "range") return !1;
      var T = Hc(k, N);
      if (!T) return !0;
      if ((T ? (C = k) : ((C = k === "v" ? "h" : "v"), (T = Hc(k, N))), !T))
        return !1;
      if (
        (!r.current && "changedTouches" in g && (p || S) && (r.current = C), !C)
      )
        return !0;
      var b = r.current || C;
      return yw(b, w, g, b === "h" ? p : S, !0);
    }, []),
    a = h.useCallback(function (g) {
      var w = g;
      if (!(!Dn.length || Dn[Dn.length - 1] !== i)) {
        var d = "deltaY" in w ? Qc(w) : Ho(w),
          c = t.current.filter(function (C) {
            return (
              C.name === w.type &&
              (C.target === w.target || w.target === C.shadowParent) &&
              ww(C.delta, d)
            );
          })[0];
        if (c && c.should) {
          w.cancelable && w.preventDefault();
          return;
        }
        if (!c) {
          var p = (s.current.shards || [])
              .map(Kc)
              .filter(Boolean)
              .filter(function (C) {
                return C.contains(w.target);
              }),
            S = p.length > 0 ? l(w, p[0]) : !s.current.noIsolation;
          S && w.cancelable && w.preventDefault();
        }
      }
    }, []),
    u = h.useCallback(function (g, w, d, c) {
      var p = { name: g, delta: w, target: d, should: c, shadowParent: Cw(d) };
      (t.current.push(p),
        setTimeout(function () {
          t.current = t.current.filter(function (S) {
            return S !== p;
          });
        }, 1));
    }, []),
    v = h.useCallback(function (g) {
      ((n.current = Ho(g)), (r.current = void 0));
    }, []),
    f = h.useCallback(function (g) {
      u(g.type, Qc(g), g.target, l(g, e.lockRef.current));
    }, []),
    m = h.useCallback(function (g) {
      u(g.type, Ho(g), g.target, l(g, e.lockRef.current));
    }, []);
  h.useEffect(function () {
    return (
      Dn.push(i),
      e.setCallbacks({
        onScrollCapture: f,
        onWheelCapture: f,
        onTouchMoveCapture: m,
      }),
      document.addEventListener("wheel", a, jn),
      document.addEventListener("touchmove", a, jn),
      document.addEventListener("touchstart", v, jn),
      function () {
        ((Dn = Dn.filter(function (g) {
          return g !== i;
        })),
          document.removeEventListener("wheel", a, jn),
          document.removeEventListener("touchmove", a, jn),
          document.removeEventListener("touchstart", v, jn));
      }
    );
  }, []);
  var x = e.removeScrollBar,
    E = e.inert;
  return h.createElement(
    h.Fragment,
    null,
    E ? h.createElement(i, { styles: xw(o) }) : null,
    x ? h.createElement(dw, { gapMode: e.gapMode }) : null,
  );
}
function Cw(e) {
  for (var t = null; e !== null; )
    (e instanceof ShadowRoot && ((t = e.host), (e = e.host)),
      (e = e.parentNode));
  return t;
}
const kw = Z1(Mh, Ew);
var Ih = h.forwardRef(function (e, t) {
  return h.createElement(is, yt({}, e, { ref: t, sideCar: kw }));
});
Ih.classNames = is.classNames;
var Pw = function (e) {
    if (typeof document > "u") return null;
    var t = Array.isArray(e) ? e[0] : e;
    return t.ownerDocument.body;
  },
  bn = new WeakMap(),
  Qo = new WeakMap(),
  Ko = {},
  Qs = 0,
  Lh = function (e) {
    return e && (e.host || Lh(e.parentNode));
  },
  Nw = function (e, t) {
    return t
      .map(function (n) {
        if (e.contains(n)) return n;
        var r = Lh(n);
        return r && e.contains(r)
          ? r
          : (console.error(
              "aria-hidden",
              n,
              "in not contained inside",
              e,
              ". Doing nothing",
            ),
            null);
      })
      .filter(function (n) {
        return !!n;
      });
  },
  Tw = function (e, t, n, r) {
    var o = Nw(t, Array.isArray(e) ? e : [e]);
    Ko[n] || (Ko[n] = new WeakMap());
    var i = Ko[n],
      s = [],
      l = new Set(),
      a = new Set(o),
      u = function (f) {
        !f || l.has(f) || (l.add(f), u(f.parentNode));
      };
    o.forEach(u);
    var v = function (f) {
      !f ||
        a.has(f) ||
        Array.prototype.forEach.call(f.children, function (m) {
          if (l.has(m)) v(m);
          else
            try {
              var x = m.getAttribute(r),
                E = x !== null && x !== "false",
                g = (bn.get(m) || 0) + 1,
                w = (i.get(m) || 0) + 1;
              (bn.set(m, g),
                i.set(m, w),
                s.push(m),
                g === 1 && E && Qo.set(m, !0),
                w === 1 && m.setAttribute(n, "true"),
                E || m.setAttribute(r, "true"));
            } catch (d) {
              console.error("aria-hidden: cannot operate on ", m, d);
            }
        });
    };
    return (
      v(t),
      l.clear(),
      Qs++,
      function () {
        (s.forEach(function (f) {
          var m = bn.get(f) - 1,
            x = i.get(f) - 1;
          (bn.set(f, m),
            i.set(f, x),
            m || (Qo.has(f) || f.removeAttribute(r), Qo.delete(f)),
            x || f.removeAttribute(n));
        }),
          Qs--,
          Qs ||
            ((bn = new WeakMap()),
            (bn = new WeakMap()),
            (Qo = new WeakMap()),
            (Ko = {})));
      }
    );
  },
  Rw = function (e, t, n) {
    n === void 0 && (n = "data-aria-hidden");
    var r = Array.from(Array.isArray(e) ? e : [e]),
      o = Pw(e);
    return o
      ? (r.push.apply(r, Array.from(o.querySelectorAll("[aria-live]"))),
        Tw(r, o, n, "aria-hidden"))
      : function () {
          return null;
        };
  },
  eu = "Dialog",
  [Ah, Zw] = Np(eu),
  [_w, dt] = Ah(eu),
  Fh = (e) => {
    const {
        __scopeDialog: t,
        children: n,
        open: r,
        defaultOpen: o,
        onOpenChange: i,
        modal: s = !0,
      } = e,
      l = h.useRef(null),
      a = h.useRef(null),
      [u = !1, v] = Op({ prop: r, defaultProp: o, onChange: i });
    return y.jsx(_w, {
      scope: t,
      triggerRef: l,
      contentRef: a,
      contentId: zs(),
      titleId: zs(),
      descriptionId: zs(),
      open: u,
      onOpenChange: v,
      onOpenToggle: h.useCallback(() => v((f) => !f), [v]),
      modal: s,
      children: n,
    });
  };
Fh.displayName = eu;
var zh = "DialogTrigger",
  Mw = h.forwardRef((e, t) => {
    const { __scopeDialog: n, ...r } = e,
      o = dt(zh, n),
      i = ut(t, o.triggerRef);
    return y.jsx(ke.button, {
      type: "button",
      "aria-haspopup": "dialog",
      "aria-expanded": o.open,
      "aria-controls": o.contentId,
      "data-state": ru(o.open),
      ...r,
      ref: i,
      onClick: pe(e.onClick, o.onOpenToggle),
    });
  });
Mw.displayName = zh;
var tu = "DialogPortal",
  [Ow, $h] = Ah(tu, { forceMount: void 0 }),
  Uh = (e) => {
    const { __scopeDialog: t, forceMount: n, children: r, container: o } = e,
      i = dt(tu, t);
    return y.jsx(Ow, {
      scope: t,
      forceMount: n,
      children: h.Children.map(r, (s) =>
        y.jsx(Eo, {
          present: n || i.open,
          children: y.jsx(Ba, { asChild: !0, container: o, children: s }),
        }),
      ),
    });
  };
Uh.displayName = tu;
var Li = "DialogOverlay",
  Wh = h.forwardRef((e, t) => {
    const n = $h(Li, e.__scopeDialog),
      { forceMount: r = n.forceMount, ...o } = e,
      i = dt(Li, e.__scopeDialog);
    return i.modal
      ? y.jsx(Eo, {
          present: r || i.open,
          children: y.jsx(jw, { ...o, ref: t }),
        })
      : null;
  });
Wh.displayName = Li;
var jw = h.forwardRef((e, t) => {
    const { __scopeDialog: n, ...r } = e,
      o = dt(Li, n);
    return y.jsx(Ih, {
      as: yr,
      allowPinchZoom: !0,
      shards: [o.contentRef],
      children: y.jsx(ke.div, {
        "data-state": ru(o.open),
        ...r,
        ref: t,
        style: { pointerEvents: "auto", ...r.style },
      }),
    });
  }),
  Rn = "DialogContent",
  Vh = h.forwardRef((e, t) => {
    const n = $h(Rn, e.__scopeDialog),
      { forceMount: r = n.forceMount, ...o } = e,
      i = dt(Rn, e.__scopeDialog);
    return y.jsx(Eo, {
      present: r || i.open,
      children: i.modal
        ? y.jsx(Dw, { ...o, ref: t })
        : y.jsx(bw, { ...o, ref: t }),
    });
  });
Vh.displayName = Rn;
var Dw = h.forwardRef((e, t) => {
    const n = dt(Rn, e.__scopeDialog),
      r = h.useRef(null),
      o = ut(t, n.contentRef, r);
    return (
      h.useEffect(() => {
        const i = r.current;
        if (i) return Rw(i);
      }, []),
      y.jsx(Bh, {
        ...e,
        ref: o,
        trapFocus: n.open,
        disableOutsidePointerEvents: !0,
        onCloseAutoFocus: pe(e.onCloseAutoFocus, (i) => {
          var s;
          (i.preventDefault(), (s = n.triggerRef.current) == null || s.focus());
        }),
        onPointerDownOutside: pe(e.onPointerDownOutside, (i) => {
          const s = i.detail.originalEvent,
            l = s.button === 0 && s.ctrlKey === !0;
          (s.button === 2 || l) && i.preventDefault();
        }),
        onFocusOutside: pe(e.onFocusOutside, (i) => i.preventDefault()),
      })
    );
  }),
  bw = h.forwardRef((e, t) => {
    const n = dt(Rn, e.__scopeDialog),
      r = h.useRef(!1),
      o = h.useRef(!1);
    return y.jsx(Bh, {
      ...e,
      ref: t,
      trapFocus: !1,
      disableOutsidePointerEvents: !1,
      onCloseAutoFocus: (i) => {
        var s, l;
        ((s = e.onCloseAutoFocus) == null || s.call(e, i),
          i.defaultPrevented ||
            (r.current || (l = n.triggerRef.current) == null || l.focus(),
            i.preventDefault()),
          (r.current = !1),
          (o.current = !1));
      },
      onInteractOutside: (i) => {
        var a, u;
        ((a = e.onInteractOutside) == null || a.call(e, i),
          i.defaultPrevented ||
            ((r.current = !0),
            i.detail.originalEvent.type === "pointerdown" && (o.current = !0)));
        const s = i.target;
        (((u = n.triggerRef.current) == null ? void 0 : u.contains(s)) &&
          i.preventDefault(),
          i.detail.originalEvent.type === "focusin" &&
            o.current &&
            i.preventDefault());
      },
    });
  }),
  Bh = h.forwardRef((e, t) => {
    const {
        __scopeDialog: n,
        trapFocus: r,
        onOpenAutoFocus: o,
        onCloseAutoFocus: i,
        ...s
      } = e,
      l = dt(Rn, n),
      a = h.useRef(null),
      u = ut(t, a);
    return (
      W1(),
      y.jsxs(y.Fragment, {
        children: [
          y.jsx(Nh, {
            asChild: !0,
            loop: !0,
            trapped: r,
            onMountAutoFocus: o,
            onUnmountAutoFocus: i,
            children: y.jsx(Va, {
              role: "dialog",
              id: l.contentId,
              "aria-describedby": l.descriptionId,
              "aria-labelledby": l.titleId,
              "data-state": ru(l.open),
              ...s,
              ref: u,
              onDismiss: () => l.onOpenChange(!1),
            }),
          }),
          y.jsxs(y.Fragment, {
            children: [
              y.jsx(Iw, { titleId: l.titleId }),
              y.jsx(Aw, { contentRef: a, descriptionId: l.descriptionId }),
            ],
          }),
        ],
      })
    );
  }),
  nu = "DialogTitle",
  Hh = h.forwardRef((e, t) => {
    const { __scopeDialog: n, ...r } = e,
      o = dt(nu, n);
    return y.jsx(ke.h2, { id: o.titleId, ...r, ref: t });
  });
Hh.displayName = nu;
var Qh = "DialogDescription",
  Kh = h.forwardRef((e, t) => {
    const { __scopeDialog: n, ...r } = e,
      o = dt(Qh, n);
    return y.jsx(ke.p, { id: o.descriptionId, ...r, ref: t });
  });
Kh.displayName = Qh;
var Gh = "DialogClose",
  Xh = h.forwardRef((e, t) => {
    const { __scopeDialog: n, ...r } = e,
      o = dt(Gh, n);
    return y.jsx(ke.button, {
      type: "button",
      ...r,
      ref: t,
      onClick: pe(e.onClick, () => o.onOpenChange(!1)),
    });
  });
Xh.displayName = Gh;
function ru(e) {
  return e ? "open" : "closed";
}
var Yh = "DialogTitleWarning",
  [Jw, qh] = gy(Yh, { contentName: Rn, titleName: nu, docsSlug: "dialog" }),
  Iw = ({ titleId: e }) => {
    const t = qh(Yh),
      n = `\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;
    return (
      h.useEffect(() => {
        e && (document.getElementById(e) || console.error(n));
      }, [n, e]),
      null
    );
  },
  Lw = "DialogDescriptionWarning",
  Aw = ({ contentRef: e, descriptionId: t }) => {
    const r = `Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${qh(Lw).contentName}}.`;
    return (
      h.useEffect(() => {
        var i;
        const o =
          (i = e.current) == null ? void 0 : i.getAttribute("aria-describedby");
        t && o && (document.getElementById(t) || console.warn(r));
      }, [r, e, t]),
      null
    );
  },
  Fw = Fh,
  zw = Uh,
  Zh = Wh,
  Jh = Vh,
  em = Hh,
  tm = Kh,
  $w = Xh;
const nm = Fw,
  Uw = zw,
  rm = h.forwardRef(({ className: e, ...t }, n) =>
    y.jsx(Zh, {
      ref: n,
      className: ce(
        "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        e,
      ),
      ...t,
    }),
  );
rm.displayName = Zh.displayName;
const ou = h.forwardRef(({ className: e, children: t, ...n }, r) =>
  y.jsxs(Uw, {
    children: [
      y.jsx(rm, {}),
      y.jsxs(Jh, {
        ref: r,
        className: ce(
          "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
          e,
        ),
        ...n,
        children: [
          t,
          y.jsxs($w, {
            className:
              "absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
            children: [
              y.jsx(eh, { className: "h-4 w-4" }),
              y.jsx("span", { className: "sr-only", children: "Close" }),
            ],
          }),
        ],
      }),
    ],
  }),
);
ou.displayName = Jh.displayName;
const iu = ({ className: e, ...t }) =>
  y.jsx("div", {
    className: ce("flex flex-col space-y-1.5 text-center sm:text-left", e),
    ...t,
  });
iu.displayName = "DialogHeader";
const su = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(em, {
    ref: n,
    className: ce("text-lg font-semibold leading-none tracking-tight", e),
    ...t,
  }),
);
su.displayName = em.displayName;
const lu = h.forwardRef(({ className: e, ...t }, n) =>
  y.jsx(tm, {
    ref: n,
    className: ce("text-sm text-muted-foreground", e),
    ...t,
  }),
);
lu.displayName = tm.displayName;
function Ww({ open: e, onOpenChange: t, colors: n, onSave: r }) {
  const [o, i] = h.useState([...n]),
    s = h.useCallback((a, u) => {
      i((v) => {
        const f = [...v];
        return ((f[a] = u), f);
      });
    }, []),
    l = h.useCallback(() => {
      (r(o), t(!1));
    }, [o, r, t]);
  return y.jsx(nm, {
    open: e,
    onOpenChange: t,
    children: y.jsxs(ou, {
      className: "sm:max-w-md",
      children: [
        y.jsxs(iu, {
          children: [
            y.jsx(su, { children: "Edit Color Palette" }),
            y.jsx(lu, {
              children:
                "Customize your color palette. Changes will be applied after saving.",
            }),
          ],
        }),
        y.jsx("div", {
          className: "space-y-4 py-4",
          children: o.map((a, u) =>
            y.jsxs(
              "div",
              {
                className: "flex items-center gap-4",
                children: [
                  y.jsx(Ii, {
                    type: "color",
                    value: a,
                    onChange: (v) => s(u, v.target.value),
                    className: "w-16 h-8 p-0 cursor-pointer",
                  }),
                  y.jsx(Ii, {
                    type: "text",
                    value: a,
                    onChange: (v) => s(u, v.target.value),
                    className: "font-mono",
                    placeholder: "#000000",
                  }),
                  y.jsx("div", {
                    className: "w-8 h-8 rounded-md",
                    style: { backgroundColor: a },
                  }),
                ],
              },
              u,
            ),
          ),
        }),
        y.jsx("div", {
          className: "flex justify-end",
          children: y.jsx(Ke, { onClick: l, children: "Save Changes" }),
        }),
      ],
    }),
  });
}
function Vw({ onSelectColor: e, colors: t, onColorsChange: n }) {
  const [r, o] = h.useState(!1);
  return y.jsxs(y.Fragment, {
    children: [
      y.jsxs("div", {
        className: "flex items-center gap-2",
        children: [
          y.jsx("div", {
            className: "flex gap-2",
            children: t.map((i) =>
              y.jsx(
                Ke,
                {
                  className: "w-8 h-8 p-0 rounded-md",
                  style: { backgroundColor: i },
                  onClick: () => e(i),
                },
                i,
              ),
            ),
          }),
          y.jsx(Ke, {
            variant: "ghost",
            size: "icon",
            onClick: () => o(!0),
            className: "h-8 w-8",
            children: y.jsx(s0, { className: "h-4 w-4" }),
          }),
        ],
      }),
      r && y.jsx(Ww, { open: r, onOpenChange: o, colors: t, onSave: n }),
    ],
  });
}
const In = {
  servers: [],
  notes: [],
  groups: [],
  connections: [],
  gridSize: 20,
  colors: [
    "#142c4c",
    "#143524",
    "#2d1b53",
    "#4a1840",
    "#4a1818",
    "#2d3748",
    "#1a202c",
    "#000000",
  ],
  selectedTexture: "Paper Texture",
};
function Bw() {
  const [e, t] = h.useState(In);

  // Load data on component mount
  h.useEffect(() => {
    const loadInitialData = async () => {
      try {
        const response = await fetch(`api/?action=load&user_id=${window.MAP_USER_ID}`);
        const result = await response.json();
        if (result.success && result.mapData) {
          const c = result.mapData;
          t({
            ...In,
            ...c,
            connections: c.connections || [],
            notes: c.notes || [],
            colors: c.colors || In.colors,
            selectedTexture: c.selectedTexture || In.selectedTexture,
          });
        }
      } catch (error) {
        console.error('Error loading initial map data:', error);
      }
    };

    if (window.MAP_USER_ID) {
      loadInitialData();
    }
  }, []);
    n = h.useCallback((d) => {
      const c = crypto.randomUUID();
      return (
        t((p) => ({
          ...p,
          servers: [
            ...p.servers,
            { id: c, position: d, cores: 1, ram: 1, notes: "" },
          ],
        })),
        c
      );
    }, []),
    r = h.useCallback((d) => {
      const c = crypto.randomUUID();
      return (
        t((p) => ({
          ...p,
          notes: [
            ...p.notes,
            { id: c, position: d, notes: "", priority: "neutral" },
          ],
        })),
        c
      );
    }, []),
    o = h.useCallback((d) => {
      const c = crypto.randomUUID();
      return (
        t((p) => ({
          ...p,
          groups: [
            ...p.groups,
            {
              id: c,
              position: d,
              width: 600,
              height: 600,
              name: "New Group",
              color: "#4A5568",
              opacity: 0.3,
            },
          ],
        })),
        c
      );
    }, []),
    i = h.useCallback((d, c) => {
      const p = crypto.randomUUID();
      return (
        t((S) => ({
          ...S,
          connections: [
            ...S.connections,
            { id: p, sourceId: d, targetId: c, color: "#4A5568" },
          ],
        })),
        p
      );
    }, []),
    s = h.useCallback((d, c) => {
      t((p) => ({
        ...p,
        connections: p.connections.map((S) =>
          S.id === d ? { ...S, ...c } : S,
        ),
      }));
    }, []),
    l = (d, c) =>
      d.x >= c.position.x &&
      d.x <= c.position.x + c.width &&
      d.y >= c.position.y &&
      d.y <= c.position.y + c.height,
    a = h.useCallback((d, c) => {
      t((p) => {
        if (!p.servers.find((C) => C.id === d)) return p;
        if (c.position) {
          const C = p.groups.find((N) => l(c.position, N));
          c.groupId = C == null ? void 0 : C.id;
        }
        return {
          ...p,
          servers: p.servers.map((C) => (C.id === d ? { ...C, ...c } : C)),
        };
      });
    }, []),
    u = h.useCallback((d, c) => {
      t((p) => {
        if (!p.notes.find((C) => C.id === d)) return p;
        if (c.position) {
          const C = p.groups.find((N) => l(c.position, N));
          c.groupId = C == null ? void 0 : C.id;
        }
        return {
          ...p,
          notes: p.notes.map((C) => (C.id === d ? { ...C, ...c } : C)),
        };
      });
    }, []),
    v = h.useCallback((d, c) => {
      t((p) => {
        const S = p.groups.find((k) => k.id === d);
        if (!S) return p;
        const C = c.position ? c.position.x - S.position.x : 0,
          N = c.position ? c.position.y - S.position.y : 0;
        return {
          ...p,
          groups: p.groups.map((k) => (k.id === d ? { ...k, ...c } : k)),
          servers: p.servers.map((k) =>
            k.groupId === d && c.position
              ? { ...k, position: { x: k.position.x + C, y: k.position.y + N } }
              : k,
          ),
          notes: p.notes.map((k) =>
            k.groupId === d && c.position
              ? { ...k, position: { x: k.position.x + C, y: k.position.y + N } }
              : k,
          ),
        };
      });
    }, []),
    f = h.useCallback((d) => {
      t((c) => ({
        ...c,
        servers: c.servers.filter((p) => p.id !== d),
        notes: c.notes.filter((p) => p.id !== d),
        groups: c.groups.filter((p) => p.id !== d),
        connections: c.connections.filter(
          (p) => p.sourceId !== d && p.targetId !== d,
        ),
      }));
    }, []),
    m = h.useCallback(async () => {
      try {
        const response = await fetch('api/?action=save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            mapData: e,
            user_id: window.MAP_USER_ID
          })
        });
        const result = await response.json();
        if (!result.success) {
          console.error('Failed to save map data:', result.error);
        }
      } catch (error) {
        console.error('Error saving map data:', error);
      }
    }, [e]),
    x = h.useCallback(async () => {
      try {
        const response = await fetch(`api/?action=load&user_id=${window.MAP_USER_ID}`);
        const result = await response.json();
        if (result.success && result.mapData) {
          const c = result.mapData;
          t({
            ...In,
            ...c,
            connections: c.connections || [],
            notes: c.notes || [],
            colors: c.colors || In.colors,
            selectedTexture: c.selectedTexture || In.selectedTexture,
          });
        }
      } catch (error) {
        console.error('Error loading map data:', error);
      }
    }, []),
    E = h.useCallback(() => {
      t(In);
    }, []),
    g = h.useCallback((d) => {
      t((c) => ({ ...c, colors: d }));
    }, []),
    w = h.useCallback((d) => {
      t((c) => ({ ...c, selectedTexture: d }));
    }, []);
  return {
    state: e,
    addServer: n,
    addNote: r,
    addGroup: o,
    addConnection: i,
    updateServer: a,
    updateNote: u,
    updateGroup: v,
    updateConnection: s,
    updateColors: g,
    updateTexture: w,
    deleteObject: f,
    saveState: m,
    loadState: x,
    clearState: E,
  };
}
const om = [
  { name: "None", style: { backgroundColor: "#262626" } },
  {
    name: "Noise Pattern",
    style: {
      backgroundColor: "#262626",
      backgroundImage:
        "repeating-radial-gradient(circle at 0 0, transparent 0, #262626 12px), repeating-linear-gradient(#ffffff03, #ffffff03)",
    },
  },
  {
    name: "Isometric",
    style: {
      backgroundColor: "#262626",
      backgroundImage:
        "linear-gradient(30deg, #ffffff08 12%, transparent 12.5%, transparent 87%, #ffffff08 87.5%, #ffffff08), linear-gradient(150deg, #ffffff08 12%, transparent 12.5%, transparent 87%, #ffffff08 87.5%, #ffffff08), linear-gradient(30deg, #ffffff08 12%, transparent 12.5%, transparent 87%, #ffffff08 87.5%, #ffffff08), linear-gradient(150deg, #ffffff08 12%, transparent 12.5%, transparent 87%, #ffffff08 87.5%, #ffffff08), linear-gradient(60deg, #ffffff04 25%, transparent 25.5%, transparent 75%, #ffffff04 75%, #ffffff04), linear-gradient(60deg, #ffffff04 25%, transparent 25.5%, transparent 75%, #ffffff04 75%, #ffffff04)",
      backgroundSize: "8px 14px",
      backgroundPosition: "0 0, 0 0, 4px 7px, 4px 7px, 0 0, 4px 7px",
    },
  },
  {
    name: "Triangle Pattern",
    style: {
      backgroundColor: "#262626",
      backgroundImage:
        "linear-gradient(-45deg, #262626, #262626 50%, #ffffff08 50%, #ffffff08)",
      backgroundSize: "4px 4px",
    },
  },
  {
    name: "Diagonal Lines",
    style: {
      backgroundColor: "#262626",
      backgroundImage:
        "repeating-linear-gradient(45deg, #ffffff08 0, #ffffff08 0.4px, #262626 0, #262626 50%)",
      backgroundSize: "4px 4px",
    },
  },
];
function Hw({
  open: e,
  onOpenChange: t,
  selectedTexture: n,
  onTextureChange: r,
}) {
  return y.jsx(nm, {
    open: e,
    onOpenChange: t,
    children: y.jsxs(ou, {
      className: "sm:max-w-md",
      children: [
        y.jsxs(iu, {
          children: [
            y.jsx(su, { children: "Background Texture Settings" }),
            y.jsx(lu, {
              children:
                "Choose a texture pattern for all objects in the canvas.",
            }),
          ],
        }),
        y.jsx("div", {
          className: "grid grid-cols-2 gap-4 py-4",
          children: om.map((o) =>
            y.jsxs(
              "div",
              {
                className: "flex flex-col items-center gap-2",
                children: [
                  y.jsx("div", {
                    className:
                      "relative w-full h-24 rounded-lg cursor-pointer transition-all duration-150 hover:scale-105",
                    style: o.style,
                    onClick: () => r(o.name),
                    children:
                      n === o.name &&
                      y.jsx("div", {
                        className:
                          "absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg",
                        children: y.jsx(r0, {
                          className: "w-6 h-6 text-white",
                        }),
                      }),
                  }),
                  y.jsx("span", {
                    className: "text-sm font-mono",
                    children: o.name,
                  }),
                ],
              },
              o.name,
            ),
          ),
        }),
      ],
    }),
  });
}
const Qw = om.reduce((e, t) => ({ ...e, [t.name]: t.style }), {});
function Kw() {
  const e = h.useRef(null),
    [t, n] = h.useState(null),
    [r, o] = h.useState(null),
    [i, s] = h.useState(!1),
    [l, a] = h.useState(null),
    [u, v] = h.useState(!1),
    {
      state: f,
      addServer: m,
      addNote: x,
      addGroup: E,
      addConnection: g,
      updateServer: w,
      updateNote: d,
      updateGroup: c,
      updateConnection: p,
      updateColors: S,
      deleteObject: C,
      saveState: N,
      loadState: k,
      clearState: T,
      updateTexture: b,
    } = Bw(),
    { snapToGrid: D } = Ch(f.gridSize);
  h.useEffect(() => {
    const _ = (I) => {
      var ee, re;
      ((ee = document.activeElement) == null ? void 0 : ee.tagName) ===
        "INPUT" ||
        ((re = document.activeElement) == null ? void 0 : re.tagName) ===
          "TEXTAREA" ||
        (I.key === "Escape"
          ? (o(null), s(!1), a(null))
          : I.key === "Delete" && r && (C(r), o(null)));
    };
    return (
      window.addEventListener("keydown", _),
      () => window.removeEventListener("keydown", _)
    );
  }, [r, C]);
  const H = (_, I) => {
      if (!e.current) return;
      if (i) {
        l && I !== l && (g(l, I), s(!1), a(null));
        return;
      }
      const ee = e.current.getBoundingClientRect(),
        re = [...f.servers, ...f.notes, ...f.groups].find((He) => He.id === I);
      if (!re) return;
      o(I);
      const St = _.clientX - ee.left - re.position.x,
        de = _.clientY - ee.top - re.position.y;
      (n({ id: I, offsetX: St, offsetY: de }), _.stopPropagation());
    },
    z = () => {
      [...f.servers, ...f.notes].find((I) => I.id === r) && (s(!0), a(r));
    },
    Pe = (_) => {
      _.target === e.current && (o(null), s(!1), a(null));
    },
    L = (_) => {
      if (!t || !e.current) return;
      const I = e.current.getBoundingClientRect(),
        ee = D({
          x: _.clientX - I.left - t.offsetX,
          y: _.clientY - I.top - t.offsetY,
        });
      if (f.servers.find((He) => He.id === t.id)) {
        w(t.id, { position: ee });
        return;
      }
      if (f.notes.find((He) => He.id === t.id)) {
        d(t.id, { position: ee });
        return;
      }
      f.groups.find((He) => He.id === t.id) && c(t.id, { position: ee });
    },
    ie = () => {
      n(null);
    },
    le = () => {
      const _ = m(D({ x: 100, y: 100 }));
      o(_);
    },
    je = () => {
      const _ = x(D({ x: 100, y: 100 }));
      o(_);
    },
    M = () => {
      const _ = E(D({ x: 50, y: 50 }));
      o(_);
    },
    j = (_) => {
      if (!r) return;
      if (f.servers.find((de) => de.id === r)) {
        w(r, { color: _ });
        return;
      }
      if (f.notes.find((de) => de.id === r)) {
        d(r, { color: _ });
        return;
      }
      if (f.groups.find((de) => de.id === r)) {
        c(r, { color: _ });
        return;
      }
      f.connections.find((de) => de.id === r) && p(r, { color: _ });
    },
    A = (_) => {
      (S(_), N());
    };
  return (
    h.useEffect(() => {
      const _ = document.createElement("style"),
        I = Qw[f.selectedTexture];
      if (I) {
        const ee = Object.entries(I).map(
          ([re, St]) =>
            `${re.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${St};`,
        ).join(`
`);
        ((_.textContent = `
        .bg-card {
          ${ee}
        }
      `),
          document.head.appendChild(_));
      }
      return () => {
        _.parentNode && _.parentNode.removeChild(_);
      };
    }, [f.selectedTexture]),
    y.jsxs("div", {
      className: "flex flex-col h-screen bg-gray-950",
      children: [
        y.jsxs("div", {
          className: "flex items-center gap-4 p-4 bg-gray-900",
          children: [
            y.jsxs("div", {
              className: "flex gap-2",
              children: [
                y.jsxs(Ke, {
                  onClick: M,
                  children: [
                    y.jsx(l0, { className: "h-4 w-4 mr-2" }),
                    "Add Group",
                  ],
                }),
                y.jsxs(Ke, {
                  onClick: le,
                  children: [
                    y.jsx(i0, { className: "h-4 w-4 mr-2" }),
                    "Add Server",
                  ],
                }),
                y.jsxs(Ke, {
                  onClick: je,
                  children: [
                    y.jsx(a0, { className: "h-4 w-4 mr-2" }),
                    "Add Note",
                  ],
                }),
                y.jsxs(Ke, {
                  onClick: z,
                  disabled:
                    !r || ![...f.servers, ...f.notes].some((_) => _.id === r),
                  variant: i ? "secondary" : "default",
                  children: [
                    y.jsx(o0, { className: "h-4 w-4 mr-2" }),
                    i ? "Select Target" : "Connect",
                  ],
                }),
              ],
            }),
            y.jsx("div", { className: "h-full w-px bg-gray-700" }),
            y.jsx(Vw, {
              colors: f.colors,
              onSelectColor: j,
              onColorsChange: A,
            }),
            y.jsx("div", { className: "h-full w-px bg-gray-700" }),
            y.jsx(Ke, {
              variant: "outline",
              size: "sm",
              onClick: () => v(!0),
              className: "h-8",
              children: "Pattern",
            }),
            y.jsx("div", { className: "h-full w-px bg-gray-700" }),
            y.jsxs("div", {
              className: "flex gap-2",
              children: [
                y.jsx(Ke, { onClick: N, children: "Save" }),
                y.jsx(Ke, { onClick: k, children: "Load" }),
                y.jsx(Ke, {
                  variant: "destructive",
                  size: "icon",
                  onClick: T,
                  children: y.jsx(u0, { className: "h-4 w-4" }),
                }),
              ],
            }),
          ],
        }),
        y.jsxs("div", {
          ref: e,
          className: `relative flex-1 overflow-auto ${i ? "cursor-crosshair" : ""}`,
          onMouseMove: L,
          onMouseUp: ie,
          onMouseLeave: ie,
          onClick: Pe,
          children: [
            y.jsx(k1, { width: 3e3, height: 2e3, gridSize: f.gridSize }),
            f.connections.map((_) =>
              y.jsx(
                Ph,
                {
                  connection: _,
                  servers: [...f.servers, ...f.notes],
                  isSelected: r === _.id,
                  onUpdate: p,
                },
                _.id,
              ),
            ),
            f.groups.map((_) =>
              y.jsx(
                O1,
                {
                  group: _,
                  onUpdate: c,
                  isSelected: r === _.id,
                  onMouseDown: (I) => H(I, _.id),
                },
                _.id,
              ),
            ),
            f.servers.map((_) =>
              y.jsx(
                "div",
                {
                  onMouseDown: (I) => H(I, _.id),
                  className: "absolute",
                  style: { zIndex: 2 },
                  children: y.jsx(M1, {
                    server: _,
                    onUpdate: w,
                    isSelected: r === _.id,
                  }),
                },
                _.id,
              ),
            ),
            f.notes.map((_) =>
              y.jsx(
                "div",
                {
                  onMouseDown: (I) => H(I, _.id),
                  className: "absolute",
                  style: { zIndex: 2 },
                  children: y.jsx(kh, {
                    note: _,
                    onUpdate: d,
                    isSelected: r === _.id,
                  }),
                },
                _.id,
              ),
            ),
          ],
        }),
        y.jsx(Hw, {
          open: u,
          onOpenChange: v,
          selectedTexture: f.selectedTexture,
          onTextureChange: (_) => {
            (b(_), N());
          },
        }),
      ],
    })
  );
}
function Gw() {
  return y.jsx("div", {
    className: "h-screen w-screen bg-gray-950 text-gray-50",
    children: y.jsx(Kw, {}),
  });
}
function Xw() {
  return y.jsx(C1, { children: y.jsx(E1, { path: "/", component: Gw }) });
}
gp(document.getElementById("root")).render(
  y.jsx(h.StrictMode, {
    children: y.jsxs(ty, {
      client: ny,
      children: [y.jsx(Xw, {}), y.jsx(Q0, {})],
    }),
  }),
);
