<?php
// User access page - allows access via URL like: user.php?code=abcd
require_once 'database.php';

$userCode = '';
$error = '';
$mapExists = false;

if (isset($_GET['code'])) {
    $userCode = trim($_GET['code']);
    
    // Validate user code format
    if (preg_match('/^[a-z]{4}$/', $userCode)) {
        try {
            $db = new MapDatabase();
            $mapExists = $db->userExists($userCode);
            
            if ($mapExists) {
                // Set cookie and redirect to main app
                setcookie('map_user_id', $userCode, time() + (86400 * 365), '/');
                header('Location: index.php');
                exit;
            } else {
                $error = "No map found for user code: " . htmlspecialchars($userCode);
            }
        } catch (Exception $e) {
            $error = "Database error occurred.";
        }
    } else {
        $error = "Invalid user code format. Please use 4 lowercase letters.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Your Map - Map Augment</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            font-family: monospace;
            text-transform: lowercase;
            letter-spacing: 2px;
            text-align: center;
            box-sizing: border-box;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        .error {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #fcc;
        }
        .info {
            color: #666;
            font-size: 14px;
            margin-top: 20px;
            line-height: 1.5;
        }
        .create-new {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .create-new a {
            color: #667eea;
            text-decoration: none;
        }
        .create-new a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Access Your Map</h1>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="GET" action="user.php">
            <div class="form-group">
                <label for="code">Enter your 4-character user code:</label>
                <input 
                    type="text" 
                    id="code" 
                    name="code" 
                    maxlength="4" 
                    pattern="[a-z]{4}" 
                    value="<?php echo htmlspecialchars($userCode); ?>"
                    placeholder="abcd"
                    required
                >
            </div>
            <button type="submit">Access My Map</button>
        </form>
        
        <div class="info">
            Your user code is a unique 4-letter identifier (e.g., "abcd") that was assigned when you first created your map.
        </div>
        
        <div class="create-new">
            <a href="index.php">Create a new map instead</a>
        </div>
    </div>
    
    <script>
        // Auto-convert to lowercase and limit to 4 characters
        document.getElementById('code').addEventListener('input', function(e) {
            e.target.value = e.target.value.toLowerCase().replace(/[^a-z]/g, '').substring(0, 4);
        });
    </script>
</body>
</html>
