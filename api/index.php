<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database.php';

try {
    $db = new MapDatabase();
    
    // Get user ID from cookie or request
    $userId = '';
    if (isset($_COOKIE['map_user_id'])) {
        $userId = $_COOKIE['map_user_id'];
    } elseif (isset($_POST['user_id'])) {
        $userId = $_POST['user_id'];
    } elseif (isset($_GET['user_id'])) {
        $userId = $_GET['user_id'];
    }
    
    // Validate user ID format
    if (!preg_match('/^[a-z]{4}$/', $userId)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid user ID format']);
        exit;
    }
    
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'save':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                exit;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input || !isset($input['mapData'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid map data']);
                exit;
            }
            
            $mapData = json_encode($input['mapData']);
            $success = $db->saveMapData($userId, $mapData);
            
            if ($success) {
                echo json_encode(['success' => true, 'message' => 'Map saved successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to save map data']);
            }
            break;
            
        case 'load':
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                exit;
            }
            
            $mapData = $db->loadMapData($userId);
            
            if ($mapData !== null) {
                $decodedData = json_decode($mapData, true);
                echo json_encode(['success' => true, 'mapData' => $decodedData]);
            } else {
                echo json_encode(['success' => true, 'mapData' => null]);
            }
            break;
            
        case 'check':
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                exit;
            }
            
            $exists = $db->userExists($userId);
            echo json_encode(['success' => true, 'exists' => $exists]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
?>
